[{"model": "authentication.submoduleaccess", "pk": 1, "fields": {"submodule": 1, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 2, "fields": {"submodule": 1, "role": "R002", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 3, "fields": {"submodule": 1, "role": "R003", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 4, "fields": {"submodule": 1, "role": "R004", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 5, "fields": {"submodule": 1, "role": "R005", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 6, "fields": {"submodule": 2, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 7, "fields": {"submodule": 2, "role": "R002", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 8, "fields": {"submodule": 2, "role": "R003", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 9, "fields": {"submodule": 2, "role": "R004", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 10, "fields": {"submodule": 2, "role": "R005", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 11, "fields": {"submodule": 3, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 12, "fields": {"submodule": 3, "role": "R002", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 13, "fields": {"submodule": 3, "role": "R003", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 14, "fields": {"submodule": 3, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 15, "fields": {"submodule": 3, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 16, "fields": {"submodule": 4, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 17, "fields": {"submodule": 4, "role": "R002", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 18, "fields": {"submodule": 4, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 19, "fields": {"submodule": 4, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 20, "fields": {"submodule": 4, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 21, "fields": {"submodule": 5, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 22, "fields": {"submodule": 5, "role": "R002", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 23, "fields": {"submodule": 5, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 24, "fields": {"submodule": 5, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 25, "fields": {"submodule": 5, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 26, "fields": {"submodule": 6, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 27, "fields": {"submodule": 6, "role": "R002", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 28, "fields": {"submodule": 6, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 29, "fields": {"submodule": 6, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 30, "fields": {"submodule": 6, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 31, "fields": {"submodule": 7, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 32, "fields": {"submodule": 7, "role": "R002", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 33, "fields": {"submodule": 7, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 34, "fields": {"submodule": 7, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 35, "fields": {"submodule": 7, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 36, "fields": {"submodule": 8, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 37, "fields": {"submodule": 8, "role": "R002", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 38, "fields": {"submodule": 8, "role": "R003", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 39, "fields": {"submodule": 8, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 40, "fields": {"submodule": 8, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 41, "fields": {"submodule": 9, "role": "R001", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 42, "fields": {"submodule": 9, "role": "R002", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 43, "fields": {"submodule": 9, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 44, "fields": {"submodule": 9, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 45, "fields": {"submodule": 9, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 46, "fields": {"submodule": 10, "role": "R001", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 47, "fields": {"submodule": 10, "role": "R002", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 48, "fields": {"submodule": 10, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 49, "fields": {"submodule": 10, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 50, "fields": {"submodule": 10, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 51, "fields": {"submodule": 11, "role": "R001", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 52, "fields": {"submodule": 11, "role": "R002", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 53, "fields": {"submodule": 11, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 54, "fields": {"submodule": 11, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 55, "fields": {"submodule": 11, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 56, "fields": {"submodule": 12, "role": "R001", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 57, "fields": {"submodule": 12, "role": "R002", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 58, "fields": {"submodule": 12, "role": "R003", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 59, "fields": {"submodule": 12, "role": "R004", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 60, "fields": {"submodule": 12, "role": "R005", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 61, "fields": {"submodule": 1, "role": "R006", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 62, "fields": {"submodule": 2, "role": "R006", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 63, "fields": {"submodule": 3, "role": "R006", "has_access": true, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 64, "fields": {"submodule": 4, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 65, "fields": {"submodule": 5, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 66, "fields": {"submodule": 6, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 67, "fields": {"submodule": 7, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 68, "fields": {"submodule": 8, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 69, "fields": {"submodule": 9, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 70, "fields": {"submodule": 10, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 71, "fields": {"submodule": 11, "role": "R006", "has_access": false, "is_deleted": false}}, {"model": "authentication.submoduleaccess", "pk": 72, "fields": {"submodule": 12, "role": "R006", "has_access": false, "is_deleted": false}}]