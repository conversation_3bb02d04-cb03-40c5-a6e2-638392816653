[{"model": "ticket_management.status", "pk": 1, "fields": {"name": "Open", "description": "Ticket is open"}}, {"model": "ticket_management.status", "pk": 2, "fields": {"name": "In Progress", "description": "Ticket is being worked on"}}, {"model": "ticket_management.status", "pk": 3, "fields": {"name": "New", "description": "New ticket"}}, {"model": "ticket_management.status", "pk": 4, "fields": {"name": "Under Observer", "description": "Ticket is under observation"}}, {"model": "ticket_management.status", "pk": 5, "fields": {"name": "Pending", "description": "Ticket is pending"}}, {"model": "ticket_management.status", "pk": 6, "fields": {"name": "Solved", "description": "Ticket is solved"}}, {"model": "ticket_management.status", "pk": 7, "fields": {"name": "Closed", "description": "Ticket is closed"}}, {"model": "ticket_management.status", "pk": 8, "fields": {"name": "Awaiting <PERSON><PERSON><PERSON><PERSON>", "description": null}}, {"model": "ticket_management.status", "pk": 9, "fields": {"name": "Awaiting User Response", "description": null}}, {"model": "ticket_management.status", "pk": 10, "fields": {"name": "Cancel", "description": "Ticket is rejected"}}, {"model": "ticket_management.status", "pk": 11, "fields": {"name": "Awaiting Payment", "description": "Ticket is awaiting payment"}}, {"model": "ticket_management.status", "pk": 12, "fields": {"name": "Requests For Deployment", "description": "Ticket is requested for deployment"}}]