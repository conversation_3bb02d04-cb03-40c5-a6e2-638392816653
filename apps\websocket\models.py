from django.db import models
from apps.user_management.models import User

class TicketChatMessage(models.Model):
    """
    Model to store WebSocket chat messages related to tickets.
    """
    ticket = models.ForeignKey("ticket_management.Ticket", on_delete=models.CASCADE, related_name="chat_messages")
    sender = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name="sender_by")
    message = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    attachment = models.ForeignKey("ticket_management.TicketChatAttachenment", on_delete=models.SET_NULL, null=True, blank=True, related_name="message_attachments")

    def __str__(self):
        return f"Message from {self.sender} on Ticket {self.ticket.ticket_id}"
