# apps/ticket_management/management/commands/auto_close_tickets.py
from django.conf import settings
from django.core.management.base import BaseCommand
from datetime import timedelta
from django.utils.timezone import now, make_aware
from apps.ticket_management.models import Ticket, Status, StatusTracking, FeedbackValue, Feedback
from django.core.mail import send_mail
from apps.user_management.models import User

class Command(BaseCommand):
    help = "Auto-close solved tickets that haven't been updated in a while."

    def handle(self, *args, **options):
        solved_status = Status.objects.filter(name='solved').first()
        closed_status = Status.objects.filter(name='closed').first()
        if not solved_status or not closed_status:
            self.stdout.write("Error: 'solved' or 'closed' status not found.")
            return

        threshold_time = now() - timedelta(hours=72)
        if threshold_time.tzinfo is None:
            threshold_time = make_aware(threshold_time)

        tickets_to_update = Ticket.objects.filter(
            status=solved_status,
            ticket_id__in=StatusTracking.objects.filter(
                current_status=solved_status,
                updated_at__lte=threshold_time
            ).values('ticket_id')
        )

        feedback_value_5 = FeedbackValue.objects.filter(value_id=5).first()
        if not feedback_value_5:
            self.stdout.write("Error: No FeedbackValue found with value_id=5.")
            return

        updated_count = 0
        closed_tickets = []

        for ticket in tickets_to_update:
            # Update the ticket status
            ticket.status = closed_status
            ticket.closed_at = now()
            ticket.save()

            # Create a new status tracking record for the closure
            StatusTracking.objects.create(
                ticket=ticket,
                current_status=closed_status,
                updated_by=ticket.created_by,
                created_by=ticket.created_by,
                created_at=now(),
                updated_at=now(),
            )

            # Create a feedback record for the auto-closure
            Feedback.objects.create(
                ticket=ticket,
                user=ticket.created_by,
                feedback_value=feedback_value_5,
                reason="Auto Closed the Ticket",
            )

            # Send email notification to the ticket creator if email exists
            if ticket.created_by and getattr(ticket.created_by, 'email', None):
                subject = f"Ticket #{ticket.ticket_id} Closed Automatically"
                message = (
                    f"Hi {ticket.created_by.first_name} {ticket.created_by.last_name},\n\n"
                    f"This is to inform you that your support ticket (ID: {ticket.ticket_id}) has been automatically closed.\n\n"
                    f"---\n"
                    f"Ticket Details:\n"
                    f"• Ticket ID: {ticket.ticket_id}\n"
                    f"• Subject: {ticket.title}\n"
                    f"• Created_by: {ticket.created_by}\n"
                    f"• Status: Closed (Auto)\n\n"
                    f"However, if you're still facing the issue, please raise a new one ticket.\n\n"
                    f"---\n\n"
                    "Thank you."
                )
                from_email = settings.EMAIL_HOST_USER  # Update with your sending email address
                recipient_list = [ticket.created_by.email]
                try:
                    send_mail(subject, message, from_email, recipient_list, fail_silently=False)
                except Exception as e:
                    self.stdout.write(f"Error sending email for ticket {ticket.ticket_id}: {e}")
                
                if hasattr(ticket, 'watchers') and isinstance(ticket.watchers, list):
                    from django.contrib.auth import get_user_model
                    User = get_user_model()
                    watcher_users = User.objects.filter(id__in=ticket.watchers)
                    watcher_emails = [
                        watcher.email for watcher in watcher_users
                        if getattr(watcher, 'email', None)
                        ]
                    recipient_list.extend(watcher_emails)
                    
                # Remove duplicate emails
                recipient_list = list(set(recipient_list))
                
                try:
                    send_mail(subject, message, from_email, recipient_list, fail_silently=False)
                except Exception as e:
                    self.stdout.write(f"Error sending email for ticket {ticket.ticket_id}: {e}")

            updated_count += 1

        self.stdout.write(f"Updated {updated_count} tickets from 'solved' to 'closed'.")