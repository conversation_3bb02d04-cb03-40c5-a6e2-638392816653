# Generated by Django 5.1.2 on 2025-02-01 11:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0001_initial'),
        ('user_management', '0002_remove_user_reporting_to'),
    ]

    operations = [
        migrations.CreateModel(
            name='SubModule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sub_module_name', models.CharField(max_length=50, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('module_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submodules', to='authentication.module')),
            ],
        ),
        migrations.CreateModel(
            name='SubModuleAccess',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('has_access', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='submodulepermissions_created', to='user_management.user')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_permisions', to='user_management.role')),
                ('submodule_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submodule_permissions', to='authentication.submodule')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='submodulepremissions_updated', to='user_management.user')),
            ],
        ),
    ]
