from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0018_add_missing_columns'),
    ]

    operations = [
        migrations.RunSQL(
            sql="""
            -- Check if last_used_reset_token column exists, add if it doesn't
            SET @column_exists = (
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = 'user_management_user'
                AND column_name = 'last_used_reset_token'
                AND table_schema = DATABASE()
            );
            
            SET @sql = IF(@column_exists = 0, 
                'ALTER TABLE user_management_user ADD COLUMN last_used_reset_token VARCHAR(150) NULL',
                'SELECT "Column last_used_reset_token already exists"');
            
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            """,
            reverse_sql="""
            -- No reverse operation needed
            """
        ),
    ]
