#!/bin/bash

# Environment configuration script for ticketing system backend
# This script generates environment-specific configuration files

set -e  # Exit immediately if a command exits with a non-zero status

# Define colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Get the environment from the first argument or default to development
ENVIRONMENT=${1:-development}
echo -e "${YELLOW}Generating environment configuration for: ${ENVIRONMENT}${NC}"

SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(50))")

# Create .env file based on environment
if [ "$ENVIRONMENT" = "production" ]; then
    echo -e "${YELLOW}Creating production environment file...${NC}"
    cat > .env << EOL
SECRET_KEY=$SECRET_KEY
DEBUG=False
DJANGO_SETTINGS_MODULE=ticketing_system.settings
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=nex_ticket_db
DATABASE_USER=nex-ticketing
DATABASE_PASSWORD=nHH9Ky@RHgTDV
DATABASE_HOST=***************
DATABASE_PORT=3306
ALLOWED_HOSTS=ticket.nexware-global.com,localhost,127.0.0.1
BACKEND_URL=https://ticket.nexware-global.com:9048/
EMAIL_HOST=smtp.zeptomail.in
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=PHtE6r0JELzu2Gcpo0JWtKewRcCiYIss/epmeAEU4dlHC/8ETk1Sqtkjlme1rRguB/ATF/WewIw9tr/OtL/WLDu8MWpOXGqyqK3sx/VYSPOZsbq6x00asV4dd0LVU4bqdtdr1CDTvdjZNA==
CORS_ALLOWED_ORIGINS=https://ticket.nexware-global.com:9048,http://localhost:3000
CSRF_TRUSTED_ORIGINS=https://ticket.nexware-global.com:9049,https://ticket.nexware-global.com:8001
EOL

elif [ "$ENVIRONMENT" = "staging" ]; then
    echo -e "${YELLOW}Creating staging environment file...${NC}"
    cat > .env << EOL
SECRET_KEY=$SECRET_KEY
DEBUG=False
DJANGO_SETTINGS_MODULE=ticketing_system.settings
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=nex_ticket_stg_db
DATABASE_USER=nex-ticketing-stg
DATABASE_PASSWORD=RsRtW8u96@N
DATABASE_HOST=***************
DATABASE_PORT=3306'
OPTIONS={'charset': 'utf8mb4'}
ALLOWED_HOSTS=internal-project.nexware-global.com,localhost,127.0.0.1,***************
BACKEND_URL=http://internal-project.nexware-global.com:9019/
EMAIL_HOST=smtp.zeptomail.in
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=PHtE6r0JELzu2Gcpo0JWtKewRcCiYIss/epmeAEU4dlHC/8ETk1Sqtkjlme1rRguB/ATF/WewIw9tr/OtL/WLDu8MWpOXGqyqK3sx/VYSPOZsbq6x00asV4dd0LVU4bqdtdr1CDTvdjZNA==
CORS_ALLOWED_ORIGINS=http://internal-project.nexware-global.com:9018,http://localhost:3000
CSRF_TRUSTED_ORIGINS=http://internal-project.nexware-global.com:9019,ws://localhost:8000
EOL

else
    echo -e "${YELLOW}Creating development environment file...${NC}"
    cat > .env << EOL
SECRET_KEY=$SECRET_KEY
DEBUG=True
DJANGO_SETTINGS_MODULE=ticketing_system.settings
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=ticketing_tool
DATABASE_USER=root
DATABASE_PASSWORD=admin@123
DATABASE_HOST=localhost
DATABASE_PORT=3306
ALLOWED_HOSTS=localhost,127.0.0.1
BACKEND_URL=http://localhost:3000/
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://localhost:3000
CSRF_TRUSTED_ORIGINS=http://localhost:8000
EOL
fi

# Create media directories if they don't exist and set permissions
echo -e "${YELLOW}Checking media directories...${NC}"
if [ ! -d "media" ]; then
    echo -e "${YELLOW}Creating media directories...${NC}"
    mkdir -p media/uploads/profilepic
    mkdir -p media/ticket_docs

    # Set proper permissions for media directories
    echo -e "${YELLOW}Setting media directory permissions...${NC}"
    chmod -R 755 media

    # If running in a server environment, set proper ownership
    if [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "staging" ]; then
        echo -e "${YELLOW}Setting media directory ownership for server environment...${NC}"
        # Use the web server user (usually www-data, nginx, or apache)
        # Uncomment and modify the line below based on your server setup
        # chown -R www-data:www-data media
    fi
else
    echo -e "${YELLOW}Media directories already exist, checking permissions...${NC}"
    # Update permissions for existing media directories
    chmod -R 755 media

    # If running in a server environment, set proper ownership
    if [ "$ENVIRONMENT" = "production" ] || [ "$ENVIRONMENT" = "staging" ]; then
        echo -e "${YELLOW}Setting media directory ownership for server environment...${NC}"
        # Use the web server user (usually www-data, nginx, or apache)
        # Uncomment and modify the line below based on your server setup
        # chown -R www-data:www-data media
    fi
fi

# Update README.md with environment information
echo -e "${YELLOW}Updating README.md with environment information...${NC}"
README_PATH="README.md"
TEMP_README="/tmp/readme_temp.md"

# Create a temporary file with the header if README doesn't exist
if [ ! -f "$README_PATH" ]; then
    echo -e "${YELLOW}Creating new README.md file...${NC}"
    echo "# Ticketing System Backend" > "$TEMP_README"
    echo "" >> "$TEMP_README"
    echo "This is the backend for the Ticketing System application." >> "$TEMP_README"
    echo "" >> "$TEMP_README"
    echo "## Environment Setup" >> "$TEMP_README"
    echo "" >> "$TEMP_README"
    echo "Environment: $ENVIRONMENT" >> "$TEMP_README"
    echo "Setup Date: $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
    echo "Setup By: $(whoami)" >> "$TEMP_README"
    echo "" >> "$TEMP_README"
    echo "## Deployment History" >> "$TEMP_README"
    echo "" >> "$TEMP_README"
else
    # Extract existing content up to the Environment Setup section
    echo -e "${YELLOW}Updating existing README.md file...${NC}"
    awk '/^## Environment Setup/{exit} {print}' "$README_PATH" > "$TEMP_README"
    echo "## Environment Setup" >> "$TEMP_README"
    echo "" >> "$TEMP_README"
    echo "Environment: $ENVIRONMENT" >> "$TEMP_README"
    echo "Setup Date: $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
    echo "Setup By: $(whoami)" >> "$TEMP_README"
    echo "" >> "$TEMP_README"

    # Check if Deployment History section exists and append it
    if grep -q "^## Deployment History" "$README_PATH"; then
        echo "## Deployment History" >> "$TEMP_README"
        echo "" >> "$TEMP_README"
        awk '/^## Deployment History/{flag=1; next} /^##/{flag=0} flag' "$README_PATH" >> "$TEMP_README"
    else
        echo "## Deployment History" >> "$TEMP_README"
        echo "" >> "$TEMP_README"
    fi
fi

# Replace the original README with the new one
mv "$TEMP_README" "$README_PATH"

echo -e "${GREEN}Environment configuration for ${ENVIRONMENT} completed successfully!${NC}"
