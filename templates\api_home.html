<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎫 Nex Ticketing System API</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --dark-color: #34495e;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .hero-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 2rem 0;
            padding: 3rem;
        }
        
        .api-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            overflow: hidden;
        }
        
        .api-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .status-running {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
        }
        
        .btn-custom {
            border-radius: 50px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, var(--secondary-color), #5dade2);
            color: white;
        }
        
        .btn-primary-custom:hover {
            background: linear-gradient(45deg, #2980b9, var(--secondary-color));
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }
        
        .btn-success-custom {
            background: linear-gradient(45deg, var(--success-color), #2ecc71);
            color: white;
        }
        
        .btn-success-custom:hover {
            background: linear-gradient(45deg, #229954, var(--success-color));
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }
        
        .environment-indicator {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .env-local { background: #f8f9fa; color: #495057; }
        .env-staging { background: #fff3cd; color: #856404; }
        .env-production { background: #d1ecf1; color: #0c5460; }
        
        .endpoint-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
        }
        
        .endpoint-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .endpoint-item:last-child {
            border-bottom: none;
        }
        
        .method-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .method-get { background: #d4edda; color: #155724; }
        .method-post { background: #cce5ff; color: #004085; }
        .method-put { background: #fff3cd; color: #856404; }
        .method-delete { background: #f8d7da; color: #721c24; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--secondary-color);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            margin-top: 3rem;
            text-align: center;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <!-- Environment Indicator -->
                <div class="environment-indicator env-{{ environment }}">
                    <i class="fas fa-server"></i> {{ environment|title }} Environment
                </div>
                
                <!-- Hero Section -->
                <div class="hero-section position-relative">
                    <div class="text-center mb-4">
                        <h1 class="display-4 fw-bold text-primary mb-3">
                            🎫 Nex Ticketing System API
                        </h1>
                        <p class="lead text-muted mb-4">
                            Comprehensive IT Support & Ticket Management API
                        </p>
                        <div class="status-badge status-running pulse">
                            <i class="fas fa-check-circle"></i> API Running
                        </div>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">{{ features|length }}</div>
                            <div class="text-muted">Features</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ api_endpoints|length }}</div>
                            <div class="text-muted">API Endpoints</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">v{{ version }}</div>
                            <div class="text-muted">Version</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">
                                <i class="fas fa-shield-alt text-success"></i>
                            </div>
                            <div class="text-muted">JWT Secured</div>
                        </div>
                    </div>
                    
                    <!-- Documentation Links -->
                    <div class="row g-4 mb-4">
                        <div class="col-md-6">
                            <div class="api-card h-100 p-4">
                                <div class="text-center">
                                    <div class="feature-icon">
                                        <i class="fas fa-book-open"></i>
                                    </div>
                                    <h4>📖 Interactive API Docs</h4>
                                    <p class="text-muted mb-4">
                                        Explore and test all API endpoints with our interactive Swagger documentation.
                                    </p>
                                    <a href="{{ documentation.swagger_ui }}" class="btn btn-primary-custom btn-custom" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> Open Swagger UI
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="api-card h-100 p-4">
                                <div class="text-center">
                                    <div class="feature-icon">
                                        <i class="fas fa-file-alt"></i>
                                    </div>
                                    <h4>📚 ReDoc Documentation</h4>
                                    <p class="text-muted mb-4">
                                        Beautiful, responsive API documentation with detailed schemas and examples.
                                    </p>
                                    <a href="{{ documentation.redoc }}" class="btn btn-success-custom btn-custom" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> Open ReDoc
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- API Features -->
                <div class="row g-4 mb-4">
                    <div class="col-12">
                        <div class="api-card p-4">
                            <h3 class="mb-4">🚀 API Features</h3>
                            <div class="row g-3">
                                {% for feature in features %}
                                <div class="col-md-6 col-lg-4">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        <span>{{ feature }}</span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- API Endpoints -->
                <div class="row g-4 mb-4">
                    <div class="col-md-6">
                        <div class="api-card p-4 h-100">
                            <h4 class="mb-3">🔐 Authentication</h4>
                            <div class="endpoint-list">
                                <div class="endpoint-item">
                                    <div>
                                        <span class="method-badge method-post">POST</span>
                                        <span class="ms-2">Login</span>
                                    </div>
                                    <code>{{ authentication.login }}</code>
                                </div>
                                <div class="endpoint-item">
                                    <div>
                                        <span class="method-badge method-post">POST</span>
                                        <span class="ms-2">Token Refresh</span>
                                    </div>
                                    <code>{{ authentication.token_refresh }}</code>
                                </div>
                                <div class="endpoint-item">
                                    <div>
                                        <span class="method-badge method-post">POST</span>
                                        <span class="ms-2">Logout</span>
                                    </div>
                                    <code>{{ authentication.logout }}</code>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="api-card p-4 h-100">
                            <h4 class="mb-3">📋 Main Endpoints</h4>
                            <div class="endpoint-list">
                                <div class="endpoint-item">
                                    <div>
                                        <span class="method-badge method-get">GET</span>
                                        <span class="ms-2">Tickets</span>
                                    </div>
                                    <code>{{ api_endpoints.tickets }}</code>
                                </div>
                                <div class="endpoint-item">
                                    <div>
                                        <span class="method-badge method-get">GET</span>
                                        <span class="ms-2">Users</span>
                                    </div>
                                    <code>{{ api_endpoints.users }}</code>
                                </div>
                                <div class="endpoint-item">
                                    <div>
                                        <span class="method-badge method-get">GET</span>
                                        <span class="ms-2">Reports</span>
                                    </div>
                                    <code>{{ api_endpoints.reports }}</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row g-4 mb-4">
                    <div class="col-12">
                        <div class="api-card p-4">
                            <h4 class="mb-4">⚡ Quick Actions</h4>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <a href="{{ documentation.swagger_ui }}" class="btn btn-outline-primary w-100" target="_blank">
                                        <i class="fas fa-play"></i> Test API
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ documentation.schema_json }}" class="btn btn-outline-success w-100" target="_blank">
                                        <i class="fas fa-download"></i> Download Schema
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="{{ system.admin }}" class="btn btn-outline-warning w-100" target="_blank">
                                        <i class="fas fa-cog"></i> Admin Panel
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-outline-info w-100" onclick="copyApiUrl()">
                                        <i class="fas fa-copy"></i> Copy API URL
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="footer">
                    <div class="row align-items-center">
                        <div class="col-md-6 text-md-start text-center">
                            <h5>{{ support.company }}</h5>
                            <p class="mb-0">
                                <i class="fas fa-envelope"></i> {{ support.email }}
                            </p>
                        </div>
                        <div class="col-md-6 text-md-end text-center mt-3 mt-md-0">
                            <p class="mb-0">
                                <i class="fas fa-code"></i> API Version {{ version }}
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-shield-alt"></i> CORS Enabled
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyApiUrl() {
            const apiUrl = window.location.origin + '/api/';
            navigator.clipboard.writeText(apiUrl).then(function() {
                // Show success message
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                btn.classList.remove('btn-outline-info');
                btn.classList.add('btn-success');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-outline-info');
                }, 2000);
            });
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on scroll
            const cards = document.querySelectorAll('.api-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
