# Ticketing System Backend

This is the backend for the Ticketing System application. The system provides a comprehensive solution for managing IT support tickets, user management, and automated workflows.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Environment Setup](#environment-setup)
  - [Local Development](#local-development)
  - [Staging Environment](#staging-environment)
  - [Production Environment](#production-environment)
- [Automation](#automation)
- [CI/CD Pipeline](#cicd-pipeline)
- [System Information](#system-information)
- [Deployment History](#deployment-history)

## Prerequisites

Before you begin, ensure you have the following installed:

- Python 3.10+
- MySQL 8.0+
- Git
- Redis (for production/staging)

## Quick Start

To quickly set up a development environment and start the server:

```bash
# Clone the repository
git clone https://github.com/your-organization/ticketing-system-tool-backend.git
cd ticketing-system-tool-backend

# Run the automation script to set up the development environment
./automate.sh setup

# Start the development server
./automate.sh dev
```

## Environment Setup

Environment: development
Setup Date: 2025-05-08 11:35:51
Setup By: sent<PERSON><PERSON>maruthasalam

## Deployment History

### Deployment - 2025-05-08 11:36:04
- Environment: development
- Branch: feature/jenkins_config
- Commit: 9f176823ea1a3f1c2c7ab14080cb9eb792dc2c7b
- Deployed by: senthilkumarmaruthasalam

