#### Loading Seed Data in Django Using JSON Fixtures

# 1. Create a fixture (JSON file) with initial data:

    python manage.py dumpdata user_management.Role --indent 2 > role.json

# 2. Run the loaddata Command - For seperate files

    python manage.py loaddata seeders/role.json

# 3. To run and load all the files data

# RUN THIS COMMAND FOR THE RECRUITER API

    Get-ChildItem seeders/*.json | ForEach-Object { python manage.py loaddata $_.FullName }
