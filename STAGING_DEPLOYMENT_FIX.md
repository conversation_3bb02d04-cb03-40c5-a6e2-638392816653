# Staging Server Japanese Filename Fix

## 🚨 Problem
Your staging server throws this error when uploading Japanese filenames:
```json
{
    "status": 0,
    "code": 400,
    "message": "Error saving file 【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx: 'ascii' codec can't encode character '\\u3010' in position 80: ordinal not in range(128)"
}
```

## ✅ Solution Implemented

### 1. **Enhanced Filename Conversion**
- Uses `unidecode` library for universal Unicode-to-ASCII conversion
- Handles Japanese brackets `【】` and all other Unicode characters
- Multiple fallback mechanisms for robustness

### 2. **ASCII-Safe Error Handling**
- Error messages are now ASCII-safe
- No more Unicode characters in error responses
- Graceful fallback for all edge cases

### 3. **Comprehensive Testing**
- Added debug logging to track conversion process
- Multiple fallback layers for maximum compatibility

## 🚀 Deployment Steps

### Step 1: Install Dependencies
```bash
# On your staging server
pip install unidecode
```

### Step 2: Deploy Updated Code
The following files have been updated:
- `apps/ticket_management/views.py` - Enhanced filename conversion
- `apps/ticket_management/models.py` - Added original_filename field
- `apps/ticket_management/serializers.py` - Display original filenames
- `requirements.txt` - Added unidecode dependency

### Step 3: Run Migration
```bash
python manage.py makemigrations ticket_management
python manage.py migrate
```

### Step 4: Restart Server
```bash
# Restart your Django application
sudo systemctl restart your-django-app
# or however you restart your staging server
```

## 🧪 Testing

### Test the Exact Problematic File
```bash
# Run the test script
python test_japanese_filename_fix.py
```

### Manual Test Upload
Upload this exact file that was causing the error:
`【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx`

**Expected Result:**
- ✅ HTTP 201 (Success) instead of 400 (Error)
- ✅ File saved with ASCII-safe name
- ✅ Original Japanese filename preserved in database
- ✅ No ASCII encoding errors

## 📋 What the Fix Does

### Before (Error):
```
Filename: 【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx
Result: ASCII encoding error ❌
```

### After (Success):
```
Original: 【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx
Storage:  JRtawa202505getsu_du_getsu_ji_bao_gao_shu_v0_2_1_ticket_52.xlsx
Display:  【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx
Result:   Success ✅
```

## 🔧 Technical Details

### Filename Conversion Process:
1. **Normalize**: Unicode normalization (NFKD)
2. **Convert**: `unidecode` converts `【JRタワー】` → `[JRtawa]`
3. **Clean**: Remove/replace special characters
4. **Validate**: Ensure ASCII compatibility
5. **Fallback**: Hash-based naming if conversion fails

### Error Handling:
- All error messages are ASCII-safe
- Japanese characters in errors are safely converted
- Multiple fallback mechanisms prevent crashes

### Database Storage:
- `attachement`: ASCII-safe filename for storage
- `original_filename`: Original Japanese filename for display

## 🌍 Universal Language Support

This fix supports **ALL languages**, not just Japanese:
- ✅ Japanese: `【JRタワー】` → `[JRtawa]`
- ✅ Chinese: `中文文档` → `Zhong Wen Wen Dang`
- ✅ Korean: `한국어파일` → `hangug-eopaeil`
- ✅ Arabic: `ملف_عربي` → `mlf_rby`
- ✅ Russian: `Русский_файл` → `Russkii_fail`

## 🛡️ Fallback Mechanisms

### Level 1: Unidecode Conversion
```python
from unidecode import unidecode
safe_name = unidecode("【JRタワー】")  # → "[JRtawa]"
```

### Level 2: ASCII Filter
```python
safe_name = ''.join(char for char in filename if ord(char) < 128)
```

### Level 3: Hash-based Naming
```python
hash_name = f"file_{ticket_id}_{timestamp}_{hash}.xlsx"
```

### Level 4: Error Message Safety
```python
safe_error = error_message.encode('ascii', errors='replace').decode('ascii')
```

## 📊 Expected Results

### API Response (Success):
```json
{
    "status": 1,
    "code": 201,
    "message": "Ticket created successfully",
    "ticket_id": 52,
    "attachments": [
        {
            "id": 123,
            "original_filename": "【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx",
            "display_filename": "【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx",
            "file_url": "http://staging.com/media/ticket_attachments/JRtawa_ticket_52.xlsx"
        }
    ]
}
```

### Error Response (If something still fails):
```json
{
    "status": 0,
    "code": 400,
    "message": "Error saving file '[JRtawa]202505getsu_du_getsu_ji_bao_gao_shu_v0_2_1': File upload failed. Please try with a different filename or contact support.",
    "original_error": "ASCII-safe error message"
}
```

## 🔍 Troubleshooting

### If you still get ASCII errors:

1. **Check unidecode installation:**
   ```bash
   python -c "from unidecode import unidecode; print('OK')"
   ```

2. **Check server logs:**
   Look for debug messages like:
   ```
   Unidecode result: [JRtawa]202505getsu du getsu ji bao gao shu v0 2 1
   Cleaned filename: JRtawa202505getsu_du_getsu_ji_bao_gao_shu_v0_2_1
   Final ASCII-safe name: JRtawa202505getsu_du_getsu_ji_bao_gao_shu_v0_2_1_ticket_52.xlsx
   ```

3. **Test locally first:**
   ```bash
   python test_japanese_filename_fix.py
   ```

## 🎉 Success Criteria

After deployment, you should be able to:
- ✅ Upload `【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx` without errors
- ✅ See original Japanese filename in the UI
- ✅ Download files with original Japanese names
- ✅ No ASCII encoding errors in logs or responses

## 📞 Support

If you still encounter issues:
1. Check the debug logs for conversion steps
2. Verify unidecode is installed: `pip list | grep unidecode`
3. Test with the provided test script
4. Check that the migration was applied successfully

The fix is comprehensive and should handle your specific filename and any other Unicode filenames you encounter! 🇯🇵✨
