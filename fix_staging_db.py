#!/usr/bin/env python
"""
This script diagnoses and fixes database configuration issues in the Django settings.
Run this script on your staging server to fix the 500 error.
"""

import os
import sys
import json
from pathlib import Path

def check_env_file():
    """Check if .env file exists and has the correct database settings."""
    env_path = Path('.env')
    if not env_path.exists():
        print("ERROR: .env file not found!")
        return False
    
    # Read .env file
    env_content = env_path.read_text()
    
    # Check for database settings
    required_settings = [
        'DATABASE_ENGINE',
        'DATABASE_NAME',
        'DATABASE_USER',
        'DATABASE_PASSWORD',
        'DATABASE_HOST',
        'DATABASE_PORT'
    ]
    
    missing_settings = []
    for setting in required_settings:
        if setting not in env_content:
            missing_settings.append(setting)
    
    if missing_settings:
        print(f"ERROR: Missing database settings in .env file: {', '.join(missing_settings)}")
        return False
    
    print("SUCCESS: .env file exists and contains database settings.")
    return True

def check_python_decouple():
    """Check if python-decouple is installed."""
    try:
        import decouple
        print("SUCCESS: python-decouple is installed.")
        return True
    except ImportError:
        print("ERROR: python-decouple is not installed!")
        return False

def check_settings_file():
    """Check if settings.py is correctly configured to use environment variables."""
    settings_path = Path('ticketing_system/settings.py')
    if not settings_path.exists():
        print("ERROR: settings.py file not found!")
        return False
    
    # Read settings.py file
    settings_content = settings_path.read_text()
    
    # Check if decouple is imported
    if 'from decouple import config' not in settings_content:
        print("ERROR: 'from decouple import config' not found in settings.py!")
        return False
    
    # Check if database settings use config
    if "config('DATABASE_ENGINE'" not in settings_content:
        print("ERROR: Database settings in settings.py are not using config()!")
        return False
    
    print("SUCCESS: settings.py is correctly configured to use environment variables.")
    return True

def fix_env_file():
    """Create or fix the .env file with correct database settings."""
    env_content = """# Django settings
SECRET_KEY=django-insecure-_$!bmy3u@vlgu@=11o9wv^l=2c@=l99@wy41nv8e1kx7670^xo
DEBUG=False
DJANGO_SETTINGS_MODULE=ticketing_system.settings

# Database settings
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=nex_ticket_stg_db
DATABASE_USER=nex-ticketing-stg
DATABASE_PASSWORD=RsRtW8u96@N
DATABASE_HOST=***************
DATABASE_PORT=3306
DATABASE_CHARSET=utf8mb4

# Host settings
ALLOWED_HOSTS=internal-project.nexware-global.com,localhost,127.0.0.1,***************

# Email settings
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.zeptomail.in
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=PHtE6r0JELzu2Gcpo0JWtKewRcCiYIss/epmeAEU4dlHC/8ETk1Sqtkjlme1rRguB/ATF/WewIw9tr/OtL/WLDu8MWpOXGqyqK3sx/VYSPOZsbq6x00asV4dd0LVU4bqdtdr1CDTvdjZNA==

# Application settings
BACKEND_URL=http://internal-project.nexware-global.com:9019/

# CORS settings
CORS_ALLOWED_ORIGINS=http://localhost:3001,http://localhost:3000,http://internal-project.nexware-global.com:9018,http://internal-project.nexware-global.com:9019,https://internal-project.nexware-global.com:9018,https://internal-project.nexware-global.com:9019
CSRF_TRUSTED_ORIGINS=http://localhost:8000,http://internal-project.nexware-global.com:9018,http://internal-project.nexware-global.com:9019,https://internal-project.nexware-global.com:9018,https://internal-project.nexware-global.com:9019,https://internal-project.nexware-global.com:8001,wss://internal-project.nexware-global.com:8001
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("SUCCESS: Created/fixed .env file with correct database settings.")

def fix_settings_file():
    """Fix the settings.py file to correctly use environment variables."""
    settings_path = Path('ticketing_system/settings.py')
    settings_content = settings_path.read_text()
    
    # Fix decouple import
    if '# from decouple import config' in settings_content:
        settings_content = settings_content.replace('# from decouple import config', 'from decouple import config')
    elif 'from decouple import config' not in settings_content:
        # Add import after other imports
        import_section_end = settings_content.find('# Build paths inside the project like this:')
        if import_section_end == -1:
            import_section_end = settings_content.find('BASE_DIR = ')
        
        settings_content = settings_content[:import_section_end] + 'from decouple import config\n\n' + settings_content[import_section_end:]
    
    # Fix database configuration
    database_section_start = settings_content.find('DATABASES = {')
    database_section_end = settings_content.find('}', database_section_start)
    database_section_end = settings_content.find('}', database_section_end + 1) + 1
    
    new_database_config = """DATABASES = {
   'default': { 
      'ENGINE': config('DATABASE_ENGINE', default='django.db.backends.mysql'),
      'NAME': config('DATABASE_NAME', default='ticketing_tool'),
      'USER': config('DATABASE_USER', default='root'),
      'PASSWORD': config('DATABASE_PASSWORD', default='admin@123'),
      'HOST': config('DATABASE_HOST', default='localhost'),
      'PORT': config('DATABASE_PORT', default='3306'),
      'OPTIONS': {
            'charset': config('DATABASE_CHARSET', default='utf8mb4'),
            },
   }
}"""
    
    settings_content = settings_content[:database_section_start] + new_database_config + settings_content[database_section_end:]
    
    # Write the updated content back to the file
    with open('ticketing_system/settings.py', 'w') as f:
        f.write(settings_content)
    
    print("SUCCESS: Fixed settings.py to correctly use environment variables.")

def main():
    """Main function to diagnose and fix database configuration issues."""
    print("Diagnosing database configuration issues...")
    
    env_ok = check_env_file()
    decouple_ok = check_python_decouple()
    settings_ok = check_settings_file()
    
    if env_ok and decouple_ok and settings_ok:
        print("\nAll checks passed! Your configuration should be working correctly.")
        print("If you're still experiencing issues, check the server logs for more details.")
        return
    
    print("\nFixing configuration issues...")
    
    if not decouple_ok:
        print("Installing python-decouple...")
        os.system('pip install python-decouple')
    
    if not env_ok:
        fix_env_file()
    
    if not settings_ok:
        fix_settings_file()
    
    print("\nConfiguration fixed! Restart your Django server to apply the changes.")

if __name__ == "__main__":
    main()
