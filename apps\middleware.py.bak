"""
Custom middleware to fix CORS headers issues.
"""
import logging
from django.http import HttpResponse

logger = logging.getLogger(__name__)

class RemoveDuplicateCORSHeadersMiddleware:
    """
    Middleware to completely handle CORS, bypassing any other CORS handling.

    This middleware:
    1. Completely handles OPTIONS requests for CORS preflight
    2. Ensures only one set of CORS headers is sent in the response
    """

    def __init__(self, get_response):
        self.get_response = get_response
        # List of allowed origins
        self.allowed_origins = [
            "http://internal-project.nexware-global.com:9018",  # Frontend
            "http://internal-project.nexware-global.com:9019",  # Backend
            "http://localhost:3000",
            "http://localhost:4173",
            "http://localhost:5173",
        ]

    def __call__(self, request):
        # Get the origin from the request
        origin = request.headers.get('Origin')

        # For OPTIONS requests (preflight), handle directly and return
        if request.method == 'OPTIONS' and origin:
            if origin in self.allowed_origins:
                response = HttpResponse()
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH'
                response['Access-Control-Allow-Headers'] = (
                    'Authorization, Content-Type, X-Requested-With, '
                    'Accept, Origin, X-CSRFToken, Bearer-Token, '
                    'user-agent, cache-control, pragma'
                )
                response['Access-Control-Allow-Credentials'] = 'true'
                response['Access-Control-Max-Age'] = '86400'  # 24 hours
                return response
            return HttpResponse(status=403)  # Forbidden if origin not allowed

        # For non-OPTIONS requests, process normally
        response = self.get_response(request)

        # Only add CORS headers if origin is in allowed list
        if origin and origin in self.allowed_origins:
            # First, remove any existing CORS headers to prevent duplicates
            self._remove_cors_headers(response)

            # Then add our own headers
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'

        return response

    def _remove_cors_headers(self, response):
        """Remove all CORS-related headers from the response."""
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Credentials',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers',
            'Access-Control-Max-Age',
            'Access-Control-Expose-Headers',
        ]

        for header in cors_headers:
            if header in response:
                del response[header]
