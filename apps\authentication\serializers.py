from rest_framework import serializers
from .models import Module, RoleModuleAccess, SubModule, SubModuleAccess


class ModuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Module
        fields = '__all__'

class SubModuleSerializer(serializers.ModelSerializer):
    module_name = serializers.CharField(source='module_id.module_name', read_only=True)
    class Meta:
        model = SubModule
        fields = '__all__'
   
class RoleModuleAccessSerializer(serializers.ModelSerializer):
    role= RoleModuleAccess
    class Meta:
        model = RoleModuleAccess
        fields = '__all__'


class RoleModuleAccessDetailSerializer(serializers.ModelSerializer):
    role_name = serializers.CharField(source='role.role_name', read_only=True)
    module_name = serializers.CharField(source='module.module_name', read_only=True)
    module_id = serializers.IntegerField(source='module.id', read_only=True)

    class Meta:
        model = RoleModuleAccess
        fields = ['role_name', 'module_id', 'module_name', 'has_access']


class SubModuleAccessSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubModuleAccess
        fields = '__all__'