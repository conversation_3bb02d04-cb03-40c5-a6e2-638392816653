from rest_framework import serializers
from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Notification, Priority<PERSON><PERSON><PERSON>, <PERSON>icket,<PERSON><PERSON><PERSON>,Ticket<PERSON>hatAttachenment,Status,StatusTracking
# from .models import TicketChat
from ..user_management.models import User
from firebase_admin import messaging
from django.http import JsonResponse
from ..authentication.firebase_utils import send_push_notification
from ..user_management.serializers import UserSerializer


# def send_push_notification(fcm_token, title, body):
#         try:
#             # message = messaging.Message(
#             #     notification=messaging.Notification(
#             #         title=title,
#             #         body=body,
#             #     ),
#             #     token=fcm_token,
#             # )
#             message = messaging.Message(
#                 notification=messaging.Notification(
#                     title="Test Notification",
#                     body="Hello from Firebase!",
#                 ),
#                 token="YOUR_FCM_TOKEN",  # Replace with a valid FCM token
#             )
#             response = messaging.send(message)
#             print("response:",response)
#             return response
#         except Exception as e:
#             print(f"Error sending push notification: {e}")
#             return None
# def send_push_notification(fcm_token, title, body):
#     try:
#         message = messaging.Message(
#             notification=messaging.Notification(
#                 title=title,
#                 body=body,
#             ),
#             token=fcm_token,
#         )
#         response = messaging.send(message)
#         print("Successfully sent message:", response)
#     except Exception as e:
#         print(f"Error sending push notification: {e}")
# class SaveFCMTokenSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = User
#         fields = ["fcm_token"]

#     def update(self, instance, validated_data):
#         instance.fcm_token = validated_data.get("fcm_token")
#         instance.save()
#         return instance

class TicketDocumentSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    display_filename = serializers.SerializerMethodField()  # Show original Japanese filename

    class Meta:
        model = TicketChatAttachenment
        fields = '__all__'

    def get_file_url(self, obj):
        if obj.attachement:
            return obj.attachement.url
        return None

    def get_display_filename(self, obj):
        """Return the original Japanese filename for display to users"""
        if obj.original_filename:
            return obj.original_filename  # Show original Japanese name
        elif obj.attachement:
            return obj.attachement.name.split('/')[-1]  # Fallback to stored filename
        return 'Unknown file'

class TicketStatusSerializer(serializers.ModelSerializer):
     class Meta:
        model = Status
        fields = '__all__'
class TicketApprovalStatusSerializer(serializers.ModelSerializer):
     class Meta:
        model = Status
        fields = '__all__'
class TicketPrioritySerializer(serializers.ModelSerializer):
     class Meta:
        model = PriorityLevel
        fields = '__all__'


class TicketSerializer(serializers.ModelSerializer):
    attachement = TicketDocumentSerializer(many=True, read_only=True, source="attachments") 
    assigned_to_fullname = serializers.SerializerMethodField()
    created_by_fullname = serializers.SerializerMethodField()
    priority_names=serializers.SerializerMethodField()
    category_name=serializers.SerializerMethodField()
    subcategory_name=serializers.SerializerMethodField()
    status_name=serializers.SerializerMethodField()
    project_name=serializers.SerializerMethodField()
    status = serializers.PrimaryKeyRelatedField(queryset=Status.objects.all(), required=False)
    location_name=serializers.SerializerMethodField()
    approved_by_name = serializers.SerializerMethodField() 
    created_by = UserSerializer(read_only=True)  
    class Meta:
        model = Ticket
        fields = '__all__'
    
    def get_location_name(self, obj):
        return obj.location.location_name if obj.location else None

    def get_assigned_to_fullname(self, obj):
        assigned_to = obj.assigned_to
        if assigned_to:
            return f"{assigned_to.first_name} {assigned_to.last_name}"
        return None

    def get_created_by_fullname(self, obj):
        created_by = obj.created_by
        if created_by:
            return f"{created_by.first_name} {created_by.last_name}"
        return None
    def get_approved_by_name(self, obj):
        approved_by = obj.approved_by
        if approved_by:
            return f"{approved_by.first_name} {approved_by.last_name}"
        return None
    def get_location_name(self,obj):
        location=obj.location
        if location:
             return f"{location.location_name}"
        return None
    
    def get_priority_names(self,obj):
        priority=obj.priority
        if priority:
             return f"{priority.priority_name}"
        return None
    def get_category_name(self,obj):
        category=obj.category
        if category:
             return f"{category.cat_name}"
        return None
    def get_subcategory_name(self,obj):
        subcategory=obj.subcategory
        if subcategory:
             return f"{subcategory.subcat_name}"
        return None
    def get_status_name(self,obj):
        status=obj.status
        if status:
             return f"{status.name}"
        return None
    def get_project_name(self, obj):
        project = obj.project
        
        # If project is a dictionary or object with attributes
        if hasattr(project, "project_name"):  
            return project.project_name  
        
        # If project is a string, return it directly
        if isinstance(project, str):  
            return project  

        return None  # Return None if project is None or unexpected type


    # def create(self, validated_data):
    #         # Extract files from context
    #         request = self.context['request']
    #         files = request.FILES.getlist('attachement')  # Handle multiple file uploads

    #         # Create the ticket
    #         ticket = Ticket.objects.create(**validated_data)
            
    #         # watchers = ticket.watchers if ticket.watchers else ""  # Default to empty string
    #         # watcher_ids = watchers.split(",") if watchers else []  # Avoid split on None      
    #         # pm = User.objects.filter(email__in=watcher_ids, role="R003").first()
    #         # print("watcher_ids:",watcher_ids)

    #         # if pm:
    #         #         # Assign the PM to the ticket and update approval status
    #         #         ticket.assigned_to = pm
    #         #         ticket.approvel_status = "pending"
    #         #         ticket.status = Status.objects.get(name="awaitingApproval")
    #         #         ticket.save()

    #         #         # Send notification to PM
    #         #         Notification.objects.create(
    #         #             ticket=ticket,
    #         #             message=f"New ticket requires approval: {ticket.title} (ID: {ticket.ticket_id})",
    #         #             is_read=False
    #         #         )
    #         # print ("PM:",pm)
    #         # Save each file in TicketDocument
    #         for file in files:
    #             TicketChatAttachenment.objects.create(ticket=ticket, attachement=file)

    #         super_admin = User.objects.filter(role_id="R001").first()  # Adjust based on your role model
    #         admin = User.objects.filter(role_id="R002").first() 
    #         print("SA:",super_admin)
    #         # Trigger a notification after saving the ticket
    #         notification_message = f"New ticket created: {ticket.title} (ID: {ticket.ticket_id})"
    #         Notification.objects.create(
    #             ticket=ticket,
    #             created_by=ticket.created_by,
    #             message=notification_message,
    #             is_read=False  # Set to False by default
    #         )
    #         if super_admin and super_admin.fcm_token:
    #             notification_response = send_push_notification(
    #             super_admin,  # ✅ Correct: Pass the actual user instance
    #             "New Ticket Created",
    #             f"Ticket '{ticket.title}' (ID: {ticket.ticket_id}) has been created."
    #         )
    #         print("Notification Response:", notification_response)  # Debugging
    #         # else:
    #         # print("Super Admin is None or has no FCM Token!")  # Debugging

        
    #         ChatList.objects.create(
    #             ticket=ticket,
    #             message=ticket.description,  # Removed unnecessary curly braces
    #             title=ticket.title,
    #             sender_id=ticket.created_by_id,  # Ensure correct field reference

    #                     # receiver_id=ticket.assigned_to_id
    #         )
    #         StatusTracking.objects.create(
    #             ticket=ticket,
    #             current_status=ticket.status,
    #             updated_by=ticket.updated_by,
    #             created_by=ticket.created_by,
    #                     )   
            
            

    #         # Optionally, add assigned_to and created_by names if required in the response
    #         ticket.refresh_from_db()  # To fetch the latest fields (in case the created ticket object needs them)

    #         # Return the created ticket object (now including documents and additional fields)
    #         return ticket
        
    # def update(self, instance, validated_data):
    #     request = self.context['request']
    #     files = request.FILES.getlist('attachement')  # Handle multiple file uploads
    #     print("validated_data:", validated_data)

    #     # Update ticket fields
    #     for attr, value in validated_data.items():
    #         setattr(instance, attr, value)
    #     instance.save()

    #     # Save new attachments
    #     for file in files:
    #         TicketChatAttachenment.objects.create(ticket=instance, attachement=file)

    #     # **Update ChatList** when ticket is updated
    #     chat_message = f"Ticket updated: {instance.title} (ID: {instance.ticket_id})"

    #     ChatList.objects.create(
    #         ticket=instance,
    #         message=instance.description,
    #         title=instance.title,
    #         sender_id=instance.updated_by.id if instance.updated_by else None,  # Ensure ID is used
    #     )

    #     # Ensure `created_by_id` is never null
    #     created_by_id = instance.created_by.id if instance.created_by else request.user.id  # Fallback to current user
    #     print("created_by_id ",created_by_id )

    #     # Notification
    #     Notification.objects.create(
    #         ticket=instance,
    #         message=chat_message,
    #         is_read=False
    #     )

    #     # Get admin users
    #     super_admin = User.objects.filter(role_id="R001").first()
    #     admin = User.objects.filter(role_id="R002").first()

    #     if admin and admin.fcm_token:
    #         send_push_notification(
    #             admin,
    #             "New Ticket Created",
    #             f"Ticket '{instance.title}' (ID: {instance.ticket_id}) has been created."
    #         )

    #     # **Fixing created_by_id issue**
    #     StatusTracking.objects.create(
    #         ticket=instance,
    #         current_status=instance.status,
    #         updated_by_id=instance.updated_by.id if instance.updated_by else request.user.id,  # Fallback to current user
    #         created_by_id=created_by_id,  # Ensure it is never null
    #     )

    #     instance.refresh_from_db()  # Fetch the latest updated fields

    #     return instance

from .models import StatusTracking

class StatusTrackingSerializer(serializers.ModelSerializer):
    # current_status = serializers.CharField(source="current_status.name", read_only=True)
    updated_by = serializers.SerializerMethodField()
    created_by = serializers.SerializerMethodField()

    class Meta:
        model = StatusTracking
        fields = '__all__'

    def get_updated_by(self, obj):
        if obj.updated_by:
            return f"{obj.updated_by.first_name} {obj.updated_by.last_name}"
        return None

    def get_created_by(self, obj):
        if obj.created_by:
            return f"{obj.created_by.first_name} {obj.created_by.last_name}"
        return None

class ChatListSerializer(serializers.ModelSerializer):
    sender_name = serializers.SerializerMethodField()
    receiver_name = serializers.SerializerMethodField()

    class Meta:
        model = ChatList
        fields = ['id', 'ticket', 'title', 'message', 'sender_id', 'sender_name', 'receiver_id', 'receiver_name', 'created_at', 'updated_at']

    def get_sender_name(self, obj):
        if obj.sender:
            return f"{obj.sender.first_name} {obj.sender.last_name}"
        return None

    def get_receiver_name(self, obj):
        if obj.receiver:
            return f"{obj.receiver.first_name} {obj.receiver.last_name}"
        return None

        
class TicketUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ticket
        fields = ['assigned_to','due_date','due_expiry_reason','status']
    
    def update(self, instance, validated_data):
        # Update ticket fields
        assigned_user = validated_data.get('assigned_to', instance.assigned_to)
        instance.assigned_to = assigned_user
        instance.category = validated_data.get('category', instance.category)
        instance.subcategory = validated_data.get('subcategory', instance.subcategory)
        instance.due_date=validated_data.get('due_date', instance.due_date)
        instance.status=validated_data.get('status', instance.status)
        instance.due_expiry_reason=validated_data.get('due_expiry_reason', instance.due_expiry_reason)

        instance.save()
        # ✅ Ensure assigned_user is valid before filtering
        if assigned_user:
            # ✅ Filter users based on role_id (R002 for Admins)
            admin_users = User.objects.filter(role_id="R002")
            

            # ✅ Send push notification to all found admins
            for admin in admin_users:
                if admin.fcm_token:
                    updated_notification = send_push_notification(
                        admin,  # ✅ Pass actual user instance
                        "New Ticket Assigned",
                        f"Ticket '{instance.title}' (ID: {instance.ticket_id}) has been assigned to {assigned_user.first_name} {assigned_user.last_name}."
                    )

        return instance  # ✅ Return the updated ticket


class FeedbackSerializer(serializers.ModelSerializer):
    feedback_emoji = serializers.SerializerMethodField()
    feedback_label = serializers.SerializerMethodField()

    class Meta:
        model = Feedback
        fields = [
            'feedback_id',
            'ticket',
            'employee',
            'feedback_value',
            'feedback_emoji',
            'feedback_label',
            'created_at'
        ]

    def get_feedback_emoji(self, obj):
        return obj.feedback_value.emoji if obj.feedback_value else None

    def get_feedback_label(self, obj):
        return obj.feedback_value.label if obj.feedback_value else None

class TicketChatSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChatList
        fields = '__all__'

        
# class TicketChatSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = TicketChat
#         fields = ['ticket', 'user', 'message', 'image', 'created_at']


    def get_feedback_value_display(self, obj):
        return dict(Feedback.FEEDBACK_VALUES).get(obj.feedback_value)

# class TicketSerializer(serializers.ModelSerializer):
#     feedbacks = FeedbackSerializer(many=True, read_only=True)  # Include feedbacks

#     class Meta:
#         model = Ticket
#         fields = '__all__'

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = '__all__'

class FeedbackValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = FeedbackValue
        fields = '__all__'

class FeedbackSerializer(serializers.ModelSerializer):
    class Meta:
        model = Feedback
        fields = '__all__'
