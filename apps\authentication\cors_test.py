"""
Simple view to test CORS functionality.
"""
import logging
from django.http import JsonResponse, HttpResponse

logger = logging.getLogger(__name__)

def test_cors(request):
    """
    Simple view to test CORS functionality.
    This view returns a simple JSON response with CORS headers.
    """
    try:
        logger.debug("CORS test view called")
        logger.debug(f"Request method: {request.method}")

        # Convert headers to a safe dict for logging
        safe_headers = {}
        for key, value in request.headers.items():
            safe_headers[key] = value

        logger.debug(f"Request headers: {safe_headers}")

        # For OPTIONS requests, return a simple response
        if request.method == 'OPTIONS':
            response = HttpResponse()
            response['Content-Type'] = 'text/plain'
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
            return response

        # For other requests, return a JSON response
        response = JsonResponse({
            "message": "CORS test successful",
            "method": request.method,
            "path": request.path,
            "is_secure": request.is_secure(),
            "is_ajax": request.headers.get('X-Requested-With') == 'XMLHttpRequest',
        })

        # Log response headers for debugging
        logger.debug(f"Response headers: {dict(response.headers)}")

        return response
    except Exception as e:
        # Log any exceptions
        logger.error(f"Error in CORS test view: {str(e)}", exc_info=True)

        # Return a simple error response
        return JsonResponse({
            "error": "An error occurred",
            "message": str(e)
        }, status=500)
