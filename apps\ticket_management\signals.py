import json
from django.core.mail import send_mail
from django.db.models.signals import post_save
from django.conf import settings
from django.dispatch import receiver
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from .models import Ticket
from django.utils.html import strip_tags
from apps.user_management.models import User  # Ensure correct import of User model


@receiver(post_save, sender=Ticket)
def ticket_notification(sender, instance, created, **kwargs):
    """
    Signal to send email notifications when a ticket is created or updated.
    Also sends real-time WebSocket updates.
    """
    from_email = settings.EMAIL_HOST_USER
    # from_email = instance.created_by.email if instance.created_by and instance.created_by.email else '<EMAIL>'
    print("from_email:", from_email)
    channel_layer = get_channel_layer()  # Get WebSocket channel layer

    # Convert watchers to a list if stored as JSON
    if isinstance(instance.watchers, str):
        try:
            watchers = json.loads(instance.watchers)  
        except json.JSONDecodeError:
            watchers = []
    else:
        watchers = instance.watchers if isinstance(instance.watchers, list) else []

    # Convert watcher IDs to emails
    watcher_emails = []
    for watcher in watchers:
        if isinstance(watcher, int):  
            user = User.objects.filter(id=watcher).first()
            if user and user.email:
                watcher_emails.append(user.email)
        elif isinstance(watcher, str) and '@' in watcher:
            watcher_emails.append(watcher.strip())

    # WebSocket message payload
    message_type = "created" if created else "updated"
    ws_message = {
        "type": "ticket_update",
        "message": f"Ticket '{instance.title}' has been {message_type}.",
        "ticket_id": instance.ticket_id,
        "title": instance.title,
        "status": instance.status.name if instance.status else "Unknown",
        "created_by": instance.created_by.username if instance.created_by else "Unknown",
        "updated_at": instance.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
    }

    # Send WebSocket message to relevant ticket room
    async_to_sync(channel_layer.group_send)(
        f"ticket_{instance.ticket_id}",
        {"type": "chat_message", "message": json.dumps(ws_message)}
    )

    # Email notifications


    if created:
        # Subject and Message for User
        subject_user = f"Ticket Created: {instance.title}"
        message_user_html = f"""
        <p>Hello,</p>
        <p>Your ticket <strong>'{instance.title}'</strong> has been created successfully.</p>
        <p><strong>Description:</strong><br>{instance.description}</p>
        <p>Thank you!</p>
        """
        
        # Subject and Message for IT Team
        subject_it = f"New Ticket Created: {instance.title}"
        message_it_html = f"""
        <p>Hello IT Team,</p>
        <p>A new ticket has been created by <strong>{instance.created_by.first_name}{instance.created_by.last_name}</strong>.</p>
        <p><strong>Title:</strong> {instance.title}</p>
        <p><strong>Description:</strong><br>{instance.description}</p>
        <p><strong>Project:</strong> {instance.project}</p>
        <p><strong>Priority:</strong> {instance.priority}</p>
        <p><strong>Opening Date:</strong> {instance.created_at.strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Please address this issue promptly.</p>
        """

        recipient_it_team =  watcher_emails
        recipient_user = [instance.created_by.email]

        # Sending Emails
        send_mail(subject_user, strip_tags(message_user_html), from_email, recipient_user, html_message=message_user_html)
        if recipient_it_team:
            send_mail(subject_it, strip_tags(message_it_html), from_email, recipient_it_team, html_message=message_it_html)

    else:
        if isinstance(instance.updated_by, User) and isinstance(instance.assigned_to, User):
            # Subject for Ticket Update
            subject_user = f"Ticket Updated {instance.ticket_id}: {instance.title}"
            
            # HTML Email Body
            message_user_html = f"""
            <p>Hello {instance.created_by.first_name}{instance.created_by.last_name}</p>
            <p>Your ticket <strong>'{instance.title}'</strong> has been updated.</p>
            <p><strong>Ticket Details:</strong></p>
            <ul>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Project:</strong> {instance.project}</li>
                <li><strong>Status:</strong> {instance.status}</li>
                <li><strong>Assignee:</strong> {instance.assigned_to.first_name} {instance.assigned_to.last_name}</li>
                <li><strong>Opening Date:</strong> {instance.created_at.strftime('%Y-%m-%d ')}</li>
                <li><strong>Updated Date:</strong> {instance.updated_at.strftime('%Y-%m-%d ')}</li>
                <li><strong>Priority:</strong> {instance.priority}</li>
            </ul>
            <p>Thank you!</p>
            """

            # Add admin and super admin emails
            admin_emails = User.objects.filter(role_id__in=["R001", "R002"]).values_list('email', flat=True)

            # Combine admin, super admin, creator, and assignee emails
            recipient_user = list(set([instance.created_by.email, instance.assigned_to.email] + list(admin_emails)))

            # Send the email
            send_mail(subject_user, strip_tags(message_user_html), from_email, recipient_user, html_message=message_user_html)

            # recipient_user = [instance.created_by.email,instance.assigned_to.email]
            # send_mail(subject_user, strip_tags(message_user_html), from_email, recipient_user, html_message=message_user_html)

