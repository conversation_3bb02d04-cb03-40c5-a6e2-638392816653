import json
from django.core.mail import send_mail
from django.conf import settings
from django.dispatch import receiver
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
from .models import Ticket
from django.utils.html import strip_tags
from django.db.models.signals import pre_save, post_save
from apps.user_management.models import User  # Ensure correct import of User model
from django.db import transaction


@receiver(pre_save, sender=Ticket)
def capture_previous_status(sender, instance, **kwargs):
    if instance.pk:
        try:
            previous = Ticket.objects.get(pk=instance.pk)
            instance._previous_status = previous.status.name.lower() if previous.status else ""
            instance._previous_assigne = previous.assigned_to
        except Ticket.DoesNotExist:
            instance._previous_status = ""
            instance._previous_assigne = None
    else:
        instance._previous_status = ""
        instance._previous_assigne = None

@receiver(post_save, sender=Ticket)
def ticket_notification(sender, instance, created, **kwargs):
    print(f"[Signal] Triggered for ticket {instance.ticket_id}, created = {created}")
    """
    Signal to send email notifications when a ticket is created or updated.
    Also sends real-time WebSocket updates.
    """
    from_email = settings.EMAIL_HOST_USER
    # from_email = instance.created_by.email if instance.created_by and instance.created_by.email else 'vignesh<PERSON>.<EMAIL>'
    channel_layer = get_channel_layer()  # Get WebSocket channel layer

    # Convert watchers to a list if stored as JSON
    if isinstance(instance.watchers, str):
        try:
            watchers = json.loads(instance.watchers)  
        except json.JSONDecodeError:
            watchers = []
    else:
        watchers = instance.watchers if isinstance(instance.watchers, list) else []

    # Convert watcher IDs to emails
    watcher_emails = []
    for watcher in watchers:
        if isinstance(watcher, int):  
            user = User.objects.filter(id=watcher).first()
            if user and user.email:
                watcher_emails.append(user.email)
        elif isinstance(watcher, str) and '@' in watcher:
            watcher_emails.append(watcher.strip())

    # WebSocket message payload
    message_type = "created" if created else "updated"
    ws_message = {
        "type": "ticket_update",
        "message": f"Ticket '{instance.title}' has been {message_type}.",
        "ticket_id": instance.ticket_id,
        "title": instance.title,
        "status": instance.status.name if instance.status else "Unknown",
        "created_by": instance.created_by.username if instance.created_by else "Unknown",
        "updated_at": instance.updated_at.strftime("%Y-%m-%d %H:%M:%S"),
    }

    # Send WebSocket message to relevant ticket room
    async_to_sync(channel_layer.group_send)(
        f"ticket_{instance.ticket_id}",
        {"type": "chat_message", "message": json.dumps(ws_message)}
    )

    # Email notifications
    # Common recipients
    admin_users = User.objects.filter(role_id="R002").exclude(is_active=False, is_deleted=True)
    admin_emails = list(admin_users.values_list('email', flat=True))

    status_name = instance.status.name.lower() if instance.status else ""

    if created:
        if status_name == "awaiting approval":
            approval_subject = f"[Action Required] Ticket Approval Required: #{instance.ticket_id} - {instance.title}"
            approval_message_html = f"""
            <p>Hello {instance.assigned_to.first_name} {instance.assigned_to.last_name},</p>
            <p>A new ticket has been created and is pending your approval:</p>
            <ul>
                <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                <li><strong>Title:</strong> {instance.title}</li>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Project:</strong> {instance.project}</li>
                <li><strong>Priority:</strong> {instance.priority}</li>
                <li><strong>Created By:</strong> {instance.created_by.get_full_name()}</li>
                <li><strong>Created At:</strong> {instance.created_at.strftime('%Y-%m-%d %H:%M:%S')}</li>
            </ul>
            <p>Please log in and approve or take appropriate action.</p>
            """

            # send_mail(approval_subject, strip_tags(approval_message_html), from_email, [instance.assigned_to.email], html_message=approval_message_html)
            transaction.on_commit(lambda:send_mail(approval_subject, strip_tags(approval_message_html), from_email, [instance.assigned_to.email], html_message=approval_message_html)
)

            # IT team (excluding assigned_to)
            it_team = list(set(watcher_emails + admin_emails))
            if instance.assigned_to.email in it_team:
                it_team.remove(instance.assigned_to.email)

            if it_team:
                approval_html_it = f"""
                <p>Hello Team,</p>
                <p>A ticket is awaiting approval by {instance.assigned_to.get_full_name()}:</p>
                <ul>
                    <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                    <li><strong>Title:</strong> {instance.title}</li>
                    <li><strong>Description:</strong> {instance.description}</li>
                    <li><strong>Project:</strong> {instance.project}</li>
                    <li><strong>Priority:</strong> {instance.priority}</li>
                    <li><strong>Created By:</strong> {instance.created_by.get_full_name()}</li>
                    <li><strong>Created At:</strong> {instance.created_at.strftime('%Y-%m-%d %H:%M:%S')}</li>
                </ul>
                <p>Thank you!</p>
                """
                # send_mail(approval_subject, strip_tags(approval_html_it), from_email, it_team, html_message=approval_html_it)
                transaction.on_commit(lambda:send_mail(approval_subject, strip_tags(approval_html_it), from_email, it_team, html_message=approval_html_it)
)
        else:
            subject_it = f"New Ticket Created: #{instance.ticket_id} - {instance.title}"
            message_it_html = f"""
            <p>Hello Team,</p>
            <p>A new ticket has been created by <strong>{instance.created_by.get_full_name()}</strong>.</p>
            <ul>
                <li><strong>Title:</strong> {instance.title}</li>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Project:</strong> {instance.project}</li>
                <li><strong>Priority:</strong> {instance.priority}</li>
                <li><strong>Opening Date:</strong> {instance.created_at.strftime('%Y-%m-%d')}</li>
            </ul>
            <p>Thank you!</p>
            """
            # send_mail(subject_it, strip_tags(message_it_html), from_email, watcher_emails + admin_emails, html_message=message_it_html)
            transaction.on_commit(lambda:send_mail(subject_it, strip_tags(message_it_html), from_email, watcher_emails + admin_emails, html_message=message_it_html)
)

        # Confirmation to creator
        subject_creator = f"Your Ticket Created: #{instance.ticket_id} - {instance.title}"
        message_creator_html = f"""
        <p>Hello {instance.created_by.get_full_name()},</p>
        <p>Your ticket <strong>'{instance.title}'</strong> has been created successfully.</p>
        <ul>
            <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
            <li><strong>Title:</strong> {instance.title}</li>
            <li><strong>Description:</strong> {instance.description}</li>
            <li><strong>Project:</strong> {instance.project}</li>
            <li><strong>Priority:</strong> {instance.priority}</li>
            <li><strong>Opening Date:</strong> {instance.created_at.strftime('%Y-%m-%d %H:%M:%S')}</li>
        </ul>
        <p>Thank you!</p>
        """
        # send_mail(subject_creator, strip_tags(message_creator_html), from_email, [instance.created_by.email], html_message=message_creator_html)

        transaction.on_commit(lambda: send_mail(
            subject_creator,
            strip_tags(message_creator_html),
            from_email,
            [instance.created_by.email],
            html_message=message_creator_html
        ))

    else:
        previous_status = getattr(instance, '_previous_status', '').lower()
        previous_assigne = getattr(instance, '_previous_assigne', None)
        approval_confirmation_sent = False
        assignment_mail_sent = False
        recipient_user = list(set(filter(None, [
            instance.created_by.email if instance.created_by else None,
            *watcher_emails,
            *admin_emails
        ])))
        recipient_user = list(set(recipient_user))

        #Approval Confirmation
        if previous_status == "awaiting approval" and status_name == "open":
            if instance.approved_by and instance.approved_by.email:
                subject = f"Ticket Approved: #{instance.ticket_id} - {instance.title}"
                html = f"""
                <p>Hello Team,</p>
                <p>The ticket <strong>{instance.title}</strong> has been <strong>approved</strong> by {instance.approved_by.get_full_name()}.</p>
                <ul>
                    <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                    <li><strong>Title:</strong> {instance.title}</li>
                    <li><strong>Description:</strong> {instance.description}</li>
                    <li><strong>Project:</strong> {instance.project}</li>
                    <li><strong>Priority:</strong> {instance.priority}</li>
                    <li><strong>Status:</strong> {instance.status.name}</li>
                </ul>
                <p>Thank you!</p>
                """
                # Create a list of all recipients for the approval email
                approval_recipients = list(set(recipient_user + [instance.approved_by.email]))
                # send_mail(subject, strip_tags(html), from_email, approval_recipients, html_message=html)
                transaction.on_commit(lambda: send_mail(
                    subject,
                    strip_tags(html),
                    from_email,
                    approval_recipients,
                    html_message=html
                ))
                approval_confirmation_sent = True
                
                # Clear recipient_user to prevent sending the general update email
                recipient_user = []
        
        # Assigned ticket notification
        elif previous_assigne != instance.assigned_to and instance.assigned_to and instance.assigned_to.email:
            subject = f"Ticket Assigned: #{instance.ticket_id} - {instance.title}"
            html = f"""
            <p>Hello {instance.assigned_to.get_full_name()},</p>
            <p>You have been assigned a new ticket:</p>
            <ul>
                <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                <li><strong>Title:</strong> {instance.title}</li>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Project:</strong> {instance.project}</li>
                <li><strong>Updated At:</strong> {instance.updated_at.strftime('%Y-%m-%d')}</li>
            </ul>
            <p>Thank you!</p>
            """
            creator_email = f"""
            <p>Hello {instance.created_by.get_full_name()},</p>
            <p>Your ticket <strong>{instance.title}</strong> has been assigned to {instance.assigned_to.get_full_name()}.</p>
            <ul>
                <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                <li><strong>Title:</strong> {instance.title}</li>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Project:</strong> {instance.project}</li>
                <li><strong>Priority:</strong> {instance.priority}</li>
                <li><strong>Status:</strong> {instance.status.name}</li>
            </ul>
            <p>Thank you!</p>
            """
            # send_mail(subject, strip_tags(html), from_email, [instance.assigned_to.email], html_message=html)
            transaction.on_commit(lambda: send_mail(
                subject,
                strip_tags(html),
                from_email,
                [instance.assigned_to.email],
                html_message=html
            ))
            #   send_mail(subject, strip_tags(creator_email), from_email, [instance.created_by.email], html_message=creator_email)
            transaction.on_commit(lambda: send_mail(
                subject,
                strip_tags(creator_email),
                from_email,
                [instance.created_by.email],
                html_message=creator_email
            ))
            assignment_mail_sent = True
            
            # Also send general update to other recipients (excluding the new assignee and creator)
            if recipient_user:
                update_recipients = [email for email in recipient_user if email != instance.assigned_to.email and email != instance.created_by.email]
                if update_recipients:
                    update_subject = f"Ticket Updated: #{instance.ticket_id} - {instance.title}"
                    update_html = f"""
                    <p>Hello Team,</p>
                    <p>The ticket <strong>{instance.title}</strong> has been updated and assigned to {instance.assigned_to.get_full_name()}.</p>
                    <ul>
                        <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                        <li><strong>Title:</strong> {instance.title}</li>
                        <li><strong>Description:</strong> {instance.description}</li>
                        <li><strong>Status:</strong> {instance.status}</li>
                        <li><strong>Assignee:</strong> {instance.assigned_to.get_full_name()}</li>
                        <li><strong>Created By:</strong> {instance.created_by.get_full_name()}</li>
                        <li><strong>Created At:</strong> {instance.created_at.strftime('%Y-%m-%d')}</li>
                    </ul>
                    <p>Thank you!</p>
                    """
                    # send_mail(update_subject, strip_tags(update_html), from_email, update_recipients, html_message=update_html)
                    transaction.on_commit(lambda: send_mail(
                        update_subject,
                        strip_tags(update_html),
                        from_email,
                        update_recipients,
                        html_message=update_html
                    ))

        # General update
        if not approval_confirmation_sent and not assignment_mail_sent and instance.assigned_to and instance.assigned_to.email:
            recipient_user.append(instance.assigned_to.email)

        # Only send general update email if approval confirmation wasn't sent
        if not approval_confirmation_sent and not assignment_mail_sent:
            if instance.assigned_to and instance.assigned_to.email:
                recipient_user = [email for email in recipient_user if email != instance.assigned_to.email]
                
            subject = f"Ticket Updated: #{instance.ticket_id} - {instance.title}"
            html = f"""
            <p>Hello Team,</p>
            <p>The ticket <strong>{instance.title}</strong> has been updated.</p>
            <ul>
                <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                <li><strong>Title:</strong> {instance.title}</li>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Status:</strong> {instance.status}</li>
                <li><strong>Assignee:</strong> {instance.assigned_to.get_full_name() if instance.assigned_to else 'Unassigned'}</li>
                <li><strong>Created By:</strong> {instance.created_by.get_full_name()}</li>
                <li><strong>Created At:</strong> {instance.created_at.strftime('%Y-%m-%d')}</li>
            </ul>
            <p>Thank you!</p>
            """
            if recipient_user:  # Only send if there are recipients
                # send_mail(subject, strip_tags(html), from_email, recipient_user, html_message=html)
                transaction.on_commit(lambda: send_mail(
                    subject,
                    strip_tags(html),
                    from_email,
                    recipient_user,
                    html_message=html
                ))

        if status_name == "closed":
            subject = f"Ticket Closed: #{instance.ticket_id} - {instance.title}"
            body = f"""
            <p>Hello Team,</p>
            <p>The ticket <strong>{instance.title}</strong> has been closed successfully.</p>
            <ul>
                <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                <li><strong>Title:</strong> {instance.title}</li>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Project:</strong> {instance.project}</li>
                <li><strong>Status:</strong> {instance.status}</li>
                <li><strong>Created By:</strong> {instance.created_by.get_full_name()}</li>
                <li><strong>Created At:</strong> {instance.created_at.strftime('%Y-%m-%d')}</li>
                <li><strong>Closed At:</strong> {instance.updated_at.strftime('%Y-%m-%d')}</li>
            </ul>
            <p>Thank you!</p>
            """
            # send_mail(subject, strip_tags(body), from_email, recipient_user, html_message=body)
            transaction.on_commit(lambda: send_mail(
                subject,
                strip_tags(body),
                from_email,
                recipient_user,
                html_message=body
            ))

        if previous_status == "solved" and status_name != "closed":
            subject = f"Ticket Reopened: #{instance.ticket_id} - {instance.title}"
            body = f"""
            <p>Hello Team,</p>
            <p>The ticket <strong>{instance.title}</strong> has been reopened.</p>
            <ul>
                <li><strong>Ticket ID:</strong> {instance.ticket_id}</li>
                <li><strong>Title:</strong> {instance.title}</li>
                <li><strong>Description:</strong> {instance.description}</li>
                <li><strong>Project:</strong> {instance.project}</li>
                <li><strong>Status:</strong> {instance.status}</li>
                <li><strong>Assignee:</strong> {instance.assigned_to.get_full_name() if instance.assigned_to else 'Unassigned'}</li>
                <li><strong>Updated At:</strong> {instance.updated_at.strftime('%Y-%m-%d')}</li>
            </ul>
            <p>Thank you!</p>
            """
            # send_mail(subject, strip_tags(body), from_email, recipient_user, html_message=body)
            transaction.on_commit(lambda: send_mail(
                subject,
                strip_tags(body),
                from_email,
                recipient_user,
                html_message=body
            ))
