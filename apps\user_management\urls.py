from django.urls import path
from . import views 

urlpatterns = [
    # User
    path('users/create', views.add_user, name='create-user'),
    path('users/', views.get_all_users, name='get-all-user'),
    path('users/page/', views.get_all_users_page, name='get-all-user-page'),
    path('users/<int:id>', views.get_user_by_Id, name='get-user-id'),
    path('users/update/<int:id>', views.update_user, name='update-user'),
    path('users/block_unblock/<int:id>', views.block_unblock_user, name="block-user"),
    path('users/delete/<int:id>', views.delete_user, name="delete-user"),
    path('users/search_and_filter_users', views.search_and_filter_users, name="search_and_filter_users"),
    # Role
    path('users/role/create', views.create_role, name='create-role'),
    path('users/role/', views.get_all_roles, name='get-all-role'),
    path('users/role/<str:role_id>', views.get_role_by_id, name="get-role-id"),
    path('users/role/update/<str:role_id>', views.update_role, name='update-role'),
    # Location
    path('users/location/create', views.create_location, name='create-location'),
    path('users/location/', views.get_all_locations, name='get-all-locations'),
    path('users/location/<str:location_id>', views.get_location_by_id, name='get-location-id'),
    path('users/import-file/', views.import_file, name='import-users-csv'),
    path('users/update/profile_pic/<int:id>', views.update_profile, name='update-profile_picture'),
    # Project
    path('users/project/create', views.create_project, name='create-project'),
    path('users/project/', views.get_projects, name="get-all-project"),
    path('users/project/<int:project_id>', views.get_project_by_id, name="get-project-by-id"),
    path('users/project/userId/<int:user_id>', views.get_projects_by_user, name='get-projects-by-id'),
    path('users/project/update_mapping/<int:project_id>', views.update_project, name='update_project'),
    path('users/project/delete_project_and_tickets', views.delete_project_and_tickets, name='delete_project_and_tickets')
]