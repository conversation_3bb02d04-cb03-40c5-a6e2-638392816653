#!/usr/bin/env python3
import requests
import sys
import argparse
from urllib.parse import urlparse

def check_headers(url):
    """Check security headers for a given URL"""
    print(f"Checking headers for: {url}")
    
    try:
        # Make a request to the URL
        response = requests.get(url, timeout=10)
        
        # Print status code
        print(f"Status Code: {response.status_code}")
        
        # Print all headers
        print("\nAll Headers:")
        for header, value in response.headers.items():
            print(f"{header}: {value}")
        
        # Check for specific security headers
        security_headers = {
            'Cross-Origin-Opener-Policy': 'Missing',
            'Cross-Origin-Embedder-Policy': 'Missing',
            'Cross-Origin-Resource-Policy': 'Missing',
            'Content-Security-Policy': 'Missing',
            'X-Content-Type-Options': 'Missing',
            'X-Frame-Options': 'Missing',
            'Strict-Transport-Security': 'Missing',
            'Referrer-Policy': 'Missing',
            'Permissions-Policy': 'Missing',
        }
        
        # Update with actual values if present
        for header in security_headers:
            if header.lower() in [h.lower() for h in response.headers]:
                for h, v in response.headers.items():
                    if h.lower() == header.lower():
                        security_headers[header] = v
        
        # Print security headers
        print("\nSecurity Headers:")
        for header, value in security_headers.items():
            print(f"{header}: {value}")
        
        # Check if using HTTPS
        parsed_url = urlparse(url)
        if parsed_url.scheme != 'https':
            print("\nWARNING: The URL is not using HTTPS. This may cause security headers to be ignored.")
            print("Consider using HTTPS to ensure security headers are properly enforced.")
        
        # Check for CORS headers
        cors_headers = {
            'Access-Control-Allow-Origin': 'Missing',
            'Access-Control-Allow-Methods': 'Missing',
            'Access-Control-Allow-Headers': 'Missing',
            'Access-Control-Allow-Credentials': 'Missing',
        }
        
        # Update with actual values if present
        for header in cors_headers:
            if header.lower() in [h.lower() for h in response.headers]:
                for h, v in response.headers.items():
                    if h.lower() == header.lower():
                        cors_headers[header] = v
        
        # Print CORS headers
        print("\nCORS Headers:")
        for header, value in cors_headers.items():
            print(f"{header}: {value}")
        
    except requests.exceptions.RequestException as e:
        print(f"Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Check security headers for a URL')
    parser.add_argument('url', help='URL to check')
    args = parser.parse_args()
    
    check_headers(args.url)
