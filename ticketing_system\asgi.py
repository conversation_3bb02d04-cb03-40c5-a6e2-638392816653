"""
ASGI config for ticketing_system project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/howto/deployment/asgi/
"""

import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ticketing_system.settings')
django.setup()

from django.core.asgi import get_asgi_application
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.auth import AuthMiddlewareStack
from apps.websocket.routing import websocket_urlpatterns
from apps.websocket.middleware import CustomWebSocketMiddlewareStack

application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": CustomWebSocketMiddlewareStack(
        URLRouter(websocket_urlpatterns)
    ),
})

