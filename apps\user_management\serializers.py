from rest_framework import serializers
from .models import Project, ProjectMapping, User, Role, Location
from django.utils.timezone import now

class UserSerializer(serializers.ModelSerializer):
    role = serializers.PrimaryKeyRelatedField(queryset=Role.objects.all())
    location = serializers.PrimaryKeyRelatedField(queryset=Location.objects.all())
    profile_pic = serializers.SerializerMethodField()
    class Meta:
        model = User
        fields = '__all__'
        extra_kwargs = {
            'password': {'write_only': True},
            'new_user': {'read_only': True},
        }
    
    def get_full_name(self,obj):
        return f"{obj.first_name} {obj.last_name}"
    # For Profile Picture URL
    def get_profile_pic(self, obj):
        request = self.context.get('request', None)
        if obj.profile_pic:
            if request:
                return request.build_absolute_uri(obj.profile_pic.url)
            else:
                return obj.profile_pic.url  # Relative path fallback
        return None


# class FileUploadSerializer(serializers.Serializer):
#     file = serializers.FileField()
# class SaveFileSerializer(serializers.Serializer):
#     class Meta:
#         model = User
#         fields = '__all__'
#         extra_kwargs = {
#             'password': {'write_only': True},
#         }

class RoleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Role
        fields = ['role_id', 'role_name', 'created_at', 'updated_at', 'created_by', 'updated_by']
        read_only_fields = ['role_id', 'created_at', 'updated_at', 'created_by', 'updated_by']
        extra_kwargs = {
            'created_by': {'required': False, 'allow_null': True},
            'updated_by': {'required': False, 'allow_null': True},
        }


class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = ['location_id', 'location_name', 'created_at', 'updated_at']
        read_only_fields = ['location_id', 'created_at', 'updated_at']    
class ProjectSerializer(serializers.ModelSerializer):
    members = serializers.SerializerMethodField()

    class Meta:
        model = Project
        fields = [
            'id',
            'project_name',
            'project_start_date',
            'project_end_date',
            'project_status',
            'project_manager',
            'members',
            'created_at',
            'updated_at',
            'created_by',
            'updated_by'
        ]

    def get_members(self, obj):
        user_ids = ProjectMapping.objects.filter(project=obj).values_list('user_id', flat=True)
        users = User.objects.filter(id__in=user_ids)
        return UserSerializer(users, many=True).data
    
    def update(self, instance, validated_data):
        # Check if `end_date` is provided in the request
        end_date = validated_data.get('project_end_date', None)
        if end_date:
            # Update the end date and deactivate the project
            instance.project_end_date = end_date
            instance.project_status = False  # Set the project status to inactive
            instance.updated_at = now()
        # Update other fields if any
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
    
    def deactivate(self, removed_by=None):
        self.status = False  # Use False for BooleanField
        self.removed_at = now()
        if removed_by:
            self.removed_by = removed_by
        self.save()


    def activate(self, assigned_by=None):
        """Reactivates the mapping."""
        self.status = True  # Set to Boolean True
        self.removed_at = None
        if assigned_by:
            self.assigned_by = assigned_by
        self.save()
