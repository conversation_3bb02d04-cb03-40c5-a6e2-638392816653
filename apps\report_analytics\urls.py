from django.urls import path
from . import views 

urlpatterns = [
    path('report/count', views.get_tickets_status, name='ticket-count'),
    path('report/tickets/status', views.get_tickets_status, name='tickets'),
    path('report/year_report', views.get_year_report, name='year-report'),
    path('report/location/', views.get_location_report, name='location-report' ),
    path('report/category_report', views.get_category_report, name='category-report'),
    path('report/technician', views.get_technician_status, name='technician-report'),
    path('report/technician/category_report', views.get_technician_category, name='technician-report'),
    path('report/technician/time_report', views.technician_time_report, name='technician-time-report'),
    path('report/feedback/technician', views.technician_feedback, name='technician-feedback'),
    path('report/feedback/category', views.category_feedback, name='category-feedback'),
    path('report/priority', views.priority_report, name="priority-report"),
    path('report/employee_report', views.employee_report, name="employee-report"),
    path('report/export_employee_report', views.get_all_employee_reports, name="export-employee-report")
]