from ast import Module
import time
import os, re, secrets, string
from django.conf import settings
import pandas as pd
from django.core.mail import send_mail
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.utils.timezone import now
from django.db.models import Q
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.contrib.auth.hashers import make_password
from django.db import transaction
from rest_framework.pagination import PageNumberPagination
from apps.authentication.models import Module, RoleModuleAccess
from .models import  Project, ProjectMapping, User, Role, Location
from .serializers import  ProjectSerializer, UserSerializer, RoleSerializer, LocationSerializer
from .permission import UserManagement


def generate_random_password(length=8):
    """Generate a random password of length 'length'."""
    characters = string.ascii_letters + string.digits + string.punctuation
    return ''.join(secrets.choice(characters) for _ in range(length))

def clean_field(value):
    """
    Clean and normalize a field value by stripping whitespace and converting to string.
    """
    return str(value).strip() if pd.notna(value) else None

email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.com$'
japan_phone_regex = r'^81-\d{2}-\d{4}-\d{4}$'
default_phone_regex = r'^\d{1,4}-\d{7,15}$'

def get_paginated_response(paginator, paginated_queryset, serialized_data, message="Data retrieved successfully!"):
    """
    Utility function to generate a paginated response format.
    """
    return Response({
        "count": paginator.page.paginator.count,  # Total records
        "total_pages": paginator.page.paginator.num_pages,  # Total pages
        "current_page": paginator.page.number,  # Current page
        "results": {
            "message": message,
            "data": serialized_data
        }
    })

def get_cleaned_title(field_name, value, row_num, errors):
    value = clean_field(value)
    if not value:
        errors.append(f"'{field_name}' is missing in row {row_num}.")
        return None
    return value.strip().title()

class CustomPagination(PageNumberPagination):
    page_size = 10  
    page_size_query_param = 'page_size'

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_user(request):
    """
    Endpoint to add a new user with optional created_by and updated_by fields.
    """
    data = request.data.copy()  # Make a mutable copy of request.data

    try:
        # Validate Role ID
        role_id = data.get('role')
        if not Role.objects.filter(role_id=role_id).exists():
            return Response(
                {"role": [f"Object with role_id={role_id} does not exist."]},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate Location ID
        location_id = data.get('location')
        if not Location.objects.filter(location_id=location_id).exists():
            return Response(
                {"location": [f"Object with location_id={location_id} does not exist."]},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate Created By ID (if provided)
        created_by = data.get('created_by')
        if created_by and not User.objects.filter(id=created_by).exists():
            return Response(
                {"created_by": [f"User with id={created_by} does not exist."]},
                status=status.HTTP_400_BAD_REQUEST,
            )
        data['created_by'] = created_by or None

        # Validate Updated By ID (if provided)
        updated_by = data.get('updated_by')
        if updated_by and not User.objects.filter(id=updated_by).exists():
            return Response(
                {"updated_by": [f"User with id={updated_by} does not exist."]},
                status=status.HTTP_400_BAD_REQUEST,
            )
        data['updated_by'] = updated_by or None

        # Generate and hash random password
        random_password = generate_random_password()
        data['password'] = make_password(random_password)

        # Add profile picture if provided
        if 'profile_pic' in request.FILES:
            data['profile_pic'] = request.FILES['profile_pic']

        

        # Serialize and save the user
        serializer = UserSerializer(data=data)
        if serializer.is_valid():
            user = serializer.save()

            # Prepare welcome email
            subject = 'NexTicketSys User Login Credentials'
            message = (
                f"Hi {data.get('first_name', '')} {data.get('last_name', '')},\n\n"
                f"Please use the below user name and password to login to GLPI.\n\n"
                f"Username: {data.get('email', '')}\n"
                f"Password: {random_password}\n\n"
                f"Ticketing-URL: {settings.BACKEND_URL}\n\n"
                "Kindly let us know for further related queries."
            )

            try:
                # Send email
                send_mail(
                    subject,
                    message,
                    settings.EMAIL_HOST_USER,  # From email
                    [data.get('email')],  # To email
                    fail_silently=False,
                )
            except Exception as email_error:
                # Rollback user creation if email fails
                return Response(
                    {"error": f"Failed to send email: {str(email_error)}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            return Response(
                {
                    "message": "User added successfully!",
                    "data": serializer.data,
                    "generated_password": random_password,
                },
                status=status.HTTP_201_CREATED,
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_users(request):
    try:
        # Fetch all users who are not marked as deleted
        users = User.objects.filter(is_deleted=False)

        # Serialize the user data to convert it into JSON format
        serializer = UserSerializer(users, many=True)
        user_data = serializer.data

        data = []

        # Add required fields to the response
        for user_data_item, user in zip(user_data, users):
            # Get location and role names
            location_name = None
            role_name = None
            
            if user.location:
                location = Location.objects.filter(location_id=user.location.location_id).first()
                location_name = location.location_name if location else None

            if user.role:
                role = Role.objects.filter(role_id=user.role.role_id).first()
                role_name = role.role_name if role else None

            # Append only the required fields
            data.append({
                "id": user.id,
                "employee_id": user.employee_id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "role_id": user.role_id,
                "role_name": role_name,
                "location_name": location_name,
                "email": user.email,
                "is_active": user.is_active,
                "is_deleted": user.is_deleted,
                "profile_pic": request.build_absolute_uri(user.profile_pic.url) if user.profile_pic else None,
                "phone_number": user.phone_number,
            })

        return Response({"message": "Users retrieved successfully!", "data": data},
                        status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_users_page(request):
    try:
        paginator = CustomPagination()

        # Fetch users with related fields using select_related for optimization
        users = User.objects.filter(is_deleted=False).select_related('location', 'role')

        # Paginate the queryset
        result_page = paginator.paginate_queryset(users, request)

        # Serialize the paginated user data
        serializer = UserSerializer(result_page, many=True)

        # Construct user data manually to include role_name and location_name
        data = [
            {
                "id": user.id,
                "employee_id": user.employee_id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "role_name": user.role.role_name if user.role else None,
                "location_name": user.location.location_name if user.location else None,
                "email": user.email,
                "is_active": user.is_active,
                "is_deleted": user.is_deleted,
                "profile_pic": request.build_absolute_uri(user.profile_pic.url) if user.profile_pic else None,
                "phone_number": user.phone_number,
            }
            for user in result_page
        ]

        # Manually construct the response instead of using `paginator.get_paginated_response`
        return get_paginated_response(paginator, result_page, data, "Users retrieved successfully!")

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_by_Id(request, id):
    try:
        user = User.objects.get(id=id)
        serializer = UserSerializer(user)
        user_data = serializer.data

        location = Location.objects.filter(location_id=user.location.location_id).first()
        location_name = location.location_name if location else None

        role = Role.objects.filter(role_id=user.role.role_id).first()
        role_name = role.role_name if role else None

        #Fetch Project Details
        project_mapping = ProjectMapping.objects.filter(user=user,status=True)
        project_ids = [pm.project.id for pm in project_mapping if pm.project]
        projects = Project.objects.filter(id__in=project_ids)

        # Format project details
        project_data = [
            {
                "project_id": project.id,
                "project_name": project.project_name
            }
            for project in projects
        ]
        
        data = {
            "id": user.id,
                "employee_id": user.employee_id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "role_name": role_name,
                "location_name": location_name,
                "email": user.email,
                "is_active": user.is_active,
                "is_deleted": user.is_deleted,
                "profile_pic": request.build_absolute_uri(user.profile_pic.url) if user.profile_pic else None,
                "phone_number": user.phone_number,
                "projectDetails": project_data
        }


        #Add the full URL for profile_pic if it exists
        if user.profile_pic:
            user_data['profile_pic'] = request.build_absolute_uri(user.profile_pic.url)


        return Response({"message": "User retrieved successfully!", "data":data},
                        status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({"error": f"User with ID {id} not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)   

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_user(request, id):
    try:
        user = User.objects.get(id=id)
        data = request.data
        print("Data", data)

        # Validate `updated_by` field
        updated_by = data.get('updated_by')
        if not updated_by:
            return Response(
                {"error": "'updated_by' field is mandatory."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if `updated_by` user exists
        if not User.objects.filter(id=updated_by).exists():
            return Response(
                {"error": f"User with ID {updated_by} does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Update password if provided
        if 'password' in data:
            data['password'] = make_password(data['password'])

        # Check if a profile_pic is provided in the request
        profile_pic = request.FILES.get("profile_pic")
        
        if profile_pic:
            print("Profile pic uploaded:", profile_pic.name)
            # Remove old profile pic
            if user.profile_pic:
                old_image_path = os.path.join(settings.MEDIA_ROOT, str(user.profile_pic))
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)
                    
            # Assign new profile pic
            user.profile_pic = profile_pic
            user.save(update_fields=["profile_pic"])
        
        else:           
            if user.profile_pic:
                old_image_path = os.path.join(settings.MEDIA_ROOT, str(user.profile_pic))
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)
                    
                user.profile_pic = None
                user.save(update_fields=["profile_pic"])

        # Use partial update to allow updating only some fields
        serializer = UserSerializer(user, data=data, partial=True)

        if serializer.is_valid():
            # Save the updated user data
            serializer.save(updated_by_id=updated_by)
            return Response(
                {"message": "User updated successfully!", "data": serializer.data},
                status=status.HTTP_200_OK,
            )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except User.DoesNotExist:
        return Response({"error": f"User with ID {id} not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def block_unblock_user(request, id):
    try:
        # Fetch the user, ensuring they are not soft-deleted
        user = User.objects.get(id=id, is_deleted=False)

        # Validate the `updated_by` field
        updated_by = request.data.get('updated_by')
        if not updated_by:
            return Response(
                {"error": "'updated_by' field is mandatory."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if the `updated_by` user exists
        if not User.objects.filter(id=updated_by).exists():
            return Response(
                {"error": f"User with ID {updated_by} does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get the action parameter from the request
        action = request.data.get('action', '').lower()
        if action not in ['block', 'unblock']:
            return Response({"error": "Invalid action. Use 'block' or 'unblock'."}, status=status.HTTP_400_BAD_REQUEST)

        if action == 'block':
            if not user.is_active:
                return Response({"message": "User is already blocked!"}, status=status.HTTP_400_BAD_REQUEST)
            user.is_active = False
            user.updated_by_id = updated_by  # Set updated_by field
            user.save()
            return Response({"message": "User blocked successfully!"}, status=status.HTTP_200_OK)

        if action == 'unblock':
            if user.is_active:
                return Response({"message": "User is already active!"}, status=status.HTTP_400_BAD_REQUEST)
            user.is_active = True
            user.updated_by_id = updated_by  # Set updated_by field
            user.save()
            return Response({"message": "User unblocked successfully!"}, status=status.HTTP_200_OK)

    except User.DoesNotExist:
        return Response({"error": f"User with ID {id} not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
   
@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def delete_user(request, id):
    try:
        # Fetch the user, ensuring they are not soft-deleted
        user = User.objects.get(id=id, is_deleted=False)

        # Validate the `updated_by` field
        updated_by = request.data.get('updated_by')
        if not updated_by:
            return Response(
                {"error": "'updated_by' field is mandatory."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if the `updated_by` user exists
        if not User.objects.filter(id=updated_by).exists():
            return Response(
                {"error": f"User with ID {updated_by} does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Mark the user as deleted and set updated_by
        user.is_deleted = True
        user.is_active = False
        user.updated_by_id = updated_by  # Set the updated_by field
        user.save()

        # Update ProjectMapping: Set `is_active` = False, `removed_at`, `removed_by`
        ProjectMapping.objects.filter(user_id=id, status=True).update(
            status=False,
            removed_at=now(),
            removed_by=updated_by
        )

        return Response({"message": "User deleted successfully!"}, status=status.HTTP_200_OK)

    except User.DoesNotExist:
        return Response({"error": f"User with ID {id} not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def search_and_filter_users(request):
    try:
        paginator = CustomPagination()

        # Retrieve query parameters
        search_query = request.GET.get('search', '').strip()  # For Employee ID or Name
        status_filter = request.GET.get('status', '').strip()  # For Active/Inactive filter
        role_filter = request.GET.get('role', '').strip()  # For Role filter
        location_filter = request.GET.get('location', '').strip()  # For Location filter

        # Build the query for filtering and searching
        query = Q(is_deleted=False)
        if search_query:
            query &= (
                Q(employee_id__icontains=search_query) |
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query)
            )
        if status_filter:
            query &= Q(is_active=(status_filter == 'Active'))
        if role_filter:
            query &= Q(role_id=role_filter)
        if location_filter:
            query &= Q(location_id=location_filter)

        # Optimize query by using select_related
        users = User.objects.filter(query).select_related('location', 'role')

        # Paginate the queryset
        result_page = paginator.paginate_queryset(users, request)

        # Construct user data manually to include role_name and location_name
        data = [
            {
                "id": user.id,
                "employee_id": user.employee_id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "role_name": user.role.role_name if user.role else None,
                "location_name": user.location.location_name if user.location else None,
                "email": user.email,
                "is_active": user.is_active,
                "is_deleted": user.is_deleted,
                "profile_pic": request.build_absolute_uri(user.profile_pic.url) if user.profile_pic else None,
                "phone_number": user.phone_number,
            }
            for user in result_page
        ]

        return get_paginated_response(paginator, result_page, data, "Users retrieved successfully!")

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_role(request):
    """
    Create a new role
    """
    try:
        role_data = request.data

        # Check if role_name already exists
        role_name = role_data.get('role_name')
        if Role.objects.filter(role_name=role_name).exists():
            return Response(
                {"error": f"Role with name {role_name} already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate `created_by` and `updated_by` (if provided)
        created_by = role_data.get('created_by')
        updated_by = role_data.get('updated_by')

        if created_by and not User.objects.filter(id=created_by).exists():
            return Response(
                {"error": f"User with ID {created_by} does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if updated_by and not User.objects.filter(id=updated_by).exists():
            return Response(
                {"error": f"User with ID {updated_by} does not exist."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Serialize and save the role
        serializer = RoleSerializer(data=role_data)
        if serializer.is_valid():
            # Save the role object first
            role = serializer.save()

            # Set created_by and updated_by explicitly
            if created_by:
                role.created_by_id = created_by
            if updated_by:
                role.updated_by_id = updated_by
            role.save()

            # Set all modules' status to False for the new role
            modules = Module.objects.all()
            for module in modules:
                RoleModuleAccess.objects.create(
                    role=role,
                    module=module,
                    has_access=False
                )

            return Response(
                {"message": "Role created successfully!", "data": serializer.data},
                status=status.HTTP_201_CREATED,
            )

        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_roles(request):
    """
    Retrieve all roles.
    """
    start_time = time.monotonic() 
    try:
        roles = Role.objects.all()
        serializer = RoleSerializer(roles, many=True)
        end_time = time.monotonic()
        elapsed_ms = (end_time - start_time) * 1000  # Convert to ms

        # Format to 2 decimal places and append 'ms'
        elapsed_str = f"{elapsed_ms:.2f} ms"
        return Response({"message": "Roles retrieved successfully!", "data":serializer.data, "elapsed": elapsed_str}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_role_by_id(request, role_id):
    """
    Retrieve a role by ID.
    """
    try:
        role = Role.objects.get(role_id=role_id)
        serializer = RoleSerializer(role)
        return Response({"message": "Role retrieved successfully!", "data":serializer.data}, status=status.HTTP_200_OK)
    except Role.DoesNotExist:
        return Response({"error": f"Role with ID {role_id} not found"}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_role(request, role_id):
    """
    Update an existing role by its ID
    """
    try:
        # Retrieve the role to be updated
        role = Role.objects.filter(role_id=role_id).first()
        if not role:
            return Response(
                {"error": f"Role with ID '{role_id}' does not exist."},
                status=status.HTTP_404_NOT_FOUND,
            )

        role_data = request.data

        # Validate role_name uniqueness (if being updated)
        new_role_name = role_data.get("role_name")
        if new_role_name and new_role_name != role.role_name:
            if Role.objects.filter(role_name=new_role_name).exists():
                return Response(
                    {"error": f"Role with name '{new_role_name}' already exists."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Serialize the data and update the role
        serializer = RoleSerializer(role, data=role_data, partial=True)  # Use partial=True for partial updates
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Role updated successfully!", "data": serializer.data}, status=status.HTTP_200_OK)

        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_location(request):
    try:
        location_data = request.data

        #Validate Location is already exits
        location_name = location_data.get('location_name')
        if Location.objects.filter(location_name=location_name).exists():
            return Response({"error": f"Location with name '{location_name}' already exists."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Serialize the data and create a new location
        serializer = LocationSerializer(data=location_data)
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Location created successfully!", "data":serializer.data}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_locations(request):
    try:
        locations = Location.objects.all()
        serializer = LocationSerializer(locations, many=True)
        return Response({"message": "Locations retrieved successfully!", "data":serializer.data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_location_by_id(request, location_id):
    try:
        location = Location.objects.get(location_id=location_id)
        serializer = LocationSerializer(location)
        return Response({"message": "Location retrieved successfully!", "data":serializer.data}, status=status.HTTP_200_OK)
    except Location.DoesNotExist:
        return Response({"error": f"Location with ID {location_id} not found"}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_location(request, location_id):
    """
    Update an existing location by its ID
    """
    try:
        # Retrieve the location to be updated
        location = Location.objects.filter(location_id=location_id).first()
        if not location:
            return Response(
                {"error": f"Location with ID '{location_id}' does not exist."},
                status=status.HTTP_404_NOT_FOUND,
            )

        location_data = request.data

        # Validate location_name uniqueness (if being updated)
        new_location_name = location_data.get("role_name")
        if new_location_name and new_location_name != location.location_name:
            if Location.objects.filter(location_name=new_location_name).exists():
                return Response(
                    {"error": f"Role with name '{new_location_name}' already exists."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # Serialize the data and update the location
        serializer = RoleSerializer(location, data=location_data, partial=True)  # Use partial=True for partial updates
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Role updated successfully!", "data": serializer.data}, status=status.HTTP_200_OK)

        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_profile(request, id):
    try:
        #Gets User Details
        user = User.objects.get(id=id)
        data = request.data

        #User Uplaod a profile picture
        if 'profile_pic' in request.FILES:
            #Remove old Picture if exits
            if user.profile_pic:
                old_image_path = os.path.join(settings.MEDIA_ROOT, str(user.profile_pic))
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)
                
            #set new profile picture
            data['profile_pic'] = request.FILES['profile_pic']
            user.profile_pic = data['profile_pic']
        
        elif 'profile_pic' in data and data['profile_pic'] == '':
            #Remove Picture if exits
            if user.profile_pic:
                old_image_path = os.path.join(settings.MEDIA_ROOT, str(user.profile_pic))
                if os.path.exists(old_image_path):
                    os.remove(old_image_path)
            
            user.profile_pic = None
        
        user.updated_at =now()
        user.save()
            
        return Response({'message': 'Profile Picture uploaded successfully'}, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def import_file(request):
    try:
        created_by = request.data.get('created_by')
        updated_by = request.data.get('updated_by')

        if not created_by:
            return Response({"error": "'created_by' field is mandatory."}, status=status.HTTP_400_BAD_REQUEST)
        if not updated_by:
            return Response({"error": "'updated_by' field is mandatory."}, status=status.HTTP_400_BAD_REQUEST)
        if not User.objects.filter(id=created_by).exists():
            return Response({"error": f"User with ID {created_by} (created_by) does not exist."}, status=status.HTTP_400_BAD_REQUEST)
        if not User.objects.filter(id=updated_by).exists():
            return Response({"error": f"User with ID {updated_by} (updated_by) does not exist."}, status=status.HTTP_400_BAD_REQUEST)
        if 'file' not in request.FILES:
            return Response({"error": "No file provided"}, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']
        if not file.name.endswith(('.xls', '.xlsx')):
            return Response({"error": "Invalid file format. Only .xls and .xlsx are supported."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            file_data = pd.read_excel(file)
        except Exception as e:
            return Response({"error": f"Failed to read the file: {str(e)}"}, status=status.HTTP_400_BAD_REQUEST)

        if file_data.empty:
            return Response({'error': 'File is empty'}, status=status.HTTP_400_BAD_REQUEST)

        required_columns = ['Employee ID', 'First Name', 'Last Name', 'Email', 'Is Active', 'Location', 'Role', 'Phone Number']
        missing_columns = [col for col in required_columns if col not in file_data.columns]
        if missing_columns:
            return Response({"error": f"Missing columns in the file: {', '.join(missing_columns)}."}, status=status.HTTP_400_BAD_REQUEST)

        roles = {role.role_name.strip(): role for role in Role.objects.all()}
        locations = {location.location_name.strip(): location for location in Location.objects.all()}
        existing_employee_ids = set(User.objects.values_list('employee_id', flat=True))
        existing_emails = set(User.objects.values_list('email', flat=True))

        employee_ids_in_sheet = set()
        emails_in_sheet = set()
        validated_users = []
        email_queue = []
        errors = []

        for index, row in file_data.iterrows():
            if row.isnull().all():
                continue

            row_num = index + 1
            employee_id = clean_field(row.get('Employee ID'))
            first_name = get_cleaned_title('First Name', row.get('First Name'), row_num, errors)
            last_name = get_cleaned_title('Last Name', row.get('Last Name'), row_num, errors)
            email = clean_field(row.get('Email'))
            is_active = clean_field(row.get('Is Active'))
            location_name = clean_field(row.get('Location'))
            role_name = clean_field(row.get('Role'))
            phone_number = clean_field(row.get('Phone Number'))

            # Required fields check
            for field, value in [('Employee ID', employee_id), ('Email', email), ('Is Active', is_active), ('Location', location_name), ('Role', role_name), ('Phone Number', phone_number)]:
                if not value:
                    errors.append(f"'{field}' is missing in row {row_num}.")

            if is_active:
                is_active = is_active.lower()
                if is_active in ['true', 'yes']:
                    is_active = True
                elif is_active in ['false', 'no']:
                    is_active = False
                else:
                    errors.append(f"Invalid value for 'Is Active' in row {row_num}. Expected 'true', 'yes', 'false', or 'no'.")
            else:
                errors.append(f"'Is Active' is missing in row {row_num}.")

            if role_name not in roles:
                errors.append(f"Invalid role '{role_name}' in row {row_num}.")
            if location_name not in locations:
                errors.append(f"Invalid location '{location_name}' in row {row_num}.")
            if not re.match(email_regex, email):
                errors.append(f"Invalid email format in row {row_num} for email {email}.")

            if location_name == "NexGen-Japan":
                if not re.match(japan_phone_regex, phone_number):
                    errors.append(f"Invalid phone number format for NexGen-Japan in row {row_num}. Expected format: +81-xx-xxxx-xxxx")
            else:
                if not re.match(default_phone_regex, phone_number):
                    errors.append(f"Invalid phone number format in row {row_num}. Expected format: +<country_code>-<number>")

            if employee_id in employee_ids_in_sheet:
                errors.append(f"Duplicate Employee ID '{employee_id}' found in the sheet at row {row_num}.")
            else:
                employee_ids_in_sheet.add(employee_id)

            if email in emails_in_sheet:
                errors.append(f"Duplicate Email '{email}' found in the sheet at row {row_num}.")
            else:
                emails_in_sheet.add(email)

            if employee_id in existing_employee_ids:
                errors.append(f"Employee ID '{employee_id}' already exists in the database.")
            if email in existing_emails:
                errors.append(f"Email '{email}' already exists in the database.")

            if errors:
                continue

            random_password = generate_random_password()
            validated_users.append({
                "employee_id": employee_id,
                "first_name": first_name,
                "last_name": last_name,
                "email": email,
                "is_active": is_active,
                "location": locations[location_name],
                "role": roles[role_name],
                "phone_number": f"+{phone_number}",
                "password": random_password
            })
            email_queue.append({
                "email": email,
                "first_name": first_name,
                "last_name": last_name,
                "password": random_password
            })

        if errors:
            return Response({'error': '\n'.join(errors)}, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            for user_data in validated_users:
                new_user = User(
                    employee_id=user_data['employee_id'],
                    first_name=user_data['first_name'],
                    last_name=user_data['last_name'],
                    email=user_data['email'],
                    is_active=user_data['is_active'],
                    location=user_data['location'],
                    password=make_password(user_data['password']),
                    role=user_data['role'],
                    phone_number=user_data['phone_number'],
                    created_by_id=created_by,
                    updated_by_id=updated_by,
                )
                new_user.save()

        for data in email_queue:
            try:
                send_mail(
                    subject="NexTicketSys User Login Credentials",
                    message=f"Hi {data['first_name']} {data['last_name']},\n\nPlease use the below username and password to login.\n\nUsername: {data['email']}\nPassword: {data['password']}\n\nTicketing-URL: {settings.BACKEND_URL}\n\nKindly reach out for further queries.\n",
                    from_email=settings.EMAIL_HOST_USER,
                    recipient_list=[data['email']],
                    fail_silently=False,
                )
            except Exception as e:
                print(f"Failed to send email to {data['email']}: {e}")

        return Response({"message": "File imported successfully."}, status=status.HTTP_200_OK)

    except ValueError as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])  # Uncomment if authentication is required
def create_project(request):
    """
    Create a new project.
    """
    try:
        project_data = request.data

        # Validate `created_by` and `updated_by` fields
        created_by = project_data.get('created_by')
        updated_by = project_data.get('updated_by')

        if not created_by or not updated_by:
            return Response({"error": "Both 'created_by' and 'updated_by' are required."}, status=status.HTTP_400_BAD_REQUEST)

        # Check if `created_by` and `updated_by` users exist
        if not User.objects.filter(id=created_by).exists():
            return Response({"error": f"User with ID {created_by} (created_by) does not exist."},
                            status=status.HTTP_400_BAD_REQUEST)

        if not User.objects.filter(id=updated_by).exists():
            return Response({"error": f"User with ID {updated_by} (updated_by) does not exist."},
                            status=status.HTTP_400_BAD_REQUEST)

        # Assign `created_by` and `updated_by` to project_data
        project_data['created_by'] = created_by
        project_data['updated_by'] = updated_by

        # Initialize serializer without a request context
        serializer = ProjectSerializer(data=project_data)

        if serializer.is_valid():
            # Save the project
            project = serializer.save()

            # Add the project manager to the ProjectMapping table
            project_manager = project.project_manager
            if project_manager:
                ProjectMapping.objects.create(
                    project=project,
                    user=project_manager,
                    status=True,  # Active status for the manager
                    assigned_by=User.objects.get(id=created_by)
                )

            # Process members if provided
            members = project_data.get('members', [])
            for member_id in members:
                user = User.objects.filter(id=member_id).first()
                if user and user != project_manager:  # Avoid duplicating the manager in members
                    ProjectMapping.objects.create(
                        project=project,
                        user=user,
                        status=True,  # Active status for members
                        assigned_by=User.objects.get(id=created_by)
                    )

            return Response(
                {"message": "Project created successfully!", "data": serializer.data},
                status=status.HTTP_201_CREATED,
            )

        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_projects(request):
    """
    Get all projects.
    """
    try:
        #Pagination
        paginator = CustomPagination()
        projects = Project.objects.select_related('project_manager').all()
        paginated_projects = paginator.paginate_queryset(projects, request)
        
        # Fetch all mappings for the projects
        project_mappings = ProjectMapping.objects.select_related('user', 'user__role').filter(
            project_id__in=[p.id for p in paginated_projects]
        )

        # Serialize project data
        serializer = ProjectSerializer(paginated_projects, many=True)
        project_data = serializer.data

        # Prepare a mapping of project_id to members
        project_members_map = {}
        for mapping in project_mappings:
            project_id = mapping.project_id
            
            # Check project status and decide if the member should be included
            project_status = next((p.project_status for p in paginated_projects if p.id == project_id), None)
            if project_status == 1 and mapping.status == 0:
                continue  # Skip inactive members for active projects

            if project_id not in project_members_map:
                project_members_map[project_id] = []

            user = mapping.user
            project_manager = next((p.project_manager for p in projects if p.id == project_id), None)
            if user.id == project_manager.id if project_manager else False:
                continue

            project_members_map[project_id].append({
                "id": user.id,
                "employee_id": user.employee_id,
                "full_name": f"{user.first_name} {user.last_name}",
                "role": user.role.role_name if user.role else None,
                "email": user.email,
                "profile_pic": request.build_absolute_uri(user.profile_pic.url) if user.profile_pic else None,
                "status": mapping.status,  # Include status for debugging or frontend use
            })

        # Attach members to each project and include project manager's full name
        for project in project_data:
            project_id = project['id']
            project['members'] = project_members_map.get(project_id, [])

            # Add project manager's details separately
            project_manager = next((p.project_manager for p in projects if p.id == project_id), None)
            project['project_manager'] = {
                "id": project_manager.id if project_manager else None,
                "full_name": f"{project_manager.first_name} {project_manager.last_name}" if project_manager else None,
                "email": project_manager.email if project_manager else None
            }

        
        return get_paginated_response(paginator, paginated_projects, project_data, "Projects retrieved successfully!")

    except Exception as e:
        # Log the exception for debugging
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_project_by_id(request, project_id):
    """
    Get project by ID.
    """
    try:
        # Retrieve the project by ID
        project = Project.objects.select_related('project_manager').filter(id=project_id).first()
        if not project:
            return Response({"error": "Project not found."}, status=status.HTTP_404_NOT_FOUND)

        # Serialize the project
        serializer = ProjectSerializer(project)
        project_data = serializer.data

        # Add project_manager details as a nested object
        if project.project_manager:
            project_data['project_manager'] = {
                "id": project.project_manager.id,
                "employee_id": project.project_manager.employee_id,
                "full_name": f"{project.project_manager.first_name} {project.project_manager.last_name}",
                "email": project.project_manager.email,
            }
        else:
            project_data['project_manager'] = None

        # Fetch active members only (excluding inactive and deleted users)
        members = ProjectMapping.objects.filter(
            project_id=project.id,
            status=True  # Active members only
        )  # Exclude deleted users

        # Serialize members
        project_data['members'] = [
            {
                "id": member.user.id,
                "employee_id": member.user.employee_id,
                "full_name": f"{member.user.first_name} {member.user.last_name}",
                "role": member.user.role.role_name if member.user.role else None,
                "email": member.user.email,
                "status": member.status,  # Include status in the response
            }
            for member in members
        ]

        return Response({"data": project_data}, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
@api_view(['GET'])
@permission_classes([IsAuthenticated])  
def get_projects_by_user(request, user_id):
    """
    Get all projects associated with a specific user, either as a project manager or member.
    """
    try:
        # Retrieve projects where the user is the project manager
        managed_projects = Project.objects.filter(project_manager_id=user_id)

        # Retrieve projects where the user is a member (via ProjectMapping)
        member_projects = Project.objects.filter(
            id__in=ProjectMapping.objects.filter(user_id=user_id,status=True).values_list('project_id', flat=True)
        )
        print("MEMBER:", member_projects)

        # Combine and remove duplicates if the user is both manager and member of the same project
        projects = (managed_projects | member_projects).distinct()

        if not projects.exists():
            return Response({"error": "No projects found for the specified user."}, status=status.HTTP_404_NOT_FOUND)
        
        # Implement pagination
        paginator = CustomPagination()
        paginated_projects = paginator.paginate_queryset(projects, request)

        # Serialize projects
        serialized_projects = []
        for project in projects:
            serializer = ProjectSerializer(project)
            project_data = serializer.data

            # Check if the user is the project manager
            is_project_manager = project.project_manager_id == user_id

            # Add project_manager details as a nested object
            if project.project_manager:
                project_data['project_manager'] = {
                    "id": project.project_manager.id,
                    "employee_id": project.project_manager.employee_id,
                    "full_name": f"{project.project_manager.first_name} {project.project_manager.last_name}",
                    "email": project.project_manager.email,
                }
            else:
                project_data['project_manager'] = None

            # Fetch members based on the project's status
            if project.project_status:  # If the project is active
                members = ProjectMapping.objects.filter(
                    project_id=project.id,
                    status=True  # Fetch only active members
                ).select_related('user')
            else:  # If the project is inactive
                members = ProjectMapping.objects.filter(
                    project_id=project.id  # Fetch all members regardless of status
                ).select_related('user')

            # Serialize members
            project_data['members'] = [
                {
                    "id": member.user.id,
                    "employee_id": member.user.employee_id,
                    "full_name": f"{member.user.first_name} {member.user.last_name}",
                    "role": member.user.role.role_name if member.user.role else None,
                    "email": member.user.email,
                    "status": member.status,  # Include status in the response
                }
                for member in members
            ]

            # Add is_project_manager flag to the response
            project_data['is_project_manager'] = is_project_manager

            serialized_projects.append(project_data)

        return get_paginated_response(paginator, paginated_projects, serialized_projects, "Projects retrieved successfully!")

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_project(request, project_id):
    try:
        # Retrieve the project
        project = Project.objects.filter(id=project_id).first()
        if not project:
            return Response(
                {"error": f"Project with ID '{project_id}' does not exist."},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Validate and update the project manager
        project_manager = request.data.get("project_manager")
        updated_by = request.data.get("updated_by")
        if project_manager:
            if not isinstance(project_manager, int):
                return Response(
                    {"error": "Project manager should be provided as a valid user ID."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if not User.objects.filter(id=project_manager).exists():
                return Response(
                    {"error": f"User with ID '{project_manager}' does not exist."},
                    status=status.HTTP_404_NOT_FOUND,
                )
            project.project_manager_id = project_manager
            project.updated_by_id = updated_by
            project.save()

        # Validate and process members
        new_members = request.data.get("members", [])
        if not isinstance(new_members, list):
            return Response(
                {"error": "Members should be provided as a list of user IDs."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Convert members to integer IDs and ensure project_manager is included
        try:
            new_members = list(map(int, new_members))  # Convert to integers
            if project_manager and project_manager not in new_members:
                new_members.append(project_manager)  # Append project manager to members list
        except ValueError:
            return Response(
                {"error": "Members should be provided as a list of valid user IDs."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get current active members
        current_active_members = list(
            ProjectMapping.objects.filter(
                project_id=project_id, status=True
            ).values_list('user_id', flat=True)
        )

        # Determine users to add and remove
        users_to_add = set(new_members) - set(current_active_members)
        users_to_remove = set(current_active_members) - set(new_members)

        # Deactivate old members
        for user_id in users_to_remove:
            mapping = ProjectMapping.objects.filter(
                project_id=project_id, user_id=user_id, status=True
            ).first()
            if mapping:
                mapping.status = False
                mapping.removed_at = now()
                mapping.removed_by = request.user if request.user.is_authenticated else None
                mapping.save()

        # Add new members or activate existing ones
        for user_id in users_to_add:
            existing_mapping = ProjectMapping.objects.filter(
                project_id=project_id, user_id=user_id
            ).first()

            if existing_mapping:
                # Reactivate the existing record
                existing_mapping.status = True
                existing_mapping.removed_at = None
                existing_mapping.removed_by = None
                existing_mapping.assigned_at = now()
                existing_mapping.assigned_by = request.user if request.user.is_authenticated else None
                existing_mapping.save()
            else:
                # Create a new record
                ProjectMapping.objects.create(
                    project=project,
                    user_id=user_id,
                    status=True,
                    assigned_at=now(),
                    assigned_by=request.user if request.user.is_authenticated else None
                )

        # Check if all members are inactive
        active_mappings = ProjectMapping.objects.filter(project_id=project_id, status=True)
        if not active_mappings.exists():
            project.project_status = False
            project.project_end_date = now()
            project.save()

        return Response(
            {"message": f"Project members and manager for Project '{project.project_name}' updated successfully!"},
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)