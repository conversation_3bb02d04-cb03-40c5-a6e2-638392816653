amqp==5.3.1
APScheduler==3.11.0
asgiref==3.8.1
attrs==25.3.0
autobahn==24.4.2
Automat==25.4.16
billiard==4.2.1
CacheControl==0.14.3
cachetools==5.5.2
celery==5.5.2
certifi==2025.4.26
cffi==1.17.1
channels==4.2.2
charset-normalizer==3.4.2
click==8.2.0
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
constantly==23.10.4
cron-descriptor==1.4.5
cryptography==44.0.3
daphne==4.2.0
distlib==0.3.9
Django==5.2.1
django-celery-beat==2.8.1
django-cors-headers==4.7.0
django-crontab==0.7.1
django-storages==1.14.6
django-timezone-field==7.1
djangorestframework==3.16.0
djangorestframework-simplejwt==5.5.0
drf-yasg==1.21.10
dnspython==2.7.0
et_xmlfile==2.0.0
eventlet==0.40.0
filelock==3.18.0
firebase-admin==6.8.0
google-api-core==2.24.2
google-api-python-client==2.169.0
google-auth==2.40.1
google-auth-httplib2==0.2.0
google-cloud-core==2.4.3
google-cloud-firestore==2.20.2
google-cloud-storage==3.1.0
google-crc32c==1.7.1
google-resumable-media==2.7.2
googleapis-common-protos==1.70.0
greenlet==3.2.2
grpcio==1.71.0
grpcio-status==1.71.0
httplib2==0.22.0
hyperlink==21.0.0
idna==3.10
incremental==24.7.2
kombu==5.5.3
msgpack==1.1.0
# MySQL database adapters - at least one is required
#PyMySQL==1.1.1  # Pure Python MySQL client (recommended for Python 3.11+)
mysqlclient==2.2.7 - #Native MySQL client (install separately if needed)
numpy==2.2.5
openpyxl==3.1.5
packaging==25.0
pandas==2.2.3
pillow==11.2.1
pipenv==2025.0.2
platformdirs==4.3.8
prompt_toolkit==3.0.51
proto-plus==1.26.1
protobuf>=5.26.1,<6.0dev
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
PyJWT==2.6.0
pyOpenSSL==25.0.0
pyparsing==3.2.3
python-crontab==3.2.0
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.1.0
pytz==2025.2
redis==6.1.0
requests==2.32.3
rsa==4.9.1
service-identity==24.2.0
setuptools==80.7.1
six==1.17.0
SQLAlchemy==2.0.41
sqlparse==0.5.3
Twisted==24.11.0
txaio==23.1.1
typing_extensions==4.13.2
tzdata>=2025.2
tzlocal==5.3.1
unidecode>=1.3.0  # Universal Unicode to ASCII conversion for file uploads
uritemplate==4.1.1
urllib3==2.4.0
vine==5.1.0
virtualenv==20.31.2
wcwidth==0.2.13
zope.interface==7.2
