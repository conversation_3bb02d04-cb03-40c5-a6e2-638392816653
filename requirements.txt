amqp==5.3.1
APScheduler==3.11.0
asgiref==3.8.1
attrs==25.1.0
autobahn==24.4.2
Automat==24.8.1
billiard==4.2.1
CacheControl==0.14.2
cachetools==5.5.1
celery==5.4.0
certifi==2024.12.14
cffi==1.17.1
channels==4.2.0
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
constantly==23.10.4
cron-descriptor==1.4.5
cryptography==44.0.1
daphne==4.1.2
distlib==0.3.9
Django==5.1.6
django-celery-beat==2.7.0
django-cors-headers==4.6.0
django-crontab==0.7.1
django-storages==1.14.4
django-timezone-field==7.1
djangorestframework==3.15.2
djangorestframework-simplejwt==5.3.1
dnspython==2.7.0
et_xmlfile==2.0.0
eventlet==0.39.0
filelock==3.16.1
firebase-admin==6.6.0
google-api-core==2.24.1
google-api-python-client==2.160.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.1
google-cloud-firestore==2.20.0
google-cloud-storage==3.0.0
google-crc32c==1.6.0
google-resumable-media==2.7.2
googleapis-common-protos==1.67.0
greenlet==3.1.1
grpcio==1.70.0
grpcio-status==1.70.0
httplib2==0.22.0
hyperlink==21.0.0
idna==3.10
incremental==24.7.2
kombu==5.4.2
msgpack==1.1.0
mysqlclient==2.2.6
numpy==2.2.1
openpyxl==3.1.5
packaging==24.2
pandas==2.2.3
pillow==11.1.0
pipenv==2024.4.0
platformdirs==4.3.6
prompt_toolkit==3.0.50
proto-plus==1.26.0
protobuf==5.29.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
PyJWT==2.10.1
pyOpenSSL==25.0.0
pyparsing==3.2.1
python-crontab==3.2.0
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.1.0
pytz==2024.2
redis==5.2.1
requests==2.32.3
rsa==4.9
service-identity==24.2.0
setuptools==75.6.0
six==1.17.0
SQLAlchemy==2.0.38
sqlparse==0.5.3
Twisted==24.11.0
txaio==23.1.1
typing_extensions==4.12.2
tzdata==2024.2
tzlocal==5.2
uritemplate==4.1.1
urllib3==2.3.0
vine==5.1.0
virtualenv==20.28.0
wcwidth==0.2.13
zope.interface==7.2
