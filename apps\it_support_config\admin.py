from django.contrib import admin 
from .models import  User
# from .models import Feedback, Ticket, User


# @admin.register(Ticket)
# class TicketAdmin(admin.ModelAdmin):
#     list_display = ('ticket_id', 'created_at')  
#     search_fields = ('ticket_id',)  


@admin.register(User)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ('employee_id',)  
    search_fields = ('employee_id',)  


# @admin.register(Feedback)
# class FeedbackAdmin(admin.ModelAdmin):
#     list_display = ('ticket', 'employee', 'feedback_value', 'created_at')  
#     list_filter = ('feedback_value', 'created_at')
#     search_fields = ('ticket__ticket_id', 'employee__id')
