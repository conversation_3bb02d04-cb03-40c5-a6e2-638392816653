from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from rest_framework.decorators import action
from rest_framework.pagination import PageNumberPagination
from apps.user_management.models import User
from .models import Category, SubCategory
from .serializers import CategorySerializer, SubCategorySerializer
from apps.ticket_management.models import Ticket
from django.db.models import Q
from rest_framework.permissions import IsAuthenticated

class CustomPagination(PageNumberPagination):
    page_size = 10  # Default page size
    page_size_query_param = 'page_size'

    def get_paginated_response(self, data):
        """
        Returns paginated response with total pages and current page.
        """
        return Response({
            "count": self.page.paginator.count,  # Total records
            "total_pages": self.page.paginator.num_pages,  # Total pages
            "current_page": self.page.number,  # Current page
            "results": data  # Paginated data
        })

class CategoryViewSet(viewsets.ModelViewSet):
    permission_classes=[IsAuthenticated]
    queryset = Category.objects.all()
    serializer_class = CategorySerializer
    pagination_class = CustomPagination

    def create(self, request, *args, **kwargs):
        """
        Create a category and its subcategory in a single request.
        """
        category_name = request.data.get("cat_name")
        subcategory_name = request.data.get("subcat_name")
        created_by_id = request.data.get("created_by")
        updated_by_id = request.data.get('updated_by')

        if not category_name:
            return Response({"error": "Category name is required."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if the category exists
        if category_name:
            if Category.objects.filter(cat_name=category_name).exists():
                raise ValidationError({
                    "error": f'Category "{category_name}" already exists.'
                })
            category, created = Category.objects.get_or_create(cat_name=category_name, created_by_id=created_by_id, updated_by_id=updated_by_id)
        else:
            category = None

        subcategory = SubCategory.objects.create(subcat_name=subcategory_name, category=category, created_by_id=created_by_id, updated_by_id=updated_by_id)

        return Response({
            "message": "Category and Subcategory created successfully.",
            "category": CategorySerializer(category).data,
            "subcategory": SubCategorySerializer(subcategory).data if subcategory else None
        }, status=status.HTTP_201_CREATED)

    def list(self, request, *args, **kwargs):
        """
        GET API to return only active categories with their associated active subcategories.
        """
        categories = Category.objects.filter().order_by("id")  # Ensure Ordered Queryset

        # Apply Pagination
        paginator = self.pagination_class()
        result_page = paginator.paginate_queryset(categories, request, view=self)  # Paginate data

        category_data = []

        for category in result_page:  # Iterate over paginated data
            subcategories = SubCategory.objects.filter(category=category)
            subcategory_serializer = SubCategorySerializer(subcategories, many=True)            

            category_data.append({
                "id": category.id,
                "name": category.cat_name,
                "is_active": category.is_active,
                "subcategoriesCount": subcategories.count(),
                "subcategories": subcategory_serializer.data,
                "created_by_fullname": f"{category.created_by.first_name} {category.created_by.last_name}",
                "updated_by_fullname": f"{category.updated_by.first_name} {category.updated_by.last_name}"  
            })

        # Return Paginated Response
        return paginator.get_paginated_response(category_data)
    
    @action(detail=False, methods=["get"], url_path="without-pagination")
    def list_without_pagination(self, request):
        """
        GET API to return only active categories with their associated active subcategories.
        This method does NOT apply pagination.
        """
        categories = Category.objects.filter(is_active=True).order_by("id")  # Ordered Queryset

        category_data = []

        for category in categories:  # No pagination here
            subcategories = SubCategory.objects.filter(category=category)
            subcategory_serializer = SubCategorySerializer(subcategories, many=True)            

            category_data.append({
                "id": category.id,
                "name": category.cat_name,
                "is_active": category.is_active,
                "subcategoriesCount": subcategories.count(),
                "subcategories": subcategory_serializer.data,
                "created_by_fullname": f"{category.created_by.first_name} {category.created_by.last_name}",
                "updated_by_fullname": f"{category.updated_by.first_name} {category.updated_by.last_name}"  
            })

        return Response(category_data, status=status.HTTP_200_OK)

    def update(self, request, *args, **kwargs):
        category = self.get_object()
        category_name = request.data.get('cat_name')
        updated_by_id = request.data.get('updated_by')
        active_subcategory_ids = request.data.get("existing_subcategories", [])  # Fix here

        # Validate updated_by user
        try: 
            updated_by = User.objects.get(id=updated_by_id)
        except User.DoesNotExist:
            return Response({"error": "Invalid updated_by user ID."}, status=status.HTTP_400_BAD_REQUEST)

        # Validate category_name uniqueness
        if category_name and Category.objects.filter(cat_name=category_name).exclude(id=category.id).exists():
            raise ValidationError({'error': f'Category "{category_name}" already exists.'})

        # Update category name if provided
        if category_name:
            category.cat_name = category_name

        # Update the "updated_by" field
        category.updated_by = updated_by
        category.save()

        # Handle active subcategory updates
        if not isinstance(active_subcategory_ids, list):
            return Response({"error": "A list of subcategory IDs is required."}, status=status.HTTP_400_BAD_REQUEST)

        # Get all subcategories under this category
        subcategories = SubCategory.objects.filter(category=category)

        # Set is_active=False only for subcategories NOT in the provided list
        subcategories.exclude(id__in=active_subcategory_ids).update(is_active=False)

        # Set is_active=True only for subcategories in the provided list
        subcategories.filter(id__in=active_subcategory_ids).update(is_active=True)

        # Handle new subcategory creation if provided
        subcategory_name = request.data.get('subcat_name')
        if subcategory_name:
            new_subcategory, created = SubCategory.objects.get_or_create(
                category=category,
                subcat_name=subcategory_name,
                defaults={"updated_by": updated_by}
            )
            if not created:
                new_subcategory.updated_by = updated_by
                new_subcategory.save()
        else:
            new_subcategory = None

        return Response({
            'message': 'Subcategory updated successfully.',
            'category': {
                'id': category.id,
                'cat_name': category.cat_name,
                'updated_by': category.updated_by.id,
            },
            'new_subcategory': {
                'id': new_subcategory.id,
                'subcat_name': new_subcategory.subcat_name,
                'updated_by': new_subcategory.updated_by.id
            } if new_subcategory else None
        }, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        """
        Deactivate the entire category and all its subcategories.
        """
        category = self.get_object()
        category.is_active = False
        category.save()

        return Response({
            'message': 'Category and all its subcategories have been deactivated.'
        }, status=status.HTTP_204_NO_CONTENT)
    
    @action(detail=True, methods=["post"], url_path="activate")
    def activate(self, request, *args, **kwargs):
        """
        Activate the category and all its subcategories.
        """
        category = self.get_object()
        category.is_active = True
        category.save()

        return Response(
        {"message": "Category and all its subcategories have been activated."},
        status=status.HTTP_200_OK
    )

    # @action(detail=True, methods=['post'])
    # def update_active_subcategories(self, request, pk=None):
    #     """
    #     Keep only the provided subcategories active, deactivate others under the given category.
    #     """
    #     active_subcategory_ids = request.data.get("subcategory_ids", [])

    #     if not isinstance(active_subcategory_ids, list):
    #         return Response(
    #             {"error": "A list of subcategory IDs is required."},
    #             status=status.HTTP_400_BAD_REQUEST,
    #             )

    #     # Get all subcategories under this category
    #     subcategories = SubCategory.objects.filter(category_id=pk)

    #     # Set is_active=False for subcategories not in the provided list
    #     deactivated_count = subcategories.exclude(id__in=active_subcategory_ids).update(is_active=False)

    #     # Set is_active=True for subcategories in the provided list
    #     activated_count = subcategories.filter(id__in=active_subcategory_ids).update(is_active=True)

    #     return Response(
    #         {
    #             "message": "subcategory has been updated."
    #         },
    #         status=status.HTTP_200_OK,
    #     )
