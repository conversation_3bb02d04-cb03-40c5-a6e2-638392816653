import json
import base64
import os
import logging
from django.core.files.base import ContentFile
from django.db import transaction
from channels.generic.websocket import AsyncWebsocketConsumer
from asgiref.sync import sync_to_async
from django.conf import settings

from apps.ticket_management.models import Ticket, TicketChatAttachenment
from .models import TicketChatMessage
from apps.user_management.models import User

logger = logging.getLogger(__name__)

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.ticket_id = self.scope["url_route"]["kwargs"]["room_name"]
        self.room_group_name = f"ticket_{self.ticket_id}"
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

    async def receive(self, text_data):
        try:
            # Step 1: Parse the incoming data
            data = json.loads(text_data)
            message = data.get("message", "").strip()
            sender_id = data.get("sender_id")
            attachement = data.get("attachement", [])  # Expecting a list of {file_data, file_name}

            # **Log the raw received data**
            logger.info(f"Received WebSocket message: {data}")

            # Validate sender ID
            if not sender_id:
                logger.error("Sender ID missing in WebSocket message.")
                return

            # Step 2: Store the message in the database
            chat_message = await self.store_message(sender_id, message)
            if not chat_message:
                logger.error(f"Failed to store message for ticket {self.ticket_id}")
                return

            # **Log the attachment before processing**
            logger.info(f"Received attachments: {attachement}")

            # Step 3: Handle file attachments
            file_urls = []
            if isinstance(attachement, list) and attachement:
                for file in attachement:
                    file_data = file.get("file_data")
                    file_name = file.get("file_name")

                    # Log file data presence
                    if not file_data or not file_name:
                        logger.error("File data or file name missing in attachment.")
                        continue

                    # Store the file and get the URL
                    url = await self.store_file(sender_id, file_data, file_name, chat_message)

                    # Check if the file was saved correctly
                    if url:
                        logger.info(f"File stored successfully: {url}")
                        file_urls.append({"file_url": url, "file_name": file_name})
                    else:
                        logger.error(f"Failed to store file: {file_name}")

            # Step 4: Send the response to WebSocket group
            response = {
                "message": message,
                "sender_id": sender_id,
                "attachement": file_urls,
                "timestamp": chat_message.timestamp.isoformat(),
            }

            # Log the final response
            logger.info(f"Sending WebSocket message: {response}")

            await self.channel_layer.group_send(self.room_group_name, {
                "type": "chat_message",
                **response
            })

        except Exception as e:
            logger.error(f"Error in WebSocket message processing: {e}")

    async def chat_message(self, event):
        await self.send(text_data=json.dumps(event))

    @sync_to_async
    def store_message(self, sender_id, message):
        try:
            with transaction.atomic():
                sender = User.objects.filter(id=sender_id).first()
                ticket = Ticket.objects.filter(ticket_id=self.ticket_id).first()

                if not ticket or not sender:
                    logger.error(f"Invalid sender ({sender_id}) or ticket ({self.ticket_id})")
                    return None

                return TicketChatMessage.objects.create(ticket=ticket, sender=sender, message=message)
        except Exception as e:
            logger.error(f"Error storing message: {e}")
            return None

    @sync_to_async
    def store_file(self, sender_id, file_data, file_name, chat_message):
        try:
            sender = User.objects.filter(id=sender_id).first()
            ticket = Ticket.objects.filter(ticket_id=self.ticket_id).first()

            if not (ticket and chat_message):
                logger.error("Failed to store file: Invalid ticket or chat message.")
                return None

            # Validate base64 data
            if not file_data.startswith("data:"):
                logger.error(f"Invalid file data format: {file_data[:30]}...")  # Log the start of the data
                return None

            try:
                file_type, imgstr = file_data.split(";base64,")
                if not imgstr:
                    logger.error("Base64 data extraction failed.")
                    return None
            except Exception as e:
                logger.error(f"Error extracting file data: {e}")
                return None

            # Construct the file path
            file_path = f"chat_uploads/{file_name}"
            full_path = os.path.join(settings.MEDIA_ROOT, file_path)

            # Save the file
            try:
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                with open(full_path, "wb") as f:
                    f.write(base64.b64decode(imgstr))
                logger.info(f"File saved at {full_path}")
            except Exception as e:
                logger.error(f"Error saving file: {e}")
                return None

            # Store attachment record
            try:
                attachment = TicketChatAttachenment.objects.create(
                    ticket=ticket, attachement=file_path, chat=chat_message
                )
                return settings.MEDIA_URL + file_path
            except Exception as e:
                logger.error(f"Error saving file record to database: {e}")
                return None

        except Exception as e:
            logger.error(f"Unexpected error in store_file: {e}")
            return None

active_connections = {}  # Simple in-memory store
print("Active Connections:", active_connections)
class ForceLogoutConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.user_id = str(self.scope["url_route"]["kwargs"]["user_id"])
        self.group_name = f"user_{self.user_id}"

        # Add to group and accept connection
        await self.channel_layer.group_add(self.group_name, self.channel_name)
        await self.accept()

        # Store connection for debugging
        active_connections[self.user_id] = self
        print(f"✅ User {self.user_id} connected to force logout WebSocket")
        print(f"📊 Active connections: {list(active_connections.keys())}")

    async def disconnect(self, close_code):
        # Remove from group and active connections
        await self.channel_layer.group_discard(self.group_name, self.channel_name)

        if self.user_id in active_connections:
            del active_connections[self.user_id]

        print(f"❌ User {self.user_id} disconnected from force logout WebSocket")
        print(f"📊 Active connections: {list(active_connections.keys())}")

    async def receive(self, text_data):
        # Handle any incoming messages (currently not used)
        try:
            data = json.loads(text_data)
            print(f"📨 Received message from user {self.user_id}: {data}")
        except json.JSONDecodeError:
            print(f"⚠️ Invalid JSON received from user {self.user_id}: {text_data}")

    async def force_logout(self, event):
        """Handle force logout message from the backend"""
        try:
            print(f"🔴 Sending logout to user {self.user_id}")

            # Send logout message to the frontend
            from datetime import datetime
            await self.send(text_data=json.dumps({
                "action": "logout",
                "message": "Your role has been changed. Please log in again.",
                "timestamp": datetime.now().isoformat()
            }))

            print(f"✅ Force logout message sent successfully to user {self.user_id}")

        except Exception as e:
            print(f"❌ Error sending force logout to user {self.user_id}: {e}")
            logger.error(f"Error in force_logout for user {self.user_id}: {e}")
