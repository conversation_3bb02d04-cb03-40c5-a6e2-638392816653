import json
import base64
import os
import logging
import unicodedata
from django.core.files.base import ContentFile
from django.db import transaction
from channels.generic.websocket import AsyncWebsocketConsumer
from asgiref.sync import sync_to_async
from django.conf import settings

from apps.ticket_management.models import Ticket, TicketChatAttachenment
from apps.ticket_management.views import sanitize_filename
from .models import TicketChatMessage
from apps.user_management.models import User

logger = logging.getLogger(__name__)

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.ticket_id = self.scope["url_route"]["kwargs"]["room_name"]
        self.room_group_name = f"ticket_{self.ticket_id}"
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            message = data.get("message", "").strip()
            sender_id = data.get("sender_id")
            attachments = data.get("attachement", [])

            logger.info(f"Received WebSocket message: {data}")

            if not sender_id:
                logger.error("Sender ID missing in WebSocket message.")
                return

            chat_message = await self.store_message(sender_id, message)
            if not chat_message:
                logger.error(f"Failed to store message for ticket {self.ticket_id}")
                return

            file_urls = []
            if isinstance(attachments, list) and attachments:
                for file in attachments:
                    file_data = file.get("file_data")
                    file_name = file.get("file_name")

                    if not file_data or not file_name:
                        logger.error("Missing file data or file name.")
                        continue

                    url = await self.store_file(sender_id, file_data, file_name, chat_message)
                    if url:
                        file_urls.append({"file_url": url, "file_name": file_name})
                    else:
                        logger.error(f"Failed to store file: {file_name}")

            response = {
                "message": message,
                "sender_id": sender_id,
                "attachement": file_urls,
                "timestamp": chat_message.timestamp.isoformat(),
            }

            logger.info(f"Sending WebSocket message: {response}")
            await self.channel_layer.group_send(self.room_group_name, {
                "type": "chat_message",
                **response
            })

        except Exception as e:
            logger.error(f"Error in WebSocket message processing: {e}")

    async def chat_message(self, event):
        await self.send(text_data=json.dumps(event))

    @sync_to_async
    def store_message(self, sender_id, message):
        try:
            with transaction.atomic():
                sender = User.objects.filter(id=sender_id).first()
                ticket = Ticket.objects.filter(ticket_id=self.ticket_id).first()

                if not ticket or not sender:
                    logger.error(f"Invalid sender ({sender_id}) or ticket ({self.ticket_id})")
                    return None

                return TicketChatMessage.objects.create(ticket=ticket, sender=sender, message=message)
        except Exception as e:
            logger.error(f"Error storing message: {e}")
            return None

    @sync_to_async
    def store_file(self, sender_id, file_data, file_name, chat_message):
        try:
            sender = User.objects.filter(id=sender_id).first()
            ticket = Ticket.objects.filter(ticket_id=self.ticket_id).first()

            if not (ticket and chat_message):
                logger.error("Failed to store file: Invalid ticket or chat message.")
                return None

            if not file_data.startswith("data:"):
                logger.error(f"Invalid file data format: {file_data[:30]}...")
                return None

            try:
                file_type, imgstr = file_data.split(";base64,")
                if not imgstr:
                    logger.error("Base64 data extraction failed.")
                    return None
            except Exception as e:
                logger.error(f"Error extracting file data: {e}")
                return None

            # Normalize and encode filename
            normalized_name = unicodedata.normalize('NFKC', file_name)
            base, ext = os.path.splitext(normalized_name)

            if not base or len(base) < 3:
                base = "file"

            # Use URL-safe path for saving
            import urllib.parse
            safe_name = urllib.parse.quote(normalized_name, safe='')
            file_path = f"chat_uploads/{safe_name}"
            full_path = os.path.join(settings.MEDIA_ROOT, file_path)

            try:
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                with open(full_path, "wb") as f:
                    f.write(base64.b64decode(imgstr))
                logger.info(f"File saved at {full_path}")
            except Exception as e:
                logger.error(f"Error saving file: {e}")
                return None

            try:
                TicketChatAttachenment.objects.create(
                    ticket=ticket,
                    attachement=file_path,
                    original_filename=normalized_name + "_ticket_chat_id_" + str(ticket.ticket_id),
                    chat=chat_message
                )
                return settings.MEDIA_URL + file_path
            except Exception as e:
                logger.error(f"Error saving file record to database: {e}")
                return None

        except Exception as e:
            logger.error(f"Unexpected error in store_file: {e}")
            return None

active_connections = {}  # Simple in-memory store
print("Active Connections:", active_connections)
class ForceLogoutConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        try:
            self.user_id = str(self.scope["url_route"]["kwargs"]["user_id"])
            self.group_name = f"user_{self.user_id}"

            print(f"🔌 Attempting to connect user {self.user_id} to force logout WebSocket")

            # Add to group and accept connection
            await self.channel_layer.group_add(self.group_name, self.channel_name)
            await self.accept()

            # Store connection for debugging
            active_connections[self.user_id] = self
            print(f"✅ User {self.user_id} connected to force logout WebSocket")
            print(f"📊 Active connections: {list(active_connections.keys())}")

        except Exception as e:
            print(f"❌ Error connecting user {self.user_id}: {e}")
            await self.close()

    async def disconnect(self, close_code):
        # Log the disconnect reason
        disconnect_reasons = {
            1000: "Normal closure",
            1001: "Going away",
            1002: "Protocol error",
            1003: "Unsupported data",
            1006: "Abnormal closure",
            1011: "Server error",
            1012: "Service restart",
            1013: "Try again later",
            1014: "Bad gateway",
            1015: "TLS handshake failure"
        }

        reason = disconnect_reasons.get(close_code, f"Unknown reason ({close_code})")
        print(f"❌ User {self.user_id} disconnected from force logout WebSocket - Reason: {reason}")

        # Remove from group and active connections
        try:
            await self.channel_layer.group_discard(self.group_name, self.channel_name)
        except Exception as e:
            print(f"⚠️ Error removing from group: {e}")

        if hasattr(self, 'user_id') and self.user_id in active_connections:
            del active_connections[self.user_id]

        print(f"📊 Active connections: {list(active_connections.keys())}")

    async def receive(self, text_data):
        # Handle any incoming messages (currently not used)
        try:
            data = json.loads(text_data)
            print(f"📨 Received message from user {self.user_id}: {data}")
        except json.JSONDecodeError:
            print(f"⚠️ Invalid JSON received from user {self.user_id}: {text_data}")

    async def force_logout(self, event):
        """Handle force logout message from the backend"""
        try:
            user_id = event.get('user_id', self.user_id)
            old_role = event.get('old_role', 'Unknown')
            new_role = event.get('new_role', 'Unknown')

            print(f"🔴 Received force logout for user {user_id} (role change: {old_role} → {new_role})")
            print(f"🔴 Sending logout to user {self.user_id}")

            # Send logout message to the frontend
            from datetime import datetime
            await self.send(text_data=json.dumps({
                "action": "logout",
                "message": f"Your role has been changed from {old_role} to {new_role}. Please log in again.",
                "timestamp": datetime.now().isoformat(),
                "old_role": old_role,
                "new_role": new_role
            }))

            print(f"✅ Force logout message sent successfully to user {self.user_id}")

            # Don't close the connection immediately, let the frontend handle it
            # await self.close()

        except Exception as e:
            print(f"❌ Error sending force logout to user {self.user_id}: {e}")
            logger.error(f"Error in force_logout for user {self.user_id}: {e}")
