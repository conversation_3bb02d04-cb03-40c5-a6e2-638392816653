from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.hashers import check_password

from apps.user_management.models import User

class EmailBackend(BaseBackend):
    def authenticate(self, request, email=None, password=None, **kwargs):
        try:
            user = User.objects.get(email=email)
            if check_password(password, user.password):  # Ensure password is hashed
                return user
        except User.DoesNotExist:
            return None
        return None

    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
