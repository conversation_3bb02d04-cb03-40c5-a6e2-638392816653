from datetime import datetime as dt
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.permissions import IsAuthenticated 
from rest_framework.decorators import api_view, permission_classes
from apps.ticket_management.models import Status, Ticket, Feedback, PriorityLevel, StatusTracking
from apps.user_management.models import Location, Project, ProjectMapping, User, Role
from apps.it_support_config.models import Category, SubCategory
from django.db.models import OuterRef, Subquery, F, IntegerField, Q
from django.db.models import Sum, ExpressionWrapper, F, DurationField
from django.db.models import Count
import datetime
import calendar


months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

# Helper function to format the time duration
def format_duration(duration):
    if not duration:
        return '0'

    # Get the total number of seconds from the timedelta
    seconds = int(duration.total_seconds())

    # Calculate hours and minutes
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60

    # Return in the desired format
    if hours > 0 and minutes > 0:
        return f"{hours}:{minutes}"  # e.g., '40hr 30m'
    elif hours > 0:
        return f"{hours}" #e.g., '12hr'
    else:
        return f"0:{minutes}"  # e.g., '40min'

def get_employee_report_data(start_date, end_date, status=None):
    try:
        # Convert start_date and end_date to datetime objects if they are strings
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d").date()
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d").date()

    except ValueError:
        return {"error": "Invalid date format. Use YYYY-MM-DD."}, False  # Return error response

    # Fetch users based on status filter (if provided)
    users_query = User.objects.all()
    if status == "2":  # Active
        users_query = users_query.filter(is_active=True)
    elif status == "3":  # Inactive
        users_query = users_query.filter(Q(is_active=False) & Q(is_deleted=False))
    elif status == "4":
        users_query = users_query.filter(is_deleted=True)
    else:
        users_query = User.objects.all()

    users_data = []
    total_tickets = 0

    for user in users_query:
        # Filter tickets within the date range and created by the user
        tickets = Ticket.objects.filter(
            created_by_id=user.id,
            created_at__date__range=[start_date, end_date]
        )
        ticket_count = tickets.count()
        total_tickets += ticket_count

        users_data.append({
            "id": user.id,
            "employee_id": user.employee_id,
            "name": f"{user.first_name} {user.last_name}",
            "ticket_count": ticket_count,
        })

    # Sort employees by ticket count (highest first)
    users_data = sorted(users_data, key=lambda x: x['ticket_count'], reverse=True)

    return {"data": users_data, "total_tickets": total_tickets}, True

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_tickets_status(request):
    try:
        # Get role and user ID from request body
        role = request.query_params.get('role')
        user_id = request.query_params.get('user_id')

        if not role or not user_id:
            return Response({
                "message": "Role and user_id are required parameters",
                "error": "Missing parameters"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Common function to count tickets with filters
        def count_tickets_with_status(queryset, status_name):
            return queryset.filter(status__name=status_name).count()

        # Base queryset filtering based on user ID
        if role in ['R001', 'R002']:  # Super Admin
            tickets = Ticket.objects.all()

        elif role == 'R006':  # Support Agent
            tickets = Ticket.objects.filter(Q(assigned_to_id=user_id) | Q(created_by_id=user_id))  

        elif role == 'R003':  # Project Manager
            managed_projects = Project.objects.filter(project_manager_id=user_id)
            project_ids = managed_projects.values_list('id', flat=True)
            
            # Get team member IDs from ProjectMapping for these projects
            team_member_ids = ProjectMapping.objects.filter(
                project_id__in=project_ids
            ).values_list('user_id', flat=True).distinct()

            # Get tickets created by the user only
            tickets = Ticket.objects.filter(Q(created_by_id=user_id) | Q(assigned_to_id=user_id))
            # Get tickets created by team members
            team_members_tickets = Ticket.objects.filter(created_by_id__in=team_member_ids)
            # Count the number of team members
            team_members_count = len(team_member_ids)
            # Count the number of team members
            team_members_count = len(team_member_ids)

        elif role in ['R004', 'R005']:  # Other roles
            tickets = Ticket.objects.filter(created_by_id=user_id)

        else:
            return Response({
                "message": "Invalid role provided",
                "error": "Role not recognized"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Build tickets data dictionary based on user role
        tickets_data = {
            'total_tickets': tickets.count(),
            'unassigned_tickets': tickets.filter(assigned_to_id__isnull=True).count(),
            'pending_tickets': tickets.filter(
                Q(status__name="In Progress") & Q(assigned_to_id__isnull=False)
            ).count(),
            'solved_tickets': count_tickets_with_status(tickets, "Solved"),
            'closed_tickets': count_tickets_with_status(tickets, "Closed"),
        }

        # Additional fields for R001, R002 and R006
        if role in ['R001', 'R002']:
            tickets_data.update({
                'assigned_tickets': count_tickets_with_status(tickets, 'assigned'),
                'awaiting_approval': count_tickets_with_status(tickets, 'Awaiting Approval'),
                'awaiting_response': count_tickets_with_status(tickets, 'Awaiting User Response'),
                'under_observer': count_tickets_with_status(tickets, 'Under Observer'),
            })

        # Additional field for R003 (Project Manager)
        if role == 'R003':
            tickets_data.update({
                'pending_approval': tickets.filter(assigned_to_id=user_id).count(),
                'team_members_count': team_members_count - tickets.count(),
                'team_members_ticket_count': team_members_tickets.count(),
                })

        return Response({
            "message": "Tickets data fetched successfully",
            "data": tickets_data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            "message": "Error fetching tickets data",
            "error": str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_year_report(request):
    try:
        role = request.query_params.get('role')
        user_id = request.query_params.get('user_id')
        year_param = request.query_params.get('year')
        month_param = request.query_params.get('month')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')

        if not role or not user_id:
            return Response({
                "message": "Role and user_id are required parameters",
                "error": "Missing parameters"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Determine date range and grouping_mode
        if year_param:
            year = int(year_param)
            if month_param:
                month = int(month_param)
                start_date = datetime.date(year, month, 1)
                end_date = datetime.date(year, month, calendar.monthrange(year, month)[1])
                grouping_mode = "month"
            else:
                start_date = datetime.date(year, 1, 1)
                end_date = datetime.date(year, 12, 31)
                grouping_mode = "year"
        elif start_date_str and end_date_str:
            start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d').date()
            if start_date.year != end_date.year:
                grouping_mode = "custom_year"
            elif start_date.month != end_date.month:
                grouping_mode = "custom_month"
            else:
                grouping_mode = "custom_day" if start_date.isocalendar()[1] == end_date.isocalendar()[1] else "custom_week"
        else:
            return Response({
                "message": "Either provide a year (with optional month) OR start_date and end_date",
                "error": "Missing parameters"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Consolidate base filters
        if role in ['R001', 'R002']:
            base_filter = Q(created_at__date__gte=start_date, created_at__date__lte=end_date)
        elif role  == 'R006':
            base_filter = Q(assigned_to_id=user_id, created_at__date__gte=start_date, created_at__date__lte=end_date)
        else:
            base_filter = Q(created_by_id=user_id, created_at__date__gte=start_date, created_at__date__lte=end_date)

        # Build subqueries using the correct field name for the Ticket's primary key (assumed "ticket_id")
        latest_status_subquery = StatusTracking.objects.filter(
            ticket_id=OuterRef('ticket_id')
        ).order_by('-updated_at').values('current_status_id')[:1]

        latest_updated_at_subquery = StatusTracking.objects.filter(
            ticket_id=OuterRef('ticket_id')
        ).order_by('-updated_at').values('updated_at')[:1]

        previous_status_subquery = StatusTracking.objects.filter(
            ticket_id=OuterRef('ticket_id'),
            updated_at__lt=Subquery(latest_updated_at_subquery)
        ).order_by('-updated_at').values('current_status_id')[:1]

        # Annotate tickets with both latest_status and prev_status in one query.
        annotated_tickets = Ticket.objects.filter(base_filter).annotate(
            latest_status=Subquery(latest_status_subquery, output_field=IntegerField()),
            prev_status=Subquery(previous_status_subquery, output_field=IntegerField())
        )

        report_data = []

        # Helper function to calculate counts in a given bucket
        def get_bucket_counts(queryset):
            solved_count = queryset.filter(latest_status=6).count()
            closed_count = queryset.filter(latest_status=7, prev_status=6).count()
            solved_total = solved_count + closed_count
            open_count = queryset.count()
            return open_count, solved_total, closed_count

        if grouping_mode in ["year", "custom_year"]:
            for yr in range(start_date.year, end_date.year + 1):
                bucket_start = datetime.date(yr, 1, 1) if yr != start_date.year else start_date
                bucket_end = datetime.date(yr, 12, 31) if yr != end_date.year else end_date
                bucket_tickets = annotated_tickets.filter(created_at__date__gte=bucket_start, created_at__date__lte=bucket_end)
                open_count, solved_total, closed_count = get_bucket_counts(bucket_tickets)
                report_data.append({
                    'period': str(yr),
                    'tickets_count': {
                        'open': open_count,
                        'solved': solved_total,  # solved count now includes closed if previous status was 6
                        'closed': closed_count
                    }
                })

        elif grouping_mode in ["custom_month"]:
            current_date_iter = start_date
            while current_date_iter <= end_date:
                last_day = calendar.monthrange(current_date_iter.year, current_date_iter.month)[1]
                bucket_start = current_date_iter
                bucket_end = datetime.date(current_date_iter.year, current_date_iter.month, last_day)
                if bucket_end > end_date:
                    bucket_end = end_date
                label = bucket_start.strftime('%b %Y')  # e.g., "Apr 2025"
                bucket_tickets = annotated_tickets.filter(created_at__date__gte=bucket_start, created_at__date__lte=bucket_end)
                open_count, solved_total, closed_count = get_bucket_counts(bucket_tickets)
                report_data.append({
                    'period': label,
                    'tickets_count': {
                        'open': open_count,
                        'solved': solved_total,
                        'closed': closed_count
                    }
                })
                # Move to the first day of the next month.
                if current_date_iter.month == 12:
                    current_date_iter = datetime.date(current_date_iter.year + 1, 1, 1)
                else:
                    current_date_iter = datetime.date(current_date_iter.year, current_date_iter.month + 1, 1)

        elif grouping_mode == "custom_week":
            cal = calendar.monthcalendar(start_date.year, start_date.month)
            week_number = 1
            for week in cal:
                week_days = [day for day in week if day != 0]
                if not week_days:
                    continue
                week_start = datetime.date(start_date.year, start_date.month, min(week_days))
                week_end = datetime.date(start_date.year, start_date.month, max(week_days))
                if week_end < start_date or week_start > end_date:
                    continue
                bucket_start = max(week_start, start_date)
                bucket_end = min(week_end, end_date)
                label = f"Week {week_number} ({bucket_start.strftime('%d')}-{bucket_end.strftime('%d')})"
                bucket_tickets = annotated_tickets.filter(created_at__date__gte=bucket_start, created_at__date__lte=bucket_end)
                open_count, solved_total, closed_count = get_bucket_counts(bucket_tickets)
                report_data.append({
                    'period': label,
                    'tickets_count': {
                        'open': open_count,
                        'solved': solved_total,
                        'closed': closed_count
                    }
                })
                week_number += 1

        elif grouping_mode == "custom_day":
            current_date_iter = start_date
            while current_date_iter <= end_date:
                bucket_tickets = annotated_tickets.filter(created_at__date=current_date_iter)
                open_count, solved_total, closed_count = get_bucket_counts(bucket_tickets)
                report_data.append({
                    'period': current_date_iter.strftime('%Y-%m-%d'),
                    'tickets_count': {
                        'open': open_count,
                        'solved': solved_total,
                        'closed': closed_count
                    }
                })
                current_date_iter += datetime.timedelta(days=1)

        final_data = {
            'grouping_mode': grouping_mode,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'ticket_count': annotated_tickets.count(),
            'data': report_data
        }
        return Response({'message': 'Ticket report retrieved successfully', 'data': final_data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_monthly_report(request, year, month):
    try:
        # Filter tickets by year and month using the `__year` and `__month` look
        tickets = Ticket.objects.filter(created_at__year=year, created_at__month=month)
        # Filter tickets by status
        open_tickets = tickets.filter(Q(status='open') | Q(status='inProgress'))
        solved_tickets = tickets.filter(status='solved')
        closed_tickets = tickets.filter(status='closed')
        # Create a dictionary to store the report data
        report_data = {
            'open_tickets': open_tickets.count(),
            'solved_tickets': solved_tickets.count(),
            'closed_tickets': closed_tickets.count(),
            'total_tickets': tickets.count(),
        }
        return Response({'message': f'Monthly ticket report for {year}-{month} retrieved successfully', "data": report_data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_location_report(request):
    try:
        # Get the 'year' parameter from the request
        year = request.GET.get('year')

        # Get all locations
        locations = Location.objects.all()

        location_data = []

        # Loop through each location and get the count of tickets for that location
        for location in locations:
            # Filter tickets by location and optionally by year
            tickets_query = Ticket.objects.filter(location_id=location.location_id)

            if year:
                try:
                    # Parse year to an integer
                    year = int(year)
                    tickets_query = tickets_query.filter(
                        Q(created_at__year=year)  # Assuming `created_at` is the datetime field in the Ticket model
                    )
                except ValueError:
                    return Response({"error": "Invalid year format"}, status=status.HTTP_400_BAD_REQUEST)

            # Count tickets for the current query
            tickets_count = tickets_query.count()

            location_data.append({
                'location_name': location.location_name,
                'total_tickets': tickets_count,
            })

        return Response({"message": "Data fetched successfully", "data": location_data}, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_category_report(request, year=None):
    try:
        role = request.query_params.get('role')
        user_id = request.query_params.get('user_id')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')

        if not role or not user_id:
            return Response({
                "message": "Role and user_id are required parameters",
                "error": "Missing parameters"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Determine date range: use start_date and end_date if provided, else fallback to year
        if start_date_str and end_date_str:
            start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d').date()
        else:
            if year is None:
                return Response({
                    "message": "Either provide start_date and end_date or a valid year",
                    "error": "Missing parameters"
                }, status=status.HTTP_400_BAD_REQUEST)
            start_date = datetime.date(year, 1, 1)
            end_date = datetime.date(year, 12, 31)
        
        # Filter tickets based on role and date range
        if role in ['R001', 'R002']:  # Admin
            tickets = Ticket.objects.filter(
                Q(created_at__date__range=[start_date, end_date]) |
                Q(solved_at__date__range=[start_date, end_date]) |
                Q(closed_at__date__range=[start_date, end_date])
            )
        elif role == 'R006':  # Support Agent
            tickets = Ticket.objects.filter(
                Q(assigned_to_id=user_id) &
                (
                    Q(created_at__date__range=[start_date, end_date]) |
                    Q(solved_at__date__range=[start_date, end_date]) |
                    Q(closed_at__date__range=[start_date, end_date])
                )
            )
        elif role == 'R003':  # Project Manager
            tickets = Ticket.objects.filter(
                Q(created_by_id=user_id) &
                (
                    Q(created_at__date__range=[start_date, end_date]) |
                    Q(solved_at__date__range=[start_date, end_date]) |
                    Q(closed_at__date__range=[start_date, end_date])
                )
            )
        elif role in ['R004', 'R005']:  # Other roles
            tickets = Ticket.objects.filter(
                Q(created_by_id=user_id) &
                (
                    Q(created_at__date__range=[start_date, end_date]) |
                    Q(solved_at__date__range=[start_date, end_date]) |
                    Q(closed_at__date__range=[start_date, end_date])
                )
            )
        else:
            return Response({
                "message": "Invalid role provided",
                "error": "Role not recognized"
            }, status=status.HTTP_400_BAD_REQUEST)

        category_data = []
        categories = Category.objects.all()
        subcategories = SubCategory.objects.all()
        
        for category in categories:
            category_tickets = tickets.filter(category_id=category.id)
            category_ticket_count = category_tickets.count()

            subcategory_data = []
            for subcategory in subcategories.filter(category_id=category.id):
                subcategory_tickets = category_tickets.filter(subcategory_id=subcategory.id)
                subcategory_ticket_count = subcategory_tickets.count()
                
                subcategory_data.append({
                    'subcategory_name': subcategory.subcat_name,
                    'total_tickets': subcategory_ticket_count
                })
            
            category_data.append({
                'category_name': category.cat_name,
                'total_tickets': category_ticket_count,
                'subcategory_data': subcategory_data
            })
        
        return Response({
            "message": f"Category-based ticket report from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} retrieved successfully",
            "data": category_data
        }, status=status.HTTP_200_OK)
    
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_technician_status(request):
    try:
        # Fetch Role object for "Admin"
        admin_role = get_object_or_404(Role, role_name="Admin")

        # Get all technicians
        technicians = User.objects.filter(role_id=admin_role.role_id)
        if not technicians.exists():
            return Response({"error": "No technician found"}, status=status.HTTP_400_BAD_REQUEST)

        # Fetch all status mappings from the master table
        status_mappings = {status_obj.id: status_obj.name for status_obj in Status.objects.all()}
        
        # Get all tickets assigned to technicians
        tickets = Ticket.objects.filter(assigned_to_id__isnull=False)

        # Prepare the response data
        technicians_data = []
        for technician in technicians:
            technician_tickets = tickets.filter(assigned_to_id=technician.id)

            # Map ticket status IDs to names using the status master table
            technician_data = {
                "id": technician.id,
                "employee_id": technician.employee_id,
                "name": f"{technician.first_name} {technician.last_name}",
                "ticket_count": technician_tickets.count(),
                "open_ticket": technician_tickets.filter(Q(status=status_mappings.get(1))).count(),
                "in_progress_ticket": technician_tickets.filter(Q(status=status_mappings.get(2))).count(),
                "solved_ticket": technician_tickets.filter(status=status_mappings.get(3)).count(),
                "closed_tickets": technician_tickets.filter(status=status_mappings.get(4)).count(),
            }

            technicians_data.append(technician_data)

        return Response(
            {
                "message": "Technician Status report retrieved successfully!",
                "data": technicians_data,
            },
            status=status.HTTP_200_OK,
        )

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_technician_category(request):
    try:
        #Fetch Role object for "Admin"
        admin_role = get_object_or_404(Role, role_name="Admin")

        # Get all technicians
        technician = User.objects.filter(role_id=admin_role.role_id)
        if not technician.exists:
            return Response({"error": "No technician found"}, status=status.HTTP_400_BAD_REQUEST)
        
        tickets = Ticket.objects.filter(assigned_to_id__isnull=False)

        # Prepare the response data
        technicians_data = []

         # Fetch all categories with their names
        categories = Category.objects.all()
        subcategories = SubCategory.objects.all()

        # Prepare a dictionary for category name lookup
        category_names = {category.id: category.cat_name for category in categories}
        subcategory_names = {subcategory.id: subcategory.subcat_name for subcategory in subcategories}

        for technician in technician:
            technician_data = {
                "id": technician.id,
                "employee_id": technician.employee_id,
                "name": f"{technician.first_name} {technician.last_name}",
                "ticket_count": tickets.filter(assigned_to_id=technician.id).count(),
                "data": []
            }

            # Loop over each category to build the category structure
            for category in categories:
                category_data = {
                    "category_name": category.cat_name,
                    "total_tickets": tickets.filter(assigned_to_id=technician.id, category=category).count(),
                    "subcategory_data": []
                }

                # Loop over subcategories for each category
                for subcategory in subcategories.filter(category=category):
                    subcategory_data = {
                        "subcategory_name": subcategory.subcat_name,
                        "total_tickets": tickets.filter(
                            assigned_to_id=technician.id, category=category, subcategory=subcategory
                        ).count()
                    }
                    category_data["subcategory_data"].append(subcategory_data)

                # Add category data to technician data
                technician_data["data"].append(category_data)

            technicians_data.append(technician_data)

        return Response({"message": "Technician Status report retrieved successfully!", "data": technicians_data}, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def technician_time_report(request):
    try:
        #Fetch Role object for "Admin"
        admin_role = get_object_or_404(Role, role_name="Admin")

        # Get all technicians
        technician = User.objects.filter(role_id=admin_role.role_id)
        if not technician.exists:
            return Response({"error": "No technician found"}, status=status.HTTP_400_BAD_REQUEST)

        # Prepare the response data
        technicians_data = []

        for technician in technician:
             # Get all tickets assigned to the technician
            technician_tickets = Ticket.objects.filter(assigned_to_id=technician.id)

            #Calculate the total time spent on tickets for this technician
            total_time_spent =  technician_tickets.filter(solved_at__isnull=False).annotate(
                time_taken=ExpressionWrapper(
                    F('solved_at') - F('created_at'), output_field=DurationField()
                )
            ).aggregate(total_time=Sum('time_taken'))['total_time']

            total_time_str = format_duration(total_time_spent)

            # Now, let's calculate time spent by category
            category_time_data = []
            categories = technician_tickets.values('category').distinct()  # Get all distinct categories for the technician

            for category in categories:
                category_id = category['category']
                category_obj = get_object_or_404(Category, id=category_id)

                category_tickets = technician_tickets.filter(category=category['category'])

                # Calculate the time spent on this category
                category_time_spent = category_tickets.filter(solved_at__isnull=False).annotate(
                    time_taken=ExpressionWrapper(
                        F('solved_at') - F('created_at'), output_field=DurationField()
                    )
                ).aggregate(total_time=Sum('time_taken'))['total_time']

                # Format category time
                category_time_str = format_duration(category_time_spent)

                # Append the category data
                category_time_data.append({
                    "category": category_obj.cat_name,
                    "time_spent": category_time_str
                })

            technician_data = {
                "id": technician.id,
                "employee_id": technician.employee_id,
                "name": f'{technician.first_name} {technician.last_name}',
                "ticket_count": technician_tickets.count(),
                "total_time_spent": total_time_str,
                "category_time": category_time_data
            }

            technicians_data.append(technician_data)

        return Response({"message": "Technician Status report retrieved successfully!", "data": technicians_data}, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def technician_feedback(request):
    try:
        # Fetch Role object for "Admin"
        admin_role = Role.objects.get(role_name="Admin")

        # Get all technicians
        technician = User.objects.filter(role_id=admin_role.role_id)
        if not technician.exists():
            return Response({"error": "No technician found"}, status=status.HTTP_400_BAD_REQUEST)
        
        technician_data = []

        for technician in technician:
            technician_tickets = Ticket.objects.filter(assigned_to_id=technician.id)
            technician_feedback = Feedback.objects.filter(ticket__in=technician_tickets)

            # Initialize sum of feedback values and count of tickets
            total_feedback_value = 0
            feedback_count = 0

            for feedback in technician_feedback:
                #Skip feedback with value is 0
                if feedback.feedback_value != 0 :
                    total_feedback_value += feedback.feedback_value  # Sum all feedback values
                    feedback_count += 1  # Increment the feedback count
            
            # Calculate the average feedback if there are any feedbacks
            average_feedback = total_feedback_value / feedback_count if feedback_count > 0 else 0

            technician_data.append({
                "id": technician.id,
                "employee_id": technician.employee_id,
                "technician_name": f"{technician.first_name} {technician.last_name}",
                "feedback": average_feedback,  # Add average feedback value
                "tickets": feedback_count,
                "total_tickets": technician_tickets.count()
            })
        
        return Response({"message": "Feedback data fetched successfully.", "data": technician_data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def category_feedback(request):
    try:
        # Fetch all category
        category = Category.objects.all()
        if not category.exists():
            return Response({"error": "No category found"}, status=status.HTTP_400_BAD_REQUEST)
        
        category_data = []

        for category in category:
            #Gets feedback for all category
            category_tickets = Ticket.objects.filter(category_id=category.id)
            print(f"{category.cat_name} {category_tickets.count()}")

             # Extract ticket IDs for filtering feedback
            ticket_ids = category_tickets.values_list('ticket_id', flat=True)
            print(ticket_ids)

            # Get feedback for the tickets in the current category
            category_feedback = Feedback.objects.filter(ticket_id__in=ticket_ids)

            total_feedback_value = 0
            feedback_count = 0

            for feedback in category_feedback:
                #Skip feedback with value is 0
                if feedback.feedback_value != 0 :
                    total_feedback_value += feedback.feedback_value  # Sum all feedback values
                    feedback_count += 1  # Increment the feedback count
            
            # Calculate the average feedback if there are any feedbacks
            average_feedback = total_feedback_value / feedback_count if feedback_count > 0 else 0

            #Append category feedback
            category_data.append({
                "id": category.id,
                "category_name" : category.cat_name,
                "ticket_count": category_tickets.count(),
                "feedback_count": feedback_count,
                "feedback_value" : average_feedback
            })
        
        return Response({"message": "Category feedback fetched successfully.", "data": category_data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def priority_report(request):
    try:
        # Fetch all priority
        priority = PriorityLevel.objects.all()

        priority_data =[]

        for priority in priority:
            #Gets Priority count
            tickets = Ticket.objects.filter(urgency_id=priority.id)
            priority_data.append({
                "id": priority.id,
                "priority_name": priority.priority_name,
                "ticket_count": tickets.count()
            })

        return Response({"message": "Priority report fetched data successfully.", "data": priority_data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def employee_report(request):
    try:
        # Get start and end dates from the request
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        selected_status = request.GET.get('status')  # Get the selected status from the request

        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=status.HTTP_400_BAD_REQUEST)

        # Convert dates from 'dd/MM/yyyy' format to 'yyyy-MM-dd' for database queries
        try:
            start_date_obj = dt.strptime(start_date, "%d/%m/%Y").date()
            end_date_obj = dt.strptime(end_date, "%d/%m/%Y").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use dd/MM/yyyy."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Fetch employee report data using the date range and status filter
        report_data, success = get_employee_report_data(start_date_obj, end_date_obj, selected_status)

        if not success:
            return Response({"error": "Failed to fetch employee report data"}, status=status.HTTP_400_BAD_REQUEST)

        first_five_records = report_data['data'][:5]  # Get first 5 records

        # Extract employee IDs from the first five records
        employee_ids = [record['id'] for record in first_five_records]

        # Fetch active project assignments from ProjectMapping
        active_projects = ProjectMapping.objects.filter(
            user_id__in=employee_ids, status=1, removed_at__isnull=True
        ).values('user_id', 'project_id')

        # Fetch project details for assigned projects
        project_ids = {entry['project_id'] for entry in active_projects}
        project_details = Project.objects.filter(id__in=project_ids).values(
            'id', 'project_name'
        )

        # Create a mapping of employee ID to project list
        employee_project_map = {}
        for entry in active_projects:
            employee_id = entry['user_id']
            project_id = entry['project_id']
            if employee_id not in employee_project_map:
                employee_project_map[employee_id] = []
            # Add project details
            project = next((p for p in project_details if p['id'] == project_id), None)
            if project:
                employee_project_map[employee_id].append(project)
        print("Employee:", employee_project_map)

        # Append project details to each employee record
        for record in first_five_records:
            record['projects'] = employee_project_map.get(record['id'], [])

        return Response({
            "message": "Employee report fetched successfully",
            "data": first_five_records,
            "total_tickets": report_data.get("total_tickets", 0)
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_employee_reports(request):
    """
    Fetch all employee records along with their assigned projects.
    """
    try:
        # Get start and end dates from the request
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        selected_status = request.GET.get('status')  # Get the selected status from the request

        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=status.HTTP_400_BAD_REQUEST)

        # Convert dates from 'dd/MM/yyyy' format to 'yyyy-MM-dd' for database queries
        try:
            start_date_obj = dt.strptime(start_date, "%d/%m/%Y").date()
            end_date_obj = dt.strptime(end_date, "%d/%m/%Y").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use dd/MM/yyyy."}, status=status.HTTP_400_BAD_REQUEST)

        # Fetch all employee report data using the date range and status filter
        report_data, success = get_employee_report_data(start_date_obj, end_date_obj, selected_status)

        if not success:
            return Response({"error": "Failed to fetch employee report data"}, status=status.HTTP_400_BAD_REQUEST)

        all_records = report_data['data']  # Fetch all records

        # Fetch project assignments
        all_records = add_project_details(all_records, start_date_obj, end_date_obj)

        return Response({
            "message": "All employee reports fetched successfully",
            "data": all_records,
            "total_tickets": report_data.get("total_tickets", 0)
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
    
def add_project_details(employee_records, start_date, end_date):
    """
    Add to each employee record the list of projects (with ticket count)
    where they actually created tickets within the given date range.
    """

    # Extract employee IDs from records
    employee_ids = [record['id'] for record in employee_records]

    # Fetch ticket data grouped by employee and project
    ticket_data = (
        Ticket.objects.filter(
            created_by_id__in=employee_ids,
            created_at__date__range=[start_date, end_date]
        )
        .values('created_by_id', 'project')
        .annotate(total_tickets=Count('ticket_id'))
    )

    # Build map: {employee_id: [ { project_name, total_tickets }, ... ]}
    employee_project_map = {}

    for entry in ticket_data:
        user_id = entry['created_by_id']
        project_name = entry['project'] or "-"
        total_tickets = entry['total_tickets']

        if user_id not in employee_project_map:
            employee_project_map[user_id] = []

        employee_project_map[user_id].append({
            'project_name': project_name,
            'total_tickets': total_tickets
        })

    # Attach project info to each employee record
    for record in employee_records:
        record['projects'] = employee_project_map.get(
            record['id'], [{"project_name": "-", "total_tickets": 0}]
        )

    return employee_records