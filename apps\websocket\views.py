from rest_framework.response import Response
from rest_framework.decorators import api_view, parser_classes, permission_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated

from apps.ticket_management.models import Ticket, TicketChatAttachenment  # Consider renaming `Attachenment` to `Attachment`
from apps.ticket_management.serializers import TicketDocumentSerializer
from apps.user_management.models import User
from .models import TicketChatMessage
from .serializers import TicketChatMessageSerializer


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_ticket_messages(request, ticket_id):
    messages = TicketChatMessage.objects.filter(ticket__ticket_id=ticket_id).order_by("timestamp")
    chat_history = []

    for msg in messages:
        attachement = TicketChatAttachenment.objects.filter(chat=msg)
        attachment_list = [{
            "file_data": request.build_absolute_uri(att.attachement.url),
            "file_name": att.attachement.name.split("/")[-1]
        } for att in attachement if att.attachement]

        chat_history.append({
            "sender_id": msg.sender.id if msg.sender else None,
            "sender_name": msg.sender.first_name if msg.sender else "Unknown",
            "message": msg.message,
            "attachement": attachment_list,
            "timestamp": msg.timestamp.isoformat(),
        })

    return Response(chat_history)


@api_view(["POST"])
@parser_classes([MultiPartParser, FormParser])
@permission_classes([IsAuthenticated])
def upload_chat_attachment(request):
    ticket_id = request.data.get("ticket_id")
    sender_id = request.data.get("sender_id")
    message = request.data.get("message", "")
    attachement = request.FILES.getlist("attachement")

    if not ticket_id or not sender_id:
        return Response({"error": "ticket_id and sender_id are required"}, status=400)

    ticket = Ticket.objects.filter(ticket_id=ticket_id).first()
    sender = User.objects.filter(id=sender_id).first()

    if not ticket or not sender:
        return Response({"error": "Invalid ticket or sender"}, status=400)

    chat_message = TicketChatMessage.objects.create(
        ticket=ticket, sender=sender, message=message
    )

    attachment_list = []
    for file in attachement:
        attachment = TicketChatAttachenment.objects.create(
            chat=chat_message,
            attachement=file,
            ticket=ticket,
        )
        attachment_list.append({
            "file_data": request.build_absolute_uri(attachment.attachement.url),
            "file_name": attachment.attachement.name.split("/")[-1],
        })

    return Response({
        "message": message or "Message sent successfully",
        "chat_id": chat_message.id,
        "sender_id": sender.id,
        "attachement": attachment_list,
        "timestamp": chat_message.timestamp.isoformat(),
    }, status=201)
