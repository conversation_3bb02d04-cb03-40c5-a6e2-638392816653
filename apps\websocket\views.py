import unicodedata
from venv import logger
from rest_framework.response import Response
from rest_framework.decorators import api_view, parser_classes, permission_classes
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated

from apps.ticket_management.models import Ticket, TicketChatAttachenment  # Consider renaming `Attachen<PERSON>` to `Attachment`
from apps.ticket_management.serializers import TicketDocumentSerializer
from apps.ticket_management.views import sanitize_filename
from apps.user_management.models import User
from .models import TicketChatMessage
from .serializers import TicketChatMessageSerializer
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
import os


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_ticket_messages(request, ticket_id):
    messages = TicketChatMessage.objects.filter(ticket__ticket_id=ticket_id).order_by("timestamp")
    chat_history = []

    for msg in messages:
        attachement = TicketChatAttachenment.objects.filter(chat=msg)
        attachment_list = [{
            "file_data": request.build_absolute_uri(att.attachement.url),
            "file_name": att.attachement.name.split("/")[-1],
            "original_filename": att.original_filename
        } for att in attachement if att.attachement]

        chat_history.append({
            "sender_id": msg.sender.id if msg.sender else None,
            "sender_name": msg.sender.first_name if msg.sender else "Unknown",
            "message": msg.message,
            "attachement": attachment_list,
            "timestamp": msg.timestamp.isoformat(),
        })

    return Response(chat_history)


@api_view(["POST"])
@parser_classes([MultiPartParser, FormParser])
@permission_classes([IsAuthenticated])
def upload_chat_attachment(request):
    ticket_id = request.data.get("ticket_id")
    sender_id = request.data.get("sender_id")
    message = request.data.get("message", "")
    attachments = request.FILES.getlist("attachement")

    if not ticket_id or not sender_id:
        return Response({"error": "ticket_id and sender_id are required"}, status=400)

    ticket = Ticket.objects.filter(ticket_id=ticket_id).first()
    sender = User.objects.filter(id=sender_id).first()

    if not ticket or not sender:
        return Response({"error": "Invalid ticket or sender"}, status=400)

    chat_message = TicketChatMessage.objects.create(
        ticket=ticket, sender=sender, message=message
    )

    attachment_list = []
    for file in attachments:
        try:
            original_name = file.name 
            base, ext = os.path.splitext(original_name)

            # Normalize Unicode (safe but retains original characters like Japanese)
            normalized_name = unicodedata.normalize('NFKC', original_name)
            if not base or len(base) < 3:
                base = "file"

            # URL encode filename for filesystem safety
            import urllib.parse
            safe_filename = urllib.parse.quote(normalized_name, safe='')

            file_path = f"chat_uploads/{safe_filename}"
            file_content = file.read()
            renamed_file = ContentFile(file_content)
            renamed_file.name = safe_filename

            saved_path = default_storage.save(file_path, renamed_file)

            attachment = TicketChatAttachenment.objects.create(
                chat=chat_message,
                attachement=saved_path,
                ticket=ticket,
                original_filename=original_name
            )

            attachment_list.append({
                "file_data": request.build_absolute_uri(attachment.attachement.url),
                "file_name": attachment.original_filename or original_name,  # Send original Japanese name
                "display_filename": attachment.original_filename or original_name,
                # "original_filename": attachment.attachement.name.split("/")[-1],  # Optional: actual stored file
})

        except Exception as e:
            logger.error(f"Failed to process attachment '{file.name}': {e}")
            continue

    return Response({
        "message": message or "Message sent successfully",
        "chat_id": chat_message.id,
        "sender_id": sender.id,
        "attachement": attachment_list,
        "timestamp": chat_message.timestamp.isoformat(),
    }, status=201)
