"""
Django settings for ticketing_system project.

Generated by 'django-admin startproject' using Django 5.1.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/

"""

from datetime import timedelta
import os
from pathlib import Path
import firebase_admin
from firebase_admin import credentials, messaging
from decouple import config
from dotenv import load_dotenv

# Load .env file
load_dotenv()

LANG="C.UTF-8"
LC_ALL="C.UTF-8"
LC_LANG="C.UTF-8"

# Use PyMySQL as MySQL client (compatible with Python 3.13)
try:
    import pymysql
    pymysql.install_as_MySQLdb()
except ImportError:
    pass
BASE_DIR = Path(__file__).resolve().parent.parent

# Read from .env file
SECRET_KEY = config('SECRET_KEY', default='django-insecure-_$!bmy3u@vlgu@=11o9wv^l=2c@=l99@wy41nv8e1kx7670^xo')

# SECURITY WARNING: don't run with debug turned on in production!
# DEBUG = config('DEBUG', default=False, cast=bool)
DEBUG = config('DEBUG', default='False') == 'False'

# Parse allowed hosts from environment variable
ALLOWED_HOSTS_STR = config('ALLOWED_HOSTS', default='localhost,127.0.0.1')
ALLOWED_HOSTS = [host.strip() for host in ALLOWED_HOSTS_STR.split(',')]


# Application definition

INSTALLED_APPS = [
    "daphne",
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework.authtoken',
    "corsheaders",
    'channels',
    "django_crontab",
    "unidecode",
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'drf_yasg',  # Swagger API documentation
    'apps.user_management',
    'apps.ticket_management',
    'apps.it_support_config',
    'apps.authentication',
    'apps.websocket',
    'apps.report_analytics',
    'django_celery_beat'
]

ASGI_APPLICATION = "ticketing_system.asgi.application"

# MIDDLEWARE WITH CORS SUPPORT
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]




REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',
        # 'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
}


# CORS Configuration from .env file
CORS_ALLOWED_ORIGINS_STR = config('CORS_ALLOWED_ORIGINS', default='http://localhost:3000,http://localhost:3001')
CORS_ALLOWED_ORIGINS = [origin.strip() for origin in CORS_ALLOWED_ORIGINS_STR.split(',')]

CORS_ALLOW_CREDENTIALS = config('CORS_ALLOW_CREDENTIALS', default=True, cast=bool)
CORS_ALLOW_ALL_ORIGINS = config('CORS_ALLOW_ALL_ORIGINS', default=False, cast=bool)

CORS_ALLOW_METHODS = [
    "DELETE",
    "GET",
    "OPTIONS",
    "PATCH",
    "POST",
    "PUT",
]

CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
]

# Email Configuration from .env file
EMAIL_BACKEND = config('EMAIL_BACKEND', default='django.core.mail.backends.console.EmailBackend')
EMAIL_HOST = config('EMAIL_HOST', default='smtp.zeptomail.in')
EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='<EMAIL>')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')


ROOT_URLCONF = 'ticketing_system.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],  # Add templates directory
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ticketing_system.wsgi.application'

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",  # Use Redis in production
    },
}



# production Database (COMMENTED OUT FOR LOCAL DEVELOPMENT)
# DATABASES = {
#      'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'nex_ticket_db',
#         'USER': 'nex-ticketing',
#          'PASSWORD': 'nHH9Ky@RHgTDV',
#         'HOST': '***************',
#         'PORT': '3306',
#         'OPTIONS': {
#               'charset': 'utf8mb4',
#          },
#    }
#  }

# Staging Database
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'nex_ticket_stg_db',
#         'USER': 'nex-ticketing-stg',
#         'PASSWORD': 'RsRtW8u96@N',
#         'HOST': '***************',
#         'PORT': '3306',
#         'OPTIONS': {
#               'charset': 'utf8mb4',
#          },
#     }
# }


# HARDCODED PRODUCTION DATABASE CONFIGURATION
#DATABASES = {
#   'default': {
#       'ENGINE': 'django.db.backends.mysql',
#       'NAME': 'nex_ticket_db',
#       'USER': 'nex-ticketing',
#       'PASSWORD': 'nHH9Ky@RHgTDV',
#       'HOST': '***************',
#       'PORT': '3306',
#       'OPTIONS': {
#           'charset': 'utf8mb4',
#       },
#   }
#}

# Database Configuration from .env file
DATABASES = {
    'default': {
        'ENGINE': config('DATABASE_ENGINE', default='django.db.backends.mysql'),
        'NAME': config('DATABASE_NAME', default='ticketing_tool'),
        'USER': config('DATABASE_USER', default='root'),
        'PASSWORD': config('DATABASE_PASSWORD', default='admin@123'),
        'HOST': config('DATABASE_HOST', default='localhost'),
        'PORT': config('DATABASE_PORT', default='3306'),
        'OPTIONS': {
            'charset': config('DATABASE_CHARSET', default='utf8mb4'),
        },
    }
}


# Old database configurations removed - now using .env file configuration above

# Backend URL from .env file
BACKEND_URL = config('BACKEND_URL', default='http://localhost:3000/')


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# Additional static files directories
STATICFILES_DIRS = [
    # Add any additional static directories here if needed
]

# Static files finders (ensure drf-yasg static files are found)
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Force DEBUG mode for static file serving in staging/production
# This ensures Django serves static files even when DEBUG=False
FORCE_SERVE_STATIC = True

MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'


# CSRF Trusted Origins from .env file
CSRF_TRUSTED_ORIGINS_STR = config('CSRF_TRUSTED_ORIGINS', default='http://localhost:8000')
CSRF_TRUSTED_ORIGINS = [origin.strip() for origin in CSRF_TRUSTED_ORIGINS_STR.split(',')]




DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTHENTICATION_BACKENDS = [
    'apps.authentication.authentication.EmailBackend',
    'django.contrib.auth.backends.ModelBackend',  # Keep default backend
]

FIREBASE_CREDENTIALS_PATH = os.path.join(BASE_DIR, "firebase-service-account.json")

cred = credentials.Certificate(FIREBASE_CREDENTIALS_PATH)
firebase_admin.initialize_app(cred)

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=1),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": True,
    "UPDATE_LAST_LOGIN": False,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "TOKEN_TYPE_CLAIM": "token_type",
    "JTI_CLAIM": "jti",
}

AUTH_USER_MODEL = 'user_management.User'


CRONJOBS = [
    ('0 * * * *', 'apps.ticket_management.cron.update_solved_tickets_to_closed_direct'),
]
