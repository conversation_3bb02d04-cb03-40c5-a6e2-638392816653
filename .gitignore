venv/
env/
.env
.env.*

# Python files
*.pyc
__pycache__/
*.pyo
pycache/
*.pyd
*.db

# Django Stuff
*.log
*.pot
*.pyc
*.pyo
pycache/
deploy.log
dependency-update.log

# Virtual environment
env/
venv/
*.venv

# Django specific
db.sqlite3
/media/
/staticfiles/
*.sqlite3

# Jenkins related
jenkins/output/
.jenkins/
jenkins-workspace/


# Pipenv files (optional if you don't want lock files in version control)
Pipfile.lock

# IDE and editor files
.vscode/
.idea/
*.swp

# Git specific
*.orig

# OS-specific files
.DS_Store
Thumbs.db

# Django migrations
/migrations/
*/migrations/init.py

# PyCharm
.idea/

# VS Code
.vscode/

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# MySQL/MariaDB
*.sql
*.sql.gz

# MacOS
.DS_Store

# Windows
Thumbs.db

# Deployment directories
deploy-staging/
deploy-production/
