"""
Django settings for ticketing_system project.

Generated by 'django-admin startproject' using Django 5.1.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from datetime import timedelta
import os
from pathlib import Path
import firebase_admin
from firebase_admin import credentials, messaging
# from decouple import config


# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-_$!bmy3u@vlgu@=11o9wv^l=2c@=l99@wy41nv8e1kx7670^xo'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = False



ALLOWED_HOSTS = [
    '***************',
    'internal-project.nexware-global.com',
    'nex-ticket.nexware-global.com',
    'ticket.nexware-global.com',
    'localhost',
    '127.0.0.1',
]


# Application definition

INSTALLED_APPS = [
    "daphne", 
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'rest_framework',
    'rest_framework.authtoken',
    "corsheaders",
    'channels',
    "django_crontab",
    'rest_framework_simplejwt',
    'apps.user_management',
    'apps.ticket_management',
    'apps.it_support_config',
    'apps.authentication',
    'apps.websocket',
    'django_celery_beat'
]

ASGI_APPLICATION = "ticketing_system.asgi.application"

MIDDLEWARE = [
    # Our custom CORS middleware must run first to handle preflight requests
    'apps.middleware.RemoveDuplicateCORSHeadersMiddleware',  # Custom middleware to fix CORS headers
    # Django's built-in security middleware
    'django.middleware.security.SecurityMiddleware',
    # We're removing the Django CORS middleware to avoid conflicts
    # 'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# CORS allowed methods - DISABLED
# These settings are NOT being used since we've disabled the django-cors-headers middleware
# Our custom middleware (RemoveDuplicateCORSHeadersMiddleware) handles all CORS functionality
# CORS_ALLOW_METHODS = [
#     "DELETE",
#     "GET",
#     "OPTIONS",
#     "PATCH",
#     "POST",
#     "PUT",
# ]


REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
}


# CORS settings
# NOTE: These settings are NOT being used since we've disabled the django-cors-headers middleware
# Our custom middleware (RemoveDuplicateCORSHeadersMiddleware) handles all CORS functionality
# We're keeping these settings commented out for reference only

# CORS_ALLOW_CREDENTIALS = True
# CORS_ALLOWED_ORIGINS = [
#     "http://internal-project.nexware-global.com:9018",  # Frontend
#     "http://internal-project.nexware-global.com:9019",  # Backend
#     "http://localhost:3000",
#     "http://localhost:4173",
#     "http://localhost:5173",
# ]
# CORS_ORIGIN_ALLOW_ALL = False
# CORS_EXPOSE_HEADERS = []

# The allowed origins are now defined directly in the middleware class
# See apps/middleware.py for the current list of allowed origins



# smtp email config setup
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.zeptomail.in'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'PHtE6r0JELzu2Gcpo0JWtKewRcCiYIss/epmeAEU4dlHC/8ETk1Sqtkjlme1rRguB/ATF/WewIw9tr/OtL/WLDu8MWpOXGqyqK3sx/VYSPOZsbq6x00asV4dd0LVU4bqdtdr1CDTvdjZNA=='  # Use an app password if using Gmail
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

ROOT_URLCONF = 'ticketing_system.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ticketing_system.wsgi.application'

CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels.layers.InMemoryChannelLayer",  # Use Redis in production
    },
}



# production Database
# DATABASES = {
#     'default': {
#        'ENGINE': 'django.db.backends.mysql',
#        'NAME': 'nex_ticket_db',
#        'USER': 'nex-ticketing',
#         'PASSWORD': 'nHH9Ky@RHgTDV',
#        'HOST': '***************',
#        'PORT': '3306',
#        'OPTIONS': {
#              'charset': 'utf8mb4',
#         },
#   }
# }

# Staging Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'nex_ticket_stg_db',
        'USER': 'nex-ticketing-stg',
        'PASSWORD': 'RsRtW8u96@N',
        'HOST': '***************',
        'PORT': '3306',
        'OPTIONS': {
              'charset': 'utf8mb4',
         },
    }
}


#Local Database
# DATABASES = {
#      'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'ticketing_tool',
#         'USER': 'root',
#         'PASSWORD': 'nHH9Ky@RHgTDV',
#         'HOST': 'localhost',
#         'PORT': '3306',
#         'OPTIONS': {
#             'charset': 'utf8mb4',
#             # "init_command": "SET foreign_key_checks = 0;",
#          },
#     }
# }

# Local Database
# DATABASES = {
#    'default': { 
#       'ENGINE': 'django.db.backends.mysql',
#       'NAME': 'ticketing_tool',
#       'USER': 'root',
#       'PASSWORD': 'admin@123',
#       'HOST': 'localhost',
#       'PORT': '3306',
#       'OPTIONS': {
#             'charset': 'utf8mb4',
#             },
#    }
# }

# mail route URL: before push check this url
# BACKEND_URL='https://ticket.nexware-global.com:9048/'
# BACKEND_URL = 'http://localhost:3000/'
BACKEND_URL = 'http://internal-project.nexware-global.com:9018/' #staging URL


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


STATIC_URL = 'static/'

MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# server Logs

# LOG_DIR = os.path.join(BASE_DIR, 'logs')  # Store logs inside the 'logs' directory
# os.makedirs(LOG_DIR, exist_ok=True)  # Ensure the directory exists

# server Logs

# LOG_DIR = os.path.join(BASE_DIR, 'logs')  # Store logs inside the 'logs' directory
# os.makedirs(LOG_DIR, exist_ok=True)  # Ensure the directory exists

# LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'formatters': {
#         'verbose': {
#             'format': '{levelname} {asctime} {module} {message}',
#             'style': '{',
#         },
#         'simple': {
#             'format': '{levelname} {message}',
#             'style': '{',
#         },
#     },
#     'handlers': {
#         'debug_file': {
#             'level': 'DEBUG',
#             'class': 'logging.FileHandler',
#             'filename': os.path.join(LOG_DIR, 'debug.log'),
#             'formatter': 'verbose',
#         },
#         'error_file': {
#             'level': 'ERROR',
#             'class': 'logging.FileHandler',
#             'filename': os.path.join(LOG_DIR, 'error.log'),
#             'formatter': 'verbose',
#         },
#         'console': {
#             'level': 'DEBUG',
#             'class': 'logging.StreamHandler',
#             'formatter': 'simple',
#         },
#     },
#     'loggers': {
#         'django': {
#             'handlers': ['debug_file', 'error_file', 'console'],
#             'level': 'DEBUG',
#             'propagate': True,
#         },
#     },
# }

# LOGGING = {
#     "version": 1,
#     "disable_existing_loggers": False,
#     "handlers": {
#         "file": {
#             "level": "DEBUG",
#             "class": "logging.FileHandler",
#             "filename": "debug.log",
#         },
#     },
#     "loggers": {
#         "django.db.backends": {
#             "handlers": ["file"],
#             "level": "DEBUG",
#             "propagate": False,
#         },
#     },
# }

# CORS settings - DISABLED
# These settings are NOT being used since we've disabled the django-cors-headers middleware
# Our custom middleware (RemoveDuplicateCORSHeadersMiddleware) handles all CORS functionality
# We're keeping these settings commented out for reference only

# CORS_ALLOW_CREDENTIALS = True
# CORS_ALLOW_ALL_ORIGINS = False

# CORS_ALLOW_HEADERS = (
#     "accept",
#     "authorization",
#     "content-type",
#     "user-agent",
#     "x-csrftoken",
#     "x-requested-with",
#     "Bearer-Token",
#     "origin",
#     "cache-control",
#     "pragma"
# )

CSRF_TRUSTED_ORIGINS = [
    'http://localhost:8000',
    "http://internal-project.nexware-global.com:9018",
    "http://internal-project.nexware-global.com:9019",
    # "https://ticket.nexware-global.com:8001",
    "https://ticket.nexware-global.com:9049",
    "https://internal-project.nexware-global.com:8001",
    # "https://internal-project.nexware-global.com:9049",
]




DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTHENTICATION_BACKENDS = [
    'apps.authentication.authentication.EmailBackend',
    'django.contrib.auth.backends.ModelBackend',  # Keep default backend
]

FIREBASE_CREDENTIALS_PATH = os.path.join(BASE_DIR, "firebase-service-account.json")

cred = credentials.Certificate(FIREBASE_CREDENTIALS_PATH)
firebase_admin.initialize_app(cred)

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(days=2),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=1),
    "ROTATE_REFRESH_TOKENS": True,
    "BLACKLIST_AFTER_ROTATION": False,
    "UPDATE_LAST_LOGIN": False,
}

AUTH_USER_MODEL = 'user_management.User'

# Redis as the Celery broker Local
# CELERY_BROKER_URL = "redis://http://internal-project.nexware-global.com:9048:6379/0"
# CELERY_ACCEPT_CONTENT = ['json']
# CELERY_TASK_SERIALIZER = 'json'

CRONJOBS = [
    ('0 * * * *', 'apps.ticket_management.cron.update_solved_tickets_to_closed_direct'),
]


# Staging
# CELERY_BROKER_URL = 'redis://internal-project.nexware-global.com:6379/0'
# CELERY_ACCEPT_CONTENT = ['json']
# CELERY_TASK_SERIALIZER = 'json'

# # Backend for storing task results (optional)
# CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

# Timezone settings (ensure it's the same as Django's TIME_ZONE)
# CELERY_TIMEZONE = 'UTC'