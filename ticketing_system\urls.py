"""
URL configuration for ticketing_system project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include, re_path
from django.conf.urls.static import static
from django.conf import settings
from .views import home_view

# Initialize schema_view as None
schema_view = None

# Try to import and configure Swagger
try:
    # Check if drf_yasg is in INSTALLED_APPS
    if 'drf_yasg' in settings.INSTALLED_APPS:
        # Swagger imports
        from rest_framework import permissions
        from drf_yasg.views import get_schema_view
        from drf_yasg import openapi

        # Swagger schema configuration
        schema_view = get_schema_view(
            openapi.Info(
                title="Ticketing System API",
                default_version='v1',
                description="API documentation for the Ticketing System",
                terms_of_service="https://www.nexware-global.com/terms/",
                contact=openapi.Contact(email="<EMAIL>"),
                license=openapi.License(name="Proprietary"),
            ),
            public=True,
            permission_classes=(permissions.AllowAny,),
        )
        print("Swagger documentation URLs enabled.")
except ImportError:
    print("drf-yasg not available. Swagger documentation URLs disabled.")

urlpatterns = [
    # Root URL handler
    path('', home_view, name='home'),

    # Admin site
    path('admin/', admin.site.urls),

    # API endpoints
    path('api/',include('apps.ticket_management.urls')),
    path('api/', include('apps.user_management.urls')),
    path('api/', include('apps.it_support_config.urls')),
    path('api/', include('apps.report_analytics.urls')),
    path('api/', include('apps.authentication.urls')),
    path('api/',include('apps.websocket.urls')),
    path('',include('apps.ticket_management.urls')),
]

# Add Swagger documentation URLs if available
if schema_view:
    urlpatterns += [
        # Swagger documentation URLs
        re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
        re_path(r'^swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
        re_path(r'^redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),

        # API documentation at the root of the API
        path('api/docs/', schema_view.with_ui('swagger', cache_timeout=0), name='api-docs'),
    ]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
