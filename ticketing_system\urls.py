"""
URL configuration for ticketing_system project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include, re_path
from django.conf.urls.static import static
from django.conf import settings
from .views import home_view

# Initialize schema_view as None
schema_view = None

# Try to import and configure Swagger
try:
    # Check if drf_yasg is in INSTALLED_APPS
    if 'drf_yasg' in settings.INSTALLED_APPS:
        # Swagger imports
        from rest_framework import permissions
        from drf_yasg.views import get_schema_view
        from drf_yasg import openapi

        # Swagger schema configuration with enhanced documentation
        schema_view = get_schema_view(
            openapi.Info(
                title="🎫 Nex Ticketing System API",
                default_version='v1.0.0',
                description="""
# 🚀 Comprehensive Ticketing System API

This API provides complete functionality for managing IT support tickets, users, and system configuration.

## 🔐 Authentication
- **JWT Authentication**: Use Bearer token in Authorization header
- **Login Endpoint**: `/api/login` - Get access and refresh tokens
- **Token Refresh**: `/api/token/refresh/` - Refresh expired access tokens

## 📋 Main Features
- **🎫 Ticket Management**: Create, update, assign, and track tickets
- **👥 User Management**: Manage users, roles, and permissions
- **📊 Analytics & Reports**: Comprehensive reporting and analytics
- **🔧 IT Support Configuration**: Categories, priorities, and system settings
- **💬 Real-time Chat**: WebSocket-based ticket communication
- **🔔 Notifications**: FCM push notifications and in-app notifications

## 🌐 Environments
- **Staging**: http://internal-project.nexware-global.com:9019/
- **Production**: https://ticket.nexware-global.com:9049/

## 📖 API Testing
Use the **"Try it out"** button on each endpoint to test the API directly from this documentation.
                """,
                terms_of_service="https://www.nexware-global.com/terms/",
                contact=openapi.Contact(
                    name="Nexware IT Support",
                    email="<EMAIL>",
                    url="https://www.nexware-global.com"
                ),
                license=openapi.License(name="Proprietary License"),
            ),
            public=True,
            permission_classes=(permissions.AllowAny,),
            authentication_classes=[],
        )
        print("✅ Swagger documentation URLs enabled with enhanced configuration.")
    else:
        schema_view = None
        print("⚠️  drf_yasg not found in INSTALLED_APPS.")
except ImportError as e:
    schema_view = None
    print(f"❌ drf-yasg not available: {e}")

urlpatterns = [
    # Root URL handler - serves HTML landing page
    path('', home_view, name='home'),

    # API info endpoint - serves JSON data
    path('api/', home_view, name='api-info'),

    # Admin site
    path('admin/', admin.site.urls),

    # API endpoints
    path('api/',include('apps.ticket_management.urls')),
    path('api/', include('apps.user_management.urls')),
    path('api/', include('apps.it_support_config.urls')),
    path('api/', include('apps.report_analytics.urls')),
    path('api/', include('apps.authentication.urls')),
    path('api/',include('apps.websocket.urls')),
    path('',include('apps.ticket_management.urls')),
]
if settings.DEBUG:
        urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Add Swagger documentation URLs if available
if schema_view:
    urlpatterns += [
        # 📖 Main API Documentation (Swagger UI)
        path('api/docs/', schema_view.with_ui('swagger', cache_timeout=0), name='api-docs-swagger'),
        path('docs/', schema_view.with_ui('swagger', cache_timeout=0), name='api-docs-main'),

        # 📚 Alternative Documentation (ReDoc)
        path('api/redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='api-docs-redoc'),
        path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='api-docs-redoc-main'),

        # 📄 Schema Downloads
        re_path(r'^api/swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
        re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json-main'),

        # 🔗 Legacy URLs (for backward compatibility)
        re_path(r'^swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    ]
    
    if settings.DEBUG or getattr(settings, "FORCE_SERVE_STATIC", False):
        urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

    print("📖 API Documentation available at:")
    print("   • Swagger UI: /api/docs/ or /docs/")
    print("   • ReDoc: /api/redoc/ or /redoc/")
    print("   • Schema JSON: /api/swagger.json")
    print("   • Schema YAML: /api/swagger.yaml")
else:
    print("❌ Swagger documentation not available - drf_yasg not configured properly")

# Serve static and media files
# Always serve static files (required for drf-yasg in staging/production)
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

# Additional debug info for static files
if hasattr(settings, 'FORCE_SERVE_STATIC') and settings.FORCE_SERVE_STATIC:
    print(f"🔧 Force serving static files from: {settings.STATIC_ROOT}")
    print(f"🔧 Static URL pattern: {settings.STATIC_URL}")

    # Verify static files exist
    import os
    drf_yasg_path = os.path.join(settings.STATIC_ROOT, 'drf-yasg')
    if os.path.exists(drf_yasg_path):
        print(f"✅ drf-yasg static files found at: {drf_yasg_path}")
    else:
        print(f"❌ drf-yasg static files NOT found at: {drf_yasg_path}")
        print("🔧 Run: python manage.py collectstatic --noinput")
