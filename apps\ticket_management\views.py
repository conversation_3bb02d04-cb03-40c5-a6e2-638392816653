import math
import grpc_status
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework import status
from .models import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Feed<PERSON><PERSON><PERSON><PERSON>, PriorityLevel, StatusTracking, Ticket,TicketChatAttachenment
from itsdangerous import URLSafeTimedSerializer, SignatureExpired, BadTimeSignature, BadSignature
from django.conf import settings
from .models import Ticket
from .serializers import TicketSerializer, TicketUpdateSerializer,TicketStatusSerializer
from rest_framework.response import Response
from rest_framework import status,viewsets
from ..user_management.models import  User
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import Feedback,Notification
# from .serializers import FeedbackSerializer,TicketChatSerializer
from rest_framework import status
from .models import Feedback,Notification,Status
from .serializers import FeedbackSerializer
from django.db.models import Q
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
from django.utils.timezone import now
from ..authentication.firebase_utils import send_push_notification
from rest_framework.decorators import api_view, permission_classes
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
from apps.websocket.models import TicketChatMessage
from datetime import timedelta
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.utils.timesince import timesince
from django.db import connection
import logging
from django.db import transaction
from django.contrib.auth.tokens import default_token_generator
from .serializers import ChatListSerializer, FeedbackValueSerializer, NotificationSerializer, StatusTrackingSerializer, TicketApprovalStatusSerializer, TicketChatSerializer, TicketPrioritySerializer, TicketSerializer, TicketUpdateSerializer,FeedbackSerializer



logger = logging.getLogger(__name__)
class ApproveTicketView(APIView):
    def get(self, request, ticket_id):
        token = request.query_params.get('token')
        if not token:
            return Response({"error": "Token not provided"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            ticket = Ticket.objects.get(ticket_id=ticket_id)
        except Ticket.DoesNotExist:
            return Response({"error": "Ticket not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
        try:
            data = serializer.loads(token, salt=settings.APPROVAL_TOKEN_SALT, max_age=86400) # Token valid for 24 hours
        except (SignatureExpired, BadTimeSignature, BadSignature):
            return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)

        if data['ticket_id'] != ticket_id or data['email'] != ticket.assigned_to.role.manager.email:
            return Response({"error": "Invalid token for this ticket"}, status=status.HTTP_400_BAD_REQUEST)

        # Update ticket status to approved
        ticket.is_approved = True
        ticket.approved_by = ticket.assigned_to.role.manager
        ticket.approvel_status = ApprovalStatus.objects.get(name="Approved")
        ticket.save()

        return Response({"message": "Ticket approved successfully"}, status=status.HTTP_200_OK)

class RejectTicketView(APIView):
    def get(self, request, ticket_id):
        token = request.query_params.get('token')
        if not token:
            return Response({"error": "Token not provided"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            ticket = Ticket.objects.get(ticket_id=ticket_id)
        except Ticket.DoesNotExist:
            return Response({"error": "Ticket not found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
        try:
            data = serializer.loads(token, salt=settings.APPROVAL_TOKEN_SALT, max_age=86400) # Token valid for 24 hours
        except (SignatureExpired, BadTimeSignature, BadSignature):
            return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)

        if data['ticket_id'] != ticket_id or data['email'] != ticket.assigned_to.role.manager.email:
            return Response({"error": "Invalid token for this ticket"}, status=status.HTTP_400_BAD_REQUEST)

        # Update ticket status to rejected
        ticket.is_approved = False
        ticket.approvel_status = ApprovalStatus.objects.get(name="Rejected")
        ticket.save()

        return Response({"message": "Ticket rejected successfully"}, status=status.HTTP_200_OK)

class CustomPagination(PageNumberPagination):
    page_size = 10 # Set 10 tickets per page
    page_size_query_param = 'page_size'
    max_page_size = 100

    def get_paginated_response(self, data):
        return Response({
            "count": self.page.paginator.count,  # Total number of items
            "total_pages": math.ceil(self.page.paginator.count / self.page_size),  # Total pages
            "current_page": self.page.number,  # Current page number
            "next": self.get_next_link(),
            "previous": self.get_previous_link(),
            "results": data  # Paginated data
        })


# for update ticket dynamic data

def handle_ticket_update_logic(instance, request, serializer, files, old_data):
    user_id = request.user.id

    # Save attachments
    for file in files:
        TicketChatAttachenment.objects.create(ticket=instance, attachement=file)

    new_status = serializer.validated_data.get('status', instance.status.id)
    new_assigned_to = serializer.validated_data.get('assigned_to', instance.assigned_to)
    new_due_date = serializer.validated_data.get('due_date', instance.due_date)
    new_category = serializer.validated_data.get('category', instance.category)

    if isinstance(new_status, str) and new_status.isdigit():
        new_status = int(new_status)

   

    if old_data['assigned_to'] != new_assigned_to and 'due_date' in serializer.validated_data:
        instance.due_date = new_due_date

    serializer.save()

    created_by_id = instance.created_by.id if instance.created_by else user_id
    super_admin = User.objects.filter(role_id="R001").first()
    admin = User.objects.filter(role_id="R002").first()

    if super_admin and super_admin.fcm_token:
        send_push_notification(
            super_admin,
            "New Ticket Updated",
            f"Ticket '{instance.title}' (ID: {instance.ticket_id}) has been updated."
        )
    elif admin and admin.fcm_token:
        send_push_notification(
            admin,
            "New Ticket Updated",
            f"Ticket '{instance.title}' (ID: {instance.ticket_id}) has been updated."
        )

    if old_data['status'] != new_status:
        StatusTracking.objects.create(
            ticket=instance,
            current_status=new_status,
            updated_by_id=user_id,
            created_by_id=created_by_id,
            created_at=now(),
        )

    update_messages = []
    if old_data['status'] != new_status:
        update_messages.append(f"Status changed to {instance.status}")
    if old_data['assigned_to'] != new_assigned_to:
        assigned_to_name = getattr(instance.assigned_to, "get_full_name", lambda: "N/A")()
        update_messages.append(f"Assigned to {assigned_to_name}")
    if old_data['due_date'] != new_due_date:
        update_messages.append(f"Due date updated to {instance.due_date.strftime('%Y-%m-%d')}")
    if old_data['category'] != new_category:
        update_messages.append(f"Category changed to {instance.category}")

    if update_messages:
        update_text = " | ".join(update_messages)
        chat_message = TicketChatMessage.objects.create(
            ticket=instance,
            sender=request.user,
            message=f"Ticket updated: {update_text}",
            timestamp=now()
        )

        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f"ticket_{instance.ticket_id}",
            {
                "type": "ticket_chat_message",
                "message": {
                    "id": chat_message.id,
                    "ticket": instance.ticket_id,
                    "sender_name": f"{request.user.first_name} {request.user.last_name}",
                    "message": chat_message.message,
                    "timestamp": chat_message.timestamp.isoformat(),
                }
            }
        )

        Notification.objects.create(
            ticket=instance,
            message=f"Ticket{instance.ticket_id}-{chat_message.message}-{chat_message.timestamp.isoformat()}",
            is_read=False,
            created_by_id=created_by_id,
            updated_by_id=user_id,
            from_user=instance.updated_by,
            to_user=instance.created_by,
        )


class TicketViewSet(ModelViewSet):
    permission_classes=[IsAuthenticated]
    queryset = Ticket.objects.all()
    serializer_class = TicketSerializer
    

    def create(self, request, *args, **kwargs):
        try:
            # Start a transaction block to ensure rollback on failure
            with transaction.atomic():
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)

                # Ensure 'created_by' is set before saving
                if 'created_by' not in request.data:
                    return Response(
                        {"status": 0, "code": 400, "message": "Ticket must have a valid creator"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                created_by = request.data.get("created_by")
                created_by_user = User.objects.filter(id=created_by).first()

                if not created_by_user:
                    return Response(
                        {"status": 0, "code": 400, "message": "Creator user does not exist"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Assign ticket to a manager if needed
                assigned_to = User.objects.filter(role="R003").first()
                if not assigned_to:
                    return Response(
                        {"status": 0, "code": 400, "message": "No manager found for assignment"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                default_status = Status.objects.filter(id=4).first()
                if not default_status:
                    return Response(
                        {"status": 0, "code": 400, "message": "Default status not found"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Save ticket with creator and assignee
                ticket = serializer.save(created_by=created_by_user)

                if not ticket.ticket_id:
                    return Response(
                        {"status": 0, "code": 400, "message": "Ticket was not created successfully"},
                        status=status.HTTP_400_BAD_REQUEST
                    )

                # Handle file uploads
                files = request.FILES.getlist('attachement') if 'attachement' in request.FILES else []
                MAX_SIZE_MB = 2

                # Check if the files exceed the size limit
                for file in files:
                    if file.size > MAX_SIZE_MB * 1024 * 1024:
                        return Response(
                            {"status": 0, "code": 400, "message": f"{file.name} exceeds the {MAX_SIZE_MB}MB limit."},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                # Attempt to save file attachments
                for file in files:
                    try:
                        TicketChatAttachenment.objects.create(ticket=ticket, attachement=file)
                    except Exception as e:
                        # If there's a file saving error, rollback ticket creation
                        transaction.set_rollback(True)
                        return Response(
                            {"status": 0, "code": 400, "message": f"Error saving file {file.name}: {str(e)}"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                # Notify super admin and create notifications (same as original flow)
                super_admin = User.objects.filter(role_id="R001").first()
                if super_admin and super_admin.fcm_token:
                    send_push_notification(
                        super_admin,
                        "New Ticket Created",
                        f"Ticket '{ticket.title}' (ID: {ticket.ticket_id}) has been created."
                    )

                # More steps like chat entry, status tracking, etc.
                 # Create notification
            Notification.objects.create(
                ticket=ticket,
                created_by=ticket.created_by,
                message=f"New ticket created: {ticket.title} (ID: {ticket.ticket_id})",
                is_read=False,
                from_user=ticket.updated_by,
                to_user=ticket.created_by,
            )

            # Create chat entry
            ChatList.objects.create(
                ticket=ticket,
                message=ticket.description,
                title=ticket.title,
                sender_id=ticket.created_by_id,
                receiver_id=ticket.assigned_to_id
            )

            # Create status tracking entry
            StatusTracking.objects.create(
                ticket=ticket,
                current_status=ticket.status,  # Use the correct field from the Status model
                updated_by=ticket.created_by,
                created_by=ticket.created_by,
                created_at=now(),
                updated_at=now(),
            )
            created_ago = timesince(ticket.created_at)  # Example: "2 days ago"
            updated_ago = timesince(ticket.updated_at)  # Example: "2 days ago"

            # Get user details
            creator_name = f"{ticket.created_by.first_name} {ticket.created_by.last_name}"
            last_updater_name = f"{ticket.updated_by.first_name} {ticket.updated_by.last_name}" if ticket.updated_by else "N/A"
            # Format chat message
            chat_message_content = f"""
            {ticket.title} (ID: {ticket.ticket_id})
            {ticket.description}
            """

            # Save initial chat message
            chat_message = TicketChatMessage.objects.create(
                ticket=ticket,
                sender=created_by_user,
                message=chat_message_content.strip(),  # Clean up spaces
                timestamp=now()
            )

            # Send WebSocket event
            channel_layer = get_channel_layer()
            async_to_sync(channel_layer.group_send)(
                f"ticket_{ticket.ticket_id}",
                {
                    "type": "ticket_chat_message",
                    "message": {
                        "id": chat_message.id,
                        "ticket": ticket.ticket_id,
                        # "sender_name": f"{created_by_user.first_name} {created_by_user.last_name}",
                        "sender_name": creator_name,
                        "message": chat_message.message,
                        "timestamp": chat_message.timestamp.isoformat(),
                    }
                }
            )


            return Response({
                    "status": 1,
                    "code": 201,
                    "message": "Ticket created successfully",
                    "data": serializer.data
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                "status": 0,
                "code": 400,
                "message": str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def update(self, request, *args, **kwargs):
        try:
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            files = request.FILES.getlist('attachement')
            old_data = {
                'status': instance.status.id,
                'assigned_to': instance.assigned_to,
                'due_date': instance.due_date,
                'category': instance.category
            }

            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            if serializer.is_valid():
                handle_ticket_update_logic(instance, request, serializer, files, old_data)
                return Response(
                    {
                        "status": 1,
                        "code": 200,
                        "message": "Ticket updated successfully",
                        "data": serializer.data
                    },
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    {
                        "status": 0,
                        "code": 400,
                        "message": "Ticket update failed",
                        "errors": serializer.errors
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {
                    "status": 0,
                    "code": 400,
                    "message": f"Something went wrong. Error: {str(e)}"
                },
                status=status.HTTP_400_BAD_REQUEST
            )

   

class TicketView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            role = request.query_params.get('role')
            user_id = request.query_params.get('user_id')
            status_param = request.query_params.get('status')
            page_size = request.query_params.get('page_size')

            if not role or not user_id:
                return Response(
                    {"status": 0, "code": 400, "message": "Role and User Id are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                user_id = int(user_id)
            except ValueError:
                return Response(
                    {"status": 0, "code": 400, "message": "Invalid User Id format"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            is_pending_approval = request.query_params.get('is_approved', None)
            if is_pending_approval and role not in ['R001', 'R002', 'R003']:
                return Response(
                    {"status": 0, "code": 403, "message": "Invalid page. Access denied."},
                    status=status.HTTP_403_FORBIDDEN
                )

            # Base QuerySet depending on role
            if role in ['R001', 'R002']:  # Super Admin
                tickets = Ticket.objects.filter(is_deleted=False)
            elif role == 'R006':  # Support Agent
                tickets = Ticket.objects.filter(Q(assigned_to_id=user_id) | Q(created_by_id=user_id), is_deleted=False)
            elif role in ['R003', 'R004']:  # Project Manager
                tickets = Ticket.objects.filter(
                    Q(assigned_to_id=user_id) | Q(created_by_id=user_id) | Q(watchers__contains=[user_id]), is_deleted=False
                )
            elif role == 'R005':
                tickets = Ticket.objects.filter(
                    Q(created_by_id=user_id) | Q(watchers__contains=[user_id]), is_deleted=False
                )
            else:
                return Response(
                    {"status": 0, "code": 400, "message": "Invalid role provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            tickets = tickets | Ticket.objects.filter(watchers__id=user_id)

            if role in ["R003", "R004", "R005"]:
                tickets = tickets.exclude(status=0)

            # Filters
            filter_params = {
                "status_id": request.query_params.get('status', None),
                "assigned_to_id": request.query_params.get('assigned_to', None),
                "created_by_id": request.query_params.get('created_by', None),
                "location_id": request.query_params.get('location', None),
                "priority": request.query_params.get('priority', None),
                "status": request.query_params.get('status', None),
            }

            ticket_type = request.query_params.get('type', None)
            search_query = request.query_params.get('search', None)
            sort_by = request.query_params.get('sort_by', 'created_at')

            allowed_sort_fields = ["created_at", "updated_at", "priority", "status"]
            if sort_by not in allowed_sort_fields:
                sort_by = "created_at"

            filters = Q()
            for key, value in filter_params.items():
                if value is not None:
                    filters &= Q(**{key: value})
            if ticket_type:
                filters &= Q(type__icontains=ticket_type)

            # ⬇️ Handle search query including project name if it's a FK
            if search_query:
                filters &= (
                    Q(ticket_id__icontains=search_query) |
                    Q(title__icontains=search_query) |
                    Q(project__icontains=search_query)  # Assuming 'project' is a ForeignKey to a Project model with 'name'
                )

            tickets = tickets.filter(filters).order_by("-created_at")
            # tickets = Ticket.objects.filter(filters).order_by("-created_at")


            # Show closed tickets only for the last 30 days
            last_30_days = timezone.now() - timedelta(days=30)
            tickets = tickets.exclude(status=8) | tickets.filter(status=8, updated_at__gte=last_30_days)

            if page_size == "all":
                all_tickets = tickets.all()
                serializer = TicketSerializer(all_tickets, many=True,)
                return Response({"status": 1, "code": 200, "data": serializer.data}, status=status.HTTP_200_OK)

            paginator = CustomPagination()
            paginated_tickets = paginator.paginate_queryset(tickets, request)

            if paginated_tickets is None:
                return Response(
                    {"status": 0, "code": 400, "message": "Invalid page number or no data found."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            serializer = TicketSerializer(paginated_tickets, many=True)
            return paginator.get_paginated_response(serializer.data)

        except Exception as e:
            return Response(
                {
                    "status": 0,
                    "code": 500,
                    "message": f"Something went wrong. Please contact admin! Error: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class TicketAssigneeView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        try:
            assigned_to = request.query_params.get('assigned_to')

            # Convert "null" or empty string values to None
            def convert_to_none(value):
                return None if value in ["null", "None", "", None] else value

            assigned_to = convert_to_none(assigned_to)

            # Filtering logic
            filters = Q()

            if assigned_to:
                filters &= Q(assigned_to_id=assigned_to, is_deleted=False)
            else:
                filters |= Q(assigned_to__isnull=True, is_deleted=False)  # Include unassigned tickets

            tickets = Ticket.objects.filter(filters).order_by("-created_at")

            # Apply pagination
            paginator = CustomPagination()
            paginated_tickets = paginator.paginate_queryset(tickets, request)

            if paginated_tickets is None:
                return Response(
                    {"status": 0, "code": 400, "message": "Invalid page number or no data found."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            serializer = TicketSerializer(paginated_tickets, many=True)
            return paginator.get_paginated_response(serializer.data)

        except Exception as e:
            return Response(
                {
                    "status": 0,
                    "code": 500,
                    "message": f"Something went wrong. Please contact admin! Error: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )



class TicketStatusTrackingView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, ticket_id):
        try:
            status_tracking = StatusTracking.objects.filter(ticket_id=ticket_id).order_by('created_at')
            serializer = StatusTrackingSerializer(status_tracking, many=True)
            return Response({
                "status": 1,
                "code": 200,
                "message": "Status tracking retrieved successfully",
                "data": serializer.data
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "status": 0,
                "code": 400,
                "message": f"Something went wrong! Error: {str(e)}"
            }, status=status.HTTP_400_BAD_REQUEST)



class TicketUpdatePartialView(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, *args, **kwargs):
        try:
            pk = kwargs.get("pk")  # Ensure 'pk' is retrieved correctly
            ticket = self.get_object(pk)  # Pass 'pk' to get_object method
            
            # Fix: Pass 'ticket' instance and request data to the serializer
            serializer = TicketUpdateSerializer(ticket, data=request.data, partial=True)

            old_status = ticket.status.id
            old_assigned_to = ticket.assigned_to
            user_id = request.user.id  

            if serializer.is_valid():
                new_status = serializer.validated_data.get('status', ticket.status.id)
                new_assigned_to = serializer.validated_data.get('assigned_to', ticket.assigned_to)

                if isinstance(new_status, str) and new_status.isdigit():
                    new_status = int(new_status)

                # If Admin or Superadmin updates assignee, set due date
                if old_assigned_to != new_assigned_to and 'due_date' in serializer.validated_data:
                    ticket.due_date = serializer.validated_data['due_date']

                serializer.save()

                # **Update ChatList** when ticket is updated
                chat_message = f"Ticket updated: {ticket.title} (ID: {ticket.ticket_id})"

                # Ensure `created_by_id` is never null
                created_by_id = ticket.created_by.id if ticket.created_by else user_id  

                # Notification
                Notification.objects.create(
                    ticket=ticket,
                    message=chat_message,
                    is_read=False,
                    created_by_id=created_by_id,  # Ensure ID is never null
                    updated_by_id=user_id,
                    from_user=ticket.updated_by,
                    to_user=ticket.created_by,
                )

                # If status has changed, create a tracking entry
                if old_status != new_status:
                    StatusTracking.objects.create(
                        ticket=ticket,
                        current_status=new_status,
                        updated_by_id=user_id,
                        created_by_id=created_by_id,
                        created_at=now(),
                    )

                # **Track Changes for Chat Updates**
                update_messages = []
                if old_status != new_status:
                    update_messages.append(f"Status changed to {ticket.status}")
                if old_assigned_to != new_assigned_to:
                    assigned_to_name = getattr(ticket.assigned_to, "get_full_name", lambda: "N/A")()
                    update_messages.append(f"Assigned to {assigned_to_name}")

                # If any updates were made, send a chat message
                if update_messages:
                    update_text = " | ".join(update_messages)
                    chat_message = TicketChatMessage.objects.create(
                        ticket=ticket,
                        sender=request.user,
                        message=f"Ticket updated: {update_text}",
                        timestamp=now()
                    )

                    # **Send WebSocket Notification**
                    channel_layer = get_channel_layer()
                    async_to_sync(channel_layer.group_send)(
                        f"ticket_{ticket.ticket_id}",
                        {
                            "type": "ticket_chat_message",
                            "message": {
                                "id": chat_message.id,
                                "ticket": ticket.ticket_id,
                                "sender_name": f"{request.user.first_name} {request.user.last_name}",
                                "message": chat_message.message,
                                "timestamp": chat_message.timestamp.isoformat(),
                            }
                        }
                    )
                    return Response(
                        {
                            "status": 1,
                            "code": 200,
                            "message": "Ticket updated successfully",
                            "data": serializer.data
                        },
                        status=status.HTTP_200_OK
                    )

                else:
                    return Response(
                        {
                            "status": 0,
                            "code": 400,
                            "message": "Ticket update failed",
                            "errors": serializer.errors
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )

        except Exception as e:
            return Response(
                {
                    "status": 0,
                    "code": 400,
                    "message": f"Something went wrong. Please contact admin! Error: {str(e)}"
                },
                status=status.HTTP_400_BAD_REQUEST
            )

    def get_object(self, pk):
        return get_object_or_404(Ticket, pk=pk)

class TicketStatusListView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        statusDetail = Status.objects.all()
        serializer = TicketStatusSerializer(statusDetail, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)
    
class TicketApprovalStatusListView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        statusDetail = ApprovalStatus.objects.all()
        serializer = TicketApprovalStatusSerializer(statusDetail, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, ticket_id):
        ticket = get_object_or_404(Ticket, ticket_id=ticket_id)
        # user = get_object_or_404(User, email=email)

        # token = default_token_generator.make_token(user)
        # Save previous state for tracking changes
        old_data = {
            'status': ticket.status.name.lower() if ticket.status else None,
            'assigned_to': ticket.assigned_to,
            'due_date': ticket.due_date,
            'category': ticket.category
        }

        # Set tracking values for signal
        ticket._previous_status = old_data['status']
        ticket._previous_assigne = old_data['assigned_to']

        # Handle approval logic
        is_approved = request.data.get("is_approved", False)
        ticket.is_approved = is_approved
        ticket.approved_by = request.user

        if is_approved:
            ticket.status = Status.objects.get(name="Open")
            ticket.approvel_message = request.data.get("approvel_message", "")
        else:
            ticket.status = Status.objects.get(name="Cancelled")
            ticket.cancel_message = request.data.get("cancel_message", "")

        # Optional assigned_to
        assigned_to_id = request.data.get("assigned_to")
        if assigned_to_id:
            try:
                ticket.assigned_to = User.objects.get(id=assigned_to_id)
            except User.DoesNotExist:
                return Response({"message": f"User ID {assigned_to_id} not found."},
                                status=status.HTTP_400_BAD_REQUEST)
        else:
            ticket.assigned_to = None

        # Optional due_date/category/etc handled via serializer
        serializer = TicketSerializer(ticket, data=request.data, partial=True)
        files = request.FILES.getlist('attachement')

        if serializer.is_valid():
            serializer.save()
            handle_ticket_update_logic(ticket, request, serializer, files, old_data)
            return Response({"message": "Ticket approval processed."}, status=status.HTTP_200_OK)
        else:
            return Response({"message": "Invalid data", "errors": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

  
class TicketPriorityListView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        priority_levels = PriorityLevel.objects.all()
        serializer = TicketPrioritySerializer(priority_levels, many=True)  # ✅ Use the correct serializer
        return Response(serializer.data, status=status.HTTP_200_OK)

class ChatListViewSet(viewsets.ModelViewSet):
    queryset = ChatList.objects.all()
    serializer_class = ChatListSerializer
    permission_classes = [IsAuthenticated]  # Ensure only authenticated users can access

    def get_queryset(self):
        ticket_id = self.request.query_params.get('ticket_id')
        if ticket_id:
            return ChatList.objects.filter(ticket_id=ticket_id).order_by('created_at')
        return ChatList.objects.none()

class FeedbackListCreateView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        feedbacks = Feedback.objects.all()
        serializer = FeedbackSerializer(feedbacks, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = FeedbackSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
class FeedbackDetailView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, feedback_id):
        try:
            feedback = Feedback.objects.get(feedback_id=feedback_id)
            serializer = FeedbackSerializer(feedback)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Feedback.DoesNotExist:
            return Response({"error": "Feedback not found"}, status=status.HTTP_404_NOT_FOUND)

class NotificationView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request):
        """ Get all notifications """
        notifications = Notification.objects.all().order_by('created_at')
        serializer = NotificationSerializer(notifications, many=True)
        return Response({
            "status": 1,
            "code": 200,
            "message": "Notifications fetched successfully",
            "data": serializer.data
        }, status=status.HTTP_200_OK)


class UserNotificationView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, user_id):
        """Fetch notifications where is_clear=False and mark them as read"""
        user = get_object_or_404(User, id=user_id)

        # if user.role_id == "R001":
        #     # Fetch all unread notifications that are not cleared
        #     notifications = Notification.objects.filter(is_clear=False).order_by('-created_at')
        # else:
            # Regular users only get their unread, uncleared notifications
        notifications = Notification.objects.filter(to_user=user, is_clear=False).order_by('-created_at')

        # Mark fetched notifications as read
        notifications.update(is_read=True)

        serializer = NotificationSerializer(notifications, many=True)
        return Response({
            "status": 1,
            "code": 200,
            "message": f"Notifications fetched successfully for User ID {user_id}",
            "data": serializer.data
        }, status=status.HTTP_200_OK)

class ClearUserNotificationsView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, user_id):
        """Clear all notifications for the user"""
        user = get_object_or_404(User, id=user_id)

        if user.role_id == "R001":
            notifications = Notification.objects.all()
        else:
            notifications = Notification.objects.filter(to_user=user)

        # Mark notifications as cleared
        notifications.update(is_clear=True)

        return Response({
            "status": 1,
            "code": 200,
            "message": f"Notifications cleared for User ID {user_id}"
        }, status=status.HTTP_200_OK)

class UpdateNotificationsView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request):
        try:
            # Retrieve the array of notification IDs from the payload
            notification_ids = request.data.get("notifications")
            if not isinstance(notification_ids, list):
                return Response({
                    "status": 0,
                    "code": 400,
                    "message": "Invalid payload. 'notifications' must be a list."
                }, status=status.HTTP_400_BAD_REQUEST)

            # Filter notifications with IDs in the provided array
            notifications = Notification.objects.filter(id__in=notification_ids)
            
            # Update the is_read field to 0 (0 is equivalent to False)
            updated_count = notifications.update(is_read=1, is_clear=1)

            return Response({
                "status": 1,
                "code": 200,
                "message": f"Updated {updated_count} notifications.",
                "data": notification_ids
            }, status=status.HTTP_200_OK)
        except Exception as e:
            # Log the exception as needed
            return Response({
                "status": 0,
                "code": 500,
                "message": "Internal server error.",
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
  

class TicketDetailView(APIView):
    permission_classes=[IsAuthenticated]
    def get(self, request, ticket_id, *args, **kwargs):
        try:
            ticket = Ticket.objects.get(ticket_id=ticket_id)
            serializer = TicketSerializer(ticket)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Ticket.DoesNotExist:
            return Response({"error": "Ticket not found"}, status=status.HTTP_404_NOT_FOUND)


class FeedbackListCreateView(APIView):
    permission_classes=[IsAuthenticated]
    def get(self, request):
        feedbacks = Feedback.objects.select_related('feedback_value').all()
        serializer = FeedbackSerializer(feedbacks, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        serializer = FeedbackSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class FeedbackDetailView(APIView):
    permission_classes=[IsAuthenticated]
    def get(self, request, pk):  
        try:
            feedback = Feedback.objects.select_related('feedback_value').get(feedback_id=pk)
            serializer = FeedbackSerializer(feedback)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Feedback.DoesNotExist:
            return Response({"error": "Feedback not found"}, status=status.HTTP_404_NOT_FOUND)


@csrf_exempt
def update_fcm_token(request):
    """API to update the FCM token for a user."""
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            user_id = data.get("user_id")
            fcm_token = data.get("fcm_token")

            # Find the user
            user = User.objects.filter(id=user_id).first()
            if user:
                user.fcm_token = fcm_token
                user.save()
                return JsonResponse({"message": "FCM Token updated successfully!"}, status=200)
            
            return JsonResponse({"message": "User not found"}, status=404)
        
        except Exception as e:
            return JsonResponse({"message": f"Error updating FCM token: {str(e)}"}, status=500)
    
    return JsonResponse({"message": "Invalid request method"}, status=400)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_feedback_values(request):
    feedback_values = FeedbackValue.objects.all()  # Fetch all feedback values
    serializer = FeedbackValueSerializer(feedback_values, many=True)  
    return Response(serializer.data)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_feedback_value(request):
    serializer = FeedbackSerializer(data=request.data)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
