from django.db import models
from apps.user_management.models import User
# from apps.ticket_management.models import Ticket
# from apps.ticket_management.models import Ticket


class Category(models.Model):
    cat_name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, related_name='categories_created', on_delete=models.CASCADE,null=False,
        blank=False,
        default=1 )
    updated_by = models.ForeignKey(User, related_name='categories_updated', on_delete=models.CASCADE,null=False,
        blank=False,
        default=1)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def __str__(self):
        return self.cat_name


class SubCategory(models.Model):
    category = models.ForeignKey(Category, related_name='subcategories', on_delete=models.CASCADE,null=True)
    subcat_name = models.Char<PERSON>ield(max_length=255)
    is_active = models.<PERSON><PERSON>anField(default=True)
    created_by = models.ForeignKey(User, related_name='subcategories_created', on_delete=models.CASCADE,null=False,
        blank=False,
        default=1)
    updated_by = models.ForeignKey(User, related_name='subcategories_updated', on_delete=models.CASCADE,null=False,
        blank=False,
        default=1)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def __str__(self):
        return self.subcat_name

    class Meta:
        unique_together = ('subcat_name', 'category')