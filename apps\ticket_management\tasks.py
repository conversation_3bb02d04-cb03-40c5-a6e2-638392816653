from celery import shared_task
from datetime import timedelta
from django.utils.timezone import now, make_aware
from django.db.models import OuterRef
from apps.ticket_management.models import Ticket, Status, StatusTracking, FeedbackValue, Feedback

@shared_task
def update_solved_tickets_to_closed():
    """
    Updates tickets from 'solved' to 'closed' if they have been in 'solved' status for more than 72 hours.
    Also logs this change in the StatusTracking model.
    """

    # Get the status objects for 'solved' and 'closed'
    solved_status = Status.objects.filter(name='solved').first()
    closed_status = Status.objects.filter(name='closed').first()

    if not solved_status or not closed_status:
        return "Error: 'solved' or 'closed' status not found in the database."

    # Define the threshold time (72 hours ago)
    threshold_time = now() - timedelta(minutes=5)

    # Ensure threshold_time is timezone-aware
    if threshold_time.tzinfo is None:
        threshold_time = make_aware(threshold_time)

    # Get all tickets where the last status was 'solved' and older than 72 hours
    tickets_to_update = Ticket.objects.filter(
        status=solved_status,
        ticket_id__in=StatusTracking.objects.filter(
            current_status=solved_status,
            updated_at__lte=threshold_time
        ).values('ticket_id')
    )

    feedback_value_5 = FeedbackValue.objects.filter(value_id=5).first()
    if not feedback_value_5:
        return "Error: No FeedbackValue found with value_id=5."

    updated_count = 0

    # Loop through each ticket, update status, and create a StatusTracking entry
    for ticket in tickets_to_update:
        # Update ticket status
        ticket.status = closed_status
        ticket.closed_at = now()
        ticket.auto_closed = True
        ticket.save()

        # Create a new StatusTracking record for this change
        StatusTracking.objects.create(
            ticket=ticket,
            current_status=closed_status,  # Correct status field
            updated_by=ticket.created_by,
            created_by=ticket.created_by,
            created_at=now(),
            updated_at=now(),
        )

        Feedback.objects.create(
            ticket=ticket,
            user=ticket.created_by,        # Or another user if needed
            feedback_value=feedback_value_5,
            reason="Auto Closed the Ticket",
        )

        updated_count += 1

    return f"Updated {updated_count} tickets from 'solved' to 'closed'."
