#!/usr/bin/env python
"""
<PERSON><PERSON>t to create a migration for adding original_filename field
Run this to add the original_filename field to TicketChatAttachenment model
"""

import os
import sys
import django
from django.conf import settings

# Set up Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ticketing_system.settings')
django.setup()

from django.core.management import execute_from_command_line

def create_migration():
    """Create migration for adding original_filename field"""
    
    print("🔧 Creating migration for original_filename field")
    print("=" * 60)
    
    # First, let's add the field to the model
    model_file_path = "apps/ticket_management/models.py"
    
    print(f"📝 You need to manually add this field to {model_file_path}:")
    print()
    print("In the TicketChatAttachenment model, add this field:")
    print("```python")
    print("class TicketChatAttachenment(models.Model):")
    print("    ticket = models.ForeignKey(\"ticket_management.Ticket\", related_name=\"attachments\", on_delete=models.CASCADE)")
    print("    attachement = models.FileField(upload_to=\"files/\", max_length=300, null=True, blank=True)")
    print("    original_filename = models.CharField(max_length=255, null=True, blank=True)  # ADD THIS LINE")
    print("    chat = models.ForeignKey(\"websocket.TicketChatMessage\", on_delete=models.SET_NULL, null=True, blank=True)")
    print("    created_at = models.DateTimeField(auto_now_add=True)")
    print("    updated_at = models.DateTimeField(auto_now=True)")
    print("```")
    print()
    
    print("📝 Then run these commands:")
    print("1. python manage.py makemigrations ticket_management")
    print("2. python manage.py migrate")
    print()
    
    print("🔧 Alternative: Create the migration file manually")
    print("=" * 60)
    
    migration_content = '''# Generated migration for adding original_filename field

from django.db import migrations, models

class Migration(migrations.Migration):

    dependencies = [
        ('ticket_management', '0018_alter_ticketchatattachenment_chat'),  # Update this to your latest migration
    ]

    operations = [
        migrations.AddField(
            model_name='ticketchatattachenment',
            name='original_filename',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
'''
    
    print("Create this file: apps/ticket_management/migrations/0019_add_original_filename.py")
    print("```python")
    print(migration_content)
    print("```")

def update_views_to_use_original_filename():
    """Show how to update views to store original filename"""
    
    print("\n🔧 Update views.py to store original filename")
    print("=" * 60)
    
    print("In the file upload section, change:")
    print("```python")
    print("# OLD:")
    print("TicketChatAttachenment.objects.create(")
    print("    ticket=ticket,")
    print("    attachement=saved_path")
    print(")")
    print()
    print("# NEW:")
    print("TicketChatAttachenment.objects.create(")
    print("    ticket=ticket,")
    print("    attachement=saved_path,")
    print("    original_filename=original_name  # Store the original filename")
    print(")")
    print("```")

def update_serializer_to_show_original_filename():
    """Show how to update serializer to display original filename"""
    
    print("\n🔧 Update serializer to show original filename")
    print("=" * 60)
    
    print("In TicketDocumentSerializer, add:")
    print("```python")
    print("class TicketDocumentSerializer(serializers.ModelSerializer):")
    print("    file_url = serializers.SerializerMethodField()")
    print("    display_name = serializers.SerializerMethodField()  # ADD THIS")
    print("    ")
    print("    class Meta:")
    print("        model = TicketChatAttachenment")
    print("        fields = '__all__'")
    print("    ")
    print("    def get_file_url(self, obj):")
    print("        if obj.attachement:")
    print("            return obj.attachement.url")
    print("        return None")
    print("    ")
    print("    def get_display_name(self, obj):  # ADD THIS METHOD")
    print("        # Return original filename if available, otherwise use the stored filename")
    print("        if obj.original_filename:")
    print("            return obj.original_filename")
    print("        elif obj.attachement:")
    print("            return obj.attachement.name.split('/')[-1]")
    print("        return 'Unknown file'")
    print("```")

def create_complete_solution():
    """Create the complete solution files"""
    
    print("\n📁 Complete Solution Files")
    print("=" * 60)
    
    # Model update
    print("1. Update apps/ticket_management/models.py:")
    print("   Add: original_filename = models.CharField(max_length=255, null=True, blank=True)")
    print()
    
    # Views update  
    print("2. Views are already updated with the new filename logic")
    print()
    
    # Serializer update
    print("3. Update apps/ticket_management/serializers.py:")
    print("   Add display_name field to show original filename to users")
    print()
    
    print("📋 Benefits of this approach:")
    print("✅ Files are stored with ASCII-safe names (no server errors)")
    print("✅ Original filenames are preserved in database")
    print("✅ Users see meaningful filenames in the UI")
    print("✅ Filenames are partially readable even in storage")
    print("✅ Fallback mechanisms for edge cases")

if __name__ == "__main__":
    create_migration()
    update_views_to_use_original_filename()
    update_serializer_to_show_original_filename()
    create_complete_solution()
