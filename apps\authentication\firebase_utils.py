# import requests
# import google.auth
# import google.auth.transport.requests
# from google.oauth2 import service_account
# from django.core.cache import cache
# from django.conf import settings

# # Path to your Firebase service account JSON file
# SERVICE_ACCOUNT_FILE = {settings.FIREBASE_CREDENTIALS_PATH}

# # Define FCM API endpoint
# FCM_URL = "https://fcm.googleapis.com/v1/projects/nex-ticket-edf83/messages:send"

# # Function to get GCP Access Token
# def get_gcp_access_token():
#     """Generates an OAuth 2.0 access token for Firebase Cloud Messaging."""
    
#     SCOPES = ["https://www.googleapis.com/auth/cloud-platform"]

#     credentials = service_account.Credentials.from_service_account_file(
#         SERVICE_ACCOUNT_FILE, scopes=SCOPES
#     )

#     # Refresh credentials to get a valid token
#     credentials.refresh(google.auth.transport.requests.Request())

#     return credentials.token

# # Cached version to prevent frequent API calls
# def get_cached_gcp_access_token():
#     """Fetches GCP access token and caches it for 1 hour."""
    
#     token = cache.get("gcp_access_token")

#     if not token:
#         token = get_gcp_access_token()
#         cache.set("gcp_access_token", token, timeout=3600)  # Cache for 1 hour

#     return token

# # Function to send FCM notification
# def send_fcm_notification(fcm_token, title, body):
#     """Sends a push notification via Firebase Cloud Messaging."""
    
#     access_token = get_cached_gcp_access_token()  # Get cached access token

#     headers = {
#         "Authorization": f"Bearer {access_token}",
#         "Content-Type": "application/json"
#     }

#     payload = {
#         "message": {
#             "token": fcm_token,  # User's FCM token
#             "notification": {
#                 "title": title,
#                 "body": body
#             }
#         }
#     }

#     response = requests.post(FCM_URL, headers=headers, json=payload)

#     if response.status_code == 200:
#         return {"success": True, "message": "Notification sent successfully!"}
#     else:
#         return {"success": False, "error": response.text}
import firebase_admin
from firebase_admin import credentials, messaging
from django.conf import settings

# Initialize Firebase Admin SDK if not already initialized
if not firebase_admin._apps:
    cred = credentials.Certificate(settings.FIREBASE_CREDENTIALS_PATH)
    firebase_admin.initialize_app(cred)

def send_push_notification(user, title, body):
    """
    Send a push notification to a specific user
    # """
    if not user:
        return {"success": False, "message": "User not found"}  # Handle None user

    if not user.fcm_token:
        return {"success": False, "message": "User does not have an FCM token"}

    message = messaging.Message(
        notification=messaging.Notification(title=title, body=body),
        token=user.fcm_token,  # FCM token from User model
    )

    try:
        response = messaging.send(message)
        return {"success": True, "message": f"Notification sent: {response}"}
    except Exception as e:
        return {"success": False, "message": f"Error sending notification: {str(e)}"}
