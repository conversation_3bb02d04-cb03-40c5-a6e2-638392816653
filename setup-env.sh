#!/bin/bash

# Define color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
BOLD='\033[1m'
NC='\033[0m' # No Color

LOGFILE="deploy.log"
ENV_ONLY=false

# Print header
echo -e "${BLUE}=================================${NC}"
echo -e "${BOLD}Django Setup Environment Script${NC}"
echo -e "${BLUE}=================================${NC}"

# Check for --env-only flag
if [[ "$1" == "--env-only" ]]; then
    ENV_ONLY=true
    echo -e "${YELLOW}Running in environment-only mode. Will create .env file but not start server.${NC}"
fi

log() {
    local msg="$1"
    local level="${2:-INFO}"
    local color="${NC}"

    case $level in
        "INFO")
            color="${GREEN}"
            ;;
        "WARNING")
            color="${YELLOW}"
            ;;
        "ERROR")
            color="${RED}"
            ;;
        "STEP")
            color="${CYAN}"
            ;;
        "SUCCESS")
            color="${BOLD}${GREEN}"
            ;;
    esac

    echo -e "${color}$(date '+%Y-%m-%d %H:%M:%S') - $msg${NC}" | tee -a "$LOGFILE"
}

exit_on_error() {
    local status=$1
    local step="$2"
    if [ $status -ne 0 ]; then
        log "ERROR: Failed at step: $step. Exiting." "ERROR"
        exit $status
    fi
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if command -v lsof &> /dev/null; then
        if lsof -i :$port -t &> /dev/null; then
            return 0  # Port is in use
        else
            return 1  # Port is free
        fi
    elif command -v netstat &> /dev/null; then
        if netstat -tuln | grep -q ":$port "; then
            return 0  # Port is in use
        else
            return 1  # Port is free
        fi
    else
        log "Neither lsof nor netstat is available. Cannot check if port $port is in use." "WARNING"
        return 1  # Assume port is free
    fi
}

# Function to free port 8000
free_port_8000() {
    if check_port 8000; then
        log "Port 8000 is in use. Attempting to free it..." "WARNING"

        if command -v lsof &> /dev/null; then
            local pid=$(lsof -i :8000 -t)
            if [ -n "$pid" ]; then
                log "Killing process $pid using port 8000..." "INFO"
                kill -9 $pid
                sleep 1
                if check_port 8000; then
                    log "Failed to free port 8000. Please manually terminate the process and try again." "ERROR"
                    return 1
                else
                    log "Successfully freed port 8000." "SUCCESS"
                    return 0
                fi
            fi
        elif command -v netstat &> /dev/null && command -v grep &> /dev/null && command -v awk &> /dev/null; then
            # Try to get PID using netstat (less reliable)
            local pid=$(netstat -tuln | grep ":8000 " | awk '{print $7}' | cut -d'/' -f1)
            if [ -n "$pid" ]; then
                log "Killing process $pid using port 8000..." "INFO"
                kill -9 $pid
                sleep 1
                if check_port 8000; then
                    log "Failed to free port 8000. Please manually terminate the process and try again." "ERROR"
                    return 1
                else
                    log "Successfully freed port 8000." "SUCCESS"
                    return 0
                fi
            fi
        fi

        log "Could not identify the process using port 8000. Please free the port manually." "ERROR"
        return 1
    else
        log "Port 8000 is available." "SUCCESS"
        return 0
    fi
}

# Function to check database connection
check_db_connection() {
    local db_host=$1
    local db_port=$2
    local db_name=$3
    local db_user=$4
    local db_password=$5

    log "Testing database connection to $db_host:$db_port/$db_name as $db_user..." "STEP"

    # Check if mysql client is available
    if command -v mysql &> /dev/null; then
        # Try to connect to the database with verbose output
        log "Attempting connection with mysql client..." "INFO"
        mysql_output=$(mysql -h "$db_host" -P "$db_port" -u "$db_user" -p"$db_password" --connect-timeout=10 -e "SELECT 1" "$db_name" 2>&1)
        mysql_status=$?

        if [ $mysql_status -eq 0 ]; then
            log "Database connection successful with mysql client!" "SUCCESS"
            return 0
        else
            log "Failed to connect to database using mysql client. Error: $mysql_output" "ERROR"

            # Try with different options
            log "Trying alternative connection method with mysql client..." "INFO"
            mysql_output=$(mysql -h "$db_host" -P "$db_port" -u "$db_user" -p"$db_password" --protocol=TCP --connect-timeout=10 -e "SELECT 1" "$db_name" 2>&1)
            mysql_status=$?

            if [ $mysql_status -eq 0 ]; then
                log "Database connection successful with mysql client using TCP protocol!" "SUCCESS"
                return 0
            else
                log "Failed to connect with alternative method. Error: $mysql_output" "ERROR"
            fi
        fi
    else
        log "mysql client not found." "INFO"
    fi

    # Try using Python
    log "Trying to connect using Python..." "INFO"

    # Create a temporary Python script to test the connection
    cat > db_connection_test.py << EOL
import sys
import time

# Try PyMySQL first
try:
    import pymysql
    print("Using PyMySQL driver")
    try:
        # Connect with PyMySQL
        conn = pymysql.connect(
            host='$db_host',
            port=$db_port,
            user='$db_user',
            password='$db_password',
            database='$db_name',
            connect_timeout=10
        )
        cursor = conn.cursor()
        cursor.execute('SELECT 1')
        cursor.fetchone()
        cursor.close()
        conn.close()
        print("Connection successful with PyMySQL!")
        sys.exit(0)
    except Exception as e:
        print(f"PyMySQL connection failed: {e}")
        # Continue to try MySQLdb
except ImportError:
    print("PyMySQL not available")

# Try MySQLdb
try:
    import MySQLdb
    print("Using MySQLdb driver")
    try:
        # Connect with MySQLdb
        conn = MySQLdb.connect(
            host='$db_host',
            port=$db_port,
            user='$db_user',
            passwd='$db_password',
            db='$db_name',
            connect_timeout=10
        )
        cursor = conn.cursor()
        cursor.execute('SELECT 1')
        cursor.fetchone()
        cursor.close()
        conn.close()
        print("Connection successful with MySQLdb!")
        sys.exit(0)
    except Exception as e:
        print(f"MySQLdb connection failed: {e}")
except ImportError:
    print("MySQLdb not available")

# If we get here, both connection attempts failed
print("All connection attempts failed")
sys.exit(1)
EOL

    # Run the Python script
    python_output=$($PYTHON_CMD db_connection_test.py 2>&1)
    python_status=$?

    if [ $python_status -eq 0 ]; then
        log "Database connection successful using Python! Output: $python_output" "SUCCESS"
        rm db_connection_test.py
        return 0
    else
        log "Failed to connect to database using Python. Error: $python_output" "ERROR"

        # Print connection details for debugging (mask password)
        masked_password=$(echo "$db_password" | sed 's/./*/g')
        log "Connection details: mysql -h $db_host -P $db_port -u $db_user -p$masked_password $db_name" "INFO"

        # Check if host is reachable
        log "Checking if host is reachable..." "INFO"
        if ping -c 1 -W 2 "$db_host" &> /dev/null; then
            log "Host $db_host is reachable." "INFO"
        else
            log "Host $db_host is not reachable. Check network connection or firewall settings." "WARNING"
        fi

        rm db_connection_test.py
        return 1
    fi
}

# Get current Git branch
log "Detecting Git branch..." "STEP"
branch=$(git rev-parse --abbrev-ref HEAD)
log "Current Git branch: $branch" "INFO"

# Handle Jenkins environment where we might be in detached HEAD state
if [ "$branch" = "HEAD" ]; then
    log "Detected detached HEAD state, trying to determine actual branch..." "WARNING"
    # Try to get branch from Jenkins environment variables
    if [ ! -z "$BRANCH_NAME" ]; then
        branch=$BRANCH_NAME
        log "Using Jenkins BRANCH_NAME: $branch" "INFO"
    elif [ ! -z "$GIT_BRANCH" ]; then
        # GIT_BRANCH often includes the remote, like 'origin/main'
        branch=$(echo $GIT_BRANCH | sed 's|^origin/||')
        log "Using Jenkins GIT_BRANCH: $branch" "INFO"
    else
        # Try to get the branch from the commit
        git_branch=$(git show -s --pretty=%D HEAD | grep -o 'origin/[^ ,]\+' | head -1 | sed 's|^origin/||')
        if [ ! -z "$git_branch" ]; then
            branch=$git_branch
            log "Detected branch from commit: $branch" "INFO"
        else
            log "Could not determine branch name. Using 'development' as default for Jenkins." "WARNING"
            branch="development"
        fi
    fi
fi

write_config() {
    local file=$1
    local db_name=$2
    local db_user=$3
    local db_password=$4
    local db_host=$5
    local db_port=$6
    local backend_url=$7
    local debug_value=$8

    log "Creating environment configuration file: $file" "STEP"

    {
        echo "DEBUG=$debug_value"
        echo ""
        echo "# Database Configuration"
        echo "DB_ENGINE=django.db.backends.mysql"
        echo "DB_NAME=$db_name"
        echo "DB_USER=$db_user"
        echo "DB_PASSWORD=$db_password"
        echo "DB_HOST=$db_host"
        echo "DB_PORT=$db_port"
        echo "DB_OPTIONS={\"ssl\": false, \"charset\": \"utf8mb4\"}"
        echo ""
        echo "BACKEND_URL=$backend_url"
        echo ""
        echo "# SMTP Email Configuration"
        echo "EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend"
        echo "EMAIL_HOST=smtp.zeptomail.in"
        echo "EMAIL_PORT=587"
        echo "EMAIL_USE_TLS=True"
        echo "EMAIL_HOST_USER=<EMAIL>"
        echo "EMAIL_HOST_PASSWORD=PHtE6r0JELzu2Gcpo0JWtKewRcCiYIss/epmeAEU4dlHC/8ETk1Sqtkjlme1rRguB/ATF/WewIw9tr/OtL/WLDu8MWpOXGqyqK3sx/VYSPOZsbq6x00asV4dd0LVU4bqdtdr1CDTvdjZNA=="
        echo ""
        echo "ROOT_URLCONF=ticketing_system.urls"
        echo ""
        echo "# Allowed hosts and CSRF trusted origins"
        echo "ALLOWED_HOSTS=[\"***************\", \"internal-project.nexware-global.com\", \"nex-ticket.nexware-global.com\", \"ticket.nexware-global.com\", \"localhost\", \"127.0.0.1\"]"
        #echo "ALLOWED_HOSTS = ["*"]"
        echo "CSRF_TRUSTED_ORIGINS=[\"http://localhost:8000\", \"http://internal-project.nexware-global.com:9018\", \"http://internal-project.nexware-global.com:9019\", \"https://ticket.nexware-global.com:9049\", \"https://internal-project.nexware-global.com:8001\"]"
    } > "$file"

    log "Config file '$file' created with DEBUG=$debug_value and database config for DB '$db_name'" "SUCCESS"
}

# Set environment-specific configuration
log "Checking for existing environment configuration..." "STEP"
echo -e "${BLUE}--------------------------------${NC}"
echo -e "${BOLD}Environment Configuration${NC}"
echo -e "${BLUE}--------------------------------${NC}"

# Check if .env file already exists
if [ -f ".env" ]; then
    log "Found existing .env file. Using existing configuration." "SUCCESS"
    export DJANGO_ENV_FILE=".env"
else
    log "No .env file found. Creating new configuration..." "INFO"

    if [ "$branch" == "development" ]; then
        log "Detected DEVELOPMENT environment" "INFO"
        write_config ".env" "nex_ticket_stg_db" "nex-ticketing-stg" "RsRtW8u96@N" "***************" "3306" "http://internal-project.nexware-global.com:9018/" "False"
        export DJANGO_ENV_FILE=".env"

        # Check database connection for staging environment
        echo -e "${BLUE}--------------------------------${NC}"
        echo -e "${BOLD}Staging Database Connection Check${NC}"
        echo -e "${BLUE}--------------------------------${NC}"

        # Determine Python command if not already set
        if [ -z "$PYTHON_CMD" ]; then
            if command -v python &> /dev/null; then
                PYTHON_CMD="python"
            elif command -v python3 &> /dev/null; then
                PYTHON_CMD="python3"
                log "Using 'python3' command instead of 'python'" "INFO"
            else
                log "ERROR: Neither 'python' nor 'python3' commands are available. Cannot check database connection." "ERROR"
                PYTHON_CMD=""
            fi
        fi

        if [ -n "$PYTHON_CMD" ]; then
            # Install PyMySQL if needed for the connection check
            if ! $PYTHON_CMD -c "import pymysql" &> /dev/null; then
                log "Installing PyMySQL for database connection check..." "INFO"
                if command -v pip &> /dev/null; then
                    pip install pymysql
                elif command -v pip3 &> /dev/null; then
                    pip3 install pymysql
                else
                    $PYTHON_CMD -m pip install pymysql
                fi
            fi

            # Check database connection
            check_db_connection "***************" "3306" "nex_ticket_stg_db" "nex-ticketing-stg" "RsRtW8u96@N"
            if [ $? -ne 0 ]; then
                log "WARNING: Could not connect to staging database. Please check your network connection and database credentials." "WARNING"
                log "Continuing with setup, but you may encounter database-related errors." "WARNING"
            fi
        else
            log "Skipping database connection check due to missing Python." "WARNING"
        fi

    elif [[ "$branch" == feature/* ]]; then
        log "Detected FEATURE branch environment" "INFO"
        write_config ".env" "ticketing_tool" "root" "admin@123" "localhost" "3306" "http://localhost:3000/" "True"
        export DJANGO_ENV_FILE=".env"

    elif [ "$branch" == "main" ] || [[ "$branch" == release/* ]]; then
        log "Detected PRODUCTION environment" "INFO"
        write_config ".env" "nex_ticket_db" "nex-ticketing" "nHH9Ky@RHgTDV" "***************" "3306" "https://ticket.nexware-global.com:9048/" "False"
        export DJANGO_ENV_FILE=".env"

    else
        log "Unknown branch '$branch'. Falling back to '.env.local'." "WARNING"
        write_config ".env.local" "ticketing_tool" "root" "admin@123" "localhost" "3306" "http://localhost:3000/" "True"
        export DJANGO_ENV_FILE=".env"
    fi
fi

log "Running Django with environment file: $DJANGO_ENV_FILE" "INFO"

echo -e "${BLUE}--------------------------------${NC}"
echo -e "${BOLD}Environment Setup${NC}"
echo -e "${BLUE}--------------------------------${NC}"

# Make manage.py executable
log "Making manage.py executable..." "STEP"
chmod +x manage.py
exit_on_error $? "chmod manage.py"

# Check for and activate virtual environment
VENV_ACTIVATED=false
log "Checking for virtual environment..." "STEP"

# Detect OS type
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
    # Windows environment (GitBash, Cygwin, etc.)
    log "Detected Windows environment: $OSTYPE" "INFO"

    if [ -d "venv" ]; then
        log "Activating virtual environment: venv" "INFO"
        if [ -f "venv/Scripts/activate" ]; then
            source venv/Scripts/activate
            exit_on_error $? "activate venv"
            VENV_ACTIVATED=true
        else
            log "Could not find activation script at venv/Scripts/activate" "WARNING"
        fi
    elif [ -d "django_env" ]; then
        log "Activating virtual environment: django_env" "INFO"
        if [ -f "django_env/Scripts/activate" ]; then
            source django_env/Scripts/activate
            exit_on_error $? "activate django_env"
            VENV_ACTIVATED=true
        else
            log "Could not find activation script at django_env/Scripts/activate" "WARNING"
        fi
    elif [ -d "env" ]; then
        log "Activating virtual environment: env" "INFO"
        if [ -f "env/Scripts/activate" ]; then
            source env/Scripts/activate
            exit_on_error $? "activate env"
            VENV_ACTIVATED=true
        else
            log "Could not find activation script at env/Scripts/activate" "WARNING"
        fi
    else
        log "No virtual environment found. Continuing with system Python..." "WARNING"
    fi
else
    # Unix/Linux environment
    log "Detected Unix/Linux environment: $OSTYPE" "INFO"

    if [ -d "venv" ]; then
        log "Activating virtual environment: venv" "INFO"
        source venv/bin/activate
        exit_on_error $? "activate venv"
        VENV_ACTIVATED=true
    elif [ -d "django_env" ]; then
        log "Activating virtual environment: django_env" "INFO"
        source django_env/bin/activate
        exit_on_error $? "activate django_env"
        VENV_ACTIVATED=true
    elif [ -d "env" ]; then
        log "Activating virtual environment: env" "INFO"
        source env/bin/activate
        exit_on_error $? "activate env"
        VENV_ACTIVATED=true
    else
        log "No virtual environment found. Continuing with system Python..." "WARNING"
    fi
fi

# Check if Django is installed in the virtual environment
if [ "$VENV_ACTIVATED" = true ]; then
    log "Checking for Django installation..." "STEP"
    if ! python -c "import django" &> /dev/null; then
        log "Django not found in virtual environment. Installing requirements..." "WARNING"
        pip install -r requirements.txt
        exit_on_error $? "pip install"
        log "Requirements installed successfully" "SUCCESS"
    else
        log "Django is already installed in the virtual environment." "SUCCESS"
    fi
fi

# If --env-only flag is set, exit here after creating the .env file
if [ "$ENV_ONLY" = true ]; then
    log "Environment file created successfully. Exiting as requested with --env-only flag." "SUCCESS"
    echo -e "${BLUE}=================================${NC}"
    exit 0
fi

echo -e "${BLUE}--------------------------------${NC}"
echo -e "${BOLD}Python Environment Setup${NC}"
echo -e "${BLUE}--------------------------------${NC}"

# Determine the correct Python command
log "Detecting Python command..." "STEP"

# Check if we're in a virtual environment
if [ "$VENV_ACTIVATED" = true ]; then
    # In a virtual environment, just use 'python'
    PYTHON_CMD="python"
    log "Using 'python' from virtual environment" "INFO"
else
    # Not in a virtual environment, need to check available commands
    PYTHON_CMD="python"
    if ! command -v python &> /dev/null; then
        if command -v python3 &> /dev/null; then
            PYTHON_CMD="python3"
            log "Using 'python3' command instead of 'python'" "INFO"
        else
            # On Windows, try python.exe
            if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
                if command -v python.exe &> /dev/null; then
                    PYTHON_CMD="python.exe"
                    log "Using 'python.exe' command" "INFO"
                elif command -v python3.exe &> /dev/null; then
                    PYTHON_CMD="python3.exe"
                    log "Using 'python3.exe' command" "INFO"
                else
                    log "ERROR: No Python command found. Please install Python." "ERROR"
                    exit 1
                fi
            else
                log "ERROR: Neither 'python' nor 'python3' commands are available. Please install Python." "ERROR"
                exit 1
            fi
        fi
    fi
fi

# Determine the correct pip command
log "Detecting pip command..." "STEP"

# Check if we're in a virtual environment
if [ "$VENV_ACTIVATED" = true ]; then
    # In a virtual environment, just use 'pip'
    PIP_CMD="pip"
    log "Using 'pip' from virtual environment" "INFO"
else
    # Not in a virtual environment, need to check available commands
    PIP_CMD="pip"
    if ! command -v pip &> /dev/null; then
        if command -v pip3 &> /dev/null; then
            PIP_CMD="pip3"
            log "Using 'pip3' command instead of 'pip'" "INFO"
        else
            # On Windows, try pip.exe
            if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
                if command -v pip.exe &> /dev/null; then
                    PIP_CMD="pip.exe"
                    log "Using 'pip.exe' command" "INFO"
                elif command -v pip3.exe &> /dev/null; then
                    PIP_CMD="pip3.exe"
                    log "Using 'pip3.exe' command" "INFO"
                else
                    log "WARNING: No pip command found. Will try to install with Python module." "WARNING"
                    PIP_CMD="$PYTHON_CMD -m pip"
                fi
            else
                log "WARNING: Neither 'pip' nor 'pip3' commands are available. Will try to install with Python module." "WARNING"
                PIP_CMD="$PYTHON_CMD -m pip"
            fi
        fi
    fi
fi

# Install PyMySQL if not already installed
log "Checking for PyMySQL..." "STEP"
if ! $PYTHON_CMD -c "import pymysql" &> /dev/null; then
    log "Installing PyMySQL..." "INFO"
    $PIP_CMD install pymysql
    if [ $? -ne 0 ]; then
        log "WARNING: Failed to install PyMySQL. Will try to continue without it." "WARNING"
    else
        log "PyMySQL installed successfully." "SUCCESS"
    fi
else
    log "PyMySQL is already installed." "SUCCESS"
fi

echo -e "${BLUE}--------------------------------${NC}"
echo -e "${BOLD}Django Configuration${NC}"
echo -e "${BLUE}--------------------------------${NC}"

# Verify .env file is properly loaded
log "Verifying .env file contents..." "STEP"
log "DB_USER: $(grep DB_USER .env | cut -d= -f2)" "INFO"
log "DB_HOST: $(grep DB_HOST .env | cut -d= -f2)" "INFO"

# Export environment variables from .env file to ensure they're available to Django
log "Exporting environment variables from .env file..." "STEP"
# Only export simple key=value pairs, skip arrays and complex values
while IFS='=' read -r key value; do
    # Skip empty lines and comments
    if [[ -z "$key" || "$key" == \#* ]]; then
        continue
    fi

    # Skip if value contains array brackets or special characters
    if [[ "$value" != *"["* && "$value" != *"]"* && "$value" != *","* ]]; then
        export "$key=$value"
        log "Exported: $key" "INFO"
    else
        log "Skipped exporting complex value: $key" "WARNING"
    fi
done < .env

echo -e "${BLUE}--------------------------------${NC}"
echo -e "${BOLD}Django Database Setup${NC}"
echo -e "${BLUE}--------------------------------${NC}"

# Check database connection
if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ] && [ -n "$DB_NAME" ] && [ -n "$DB_USER" ] && [ -n "$DB_PASSWORD" ]; then
    log "Verifying database connection with current environment settings..." "STEP"
    check_db_connection "$DB_HOST" "$DB_PORT" "$DB_NAME" "$DB_USER" "$DB_PASSWORD"
    if [ $? -ne 0 ]; then
        log "WARNING: Could not connect to database with current settings. Please check your network connection and database credentials." "WARNING"
        log "Continuing with setup, but you may encounter database-related errors." "WARNING"
    else
        log "Database connection verified successfully!" "SUCCESS"
    fi
else
    log "Missing database configuration variables. Skipping connection check." "WARNING"
fi

# Check if there are any migration changes
log "Checking for database migration changes..." "STEP"
migration_changes=$($PYTHON_CMD manage.py makemigrations --dry-run)
migration_status=$?

if [ $migration_status -ne 0 ]; then
    log "Error checking for migrations. Will attempt to run migrations anyway." "WARNING"
    needs_migration=true
elif [[ $migration_changes == *"No changes detected"* ]]; then
    log "No migration changes detected." "SUCCESS"
    needs_migration=false
else
    log "Migration changes detected." "INFO"
    needs_migration=true
fi

# Check if there are any pending migrations
pending_migrations=$($PYTHON_CMD manage.py showmigrations | grep -c "\[ \]")
if [ $pending_migrations -gt 0 ]; then
    log "Found $pending_migrations pending migrations that need to be applied." "INFO"
    needs_migration=true
fi

# Only run migrations if needed
if [ "$needs_migration" = true ]; then
    log "Making migrations..." "STEP"
    $PYTHON_CMD manage.py makemigrations --verbosity 2 | tee -a "$LOGFILE"
    exit_on_error $? "makemigrations"

    log "Applying migrations..." "STEP"
    $PYTHON_CMD manage.py migrate --verbosity 2 --noinput | tee -a "$LOGFILE"
    exit_on_error $? "migrate"

    log "Database migrations completed successfully." "SUCCESS"
else
    log "Skipping migrations as no changes were detected." "INFO"
fi

echo -e "${BLUE}--------------------------------${NC}"
echo -e "${BOLD}Static Files Setup${NC}"
echo -e "${BLUE}--------------------------------${NC}"

# Create staticfiles directory if it doesn't exist
STATIC_ROOT="$PWD/staticfiles"
log "Checking static files directory: $STATIC_ROOT" "STEP"

# Check if staticfiles directory exists and has content
if [ -d "$STATIC_ROOT" ] && [ "$(ls -A "$STATIC_ROOT" 2>/dev/null)" ]; then
    log "Static files directory already exists and has content." "SUCCESS"
    collect_static=false
else
    log "Creating static files directory: $STATIC_ROOT" "INFO"
    mkdir -p "$STATIC_ROOT"
    chmod -R 755 "$STATIC_ROOT"
    collect_static=true
fi

# Check if any static files have changed
if [ "$collect_static" = false ] && [ -n "$(find . -path "*/static/*" -newer "$STATIC_ROOT" -type f 2>/dev/null)" ]; then
    log "Detected changes in static files." "INFO"
    collect_static=true
fi

# Only start the development server if not in Jenkins environment
if [ -z "$JENKINS_URL" ] && [ -z "$JENKINS_HOME" ]; then
    # Free port 8000 if it's in use
    log "Checking port 8000 availability..." "STEP"
    free_port_8000

    # Check if we're on Windows
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
        # On Windows, use 127.0.0.1 instead of 0.0.0.0 to avoid binding issues
        log "Starting Django development server on 127.0.0.1:8000 (Windows)..." "STEP"
        echo -e "${BLUE}=================================${NC}"
        echo -e "${BOLD}Server Starting${NC}"
        echo -e "${BLUE}=================================${NC}"
        $PYTHON_CMD manage.py runserver 127.0.0.1:8000 | tee -a "$LOGFILE"
    else
        # On Unix/Linux, use 0.0.0.0 to allow external connections
        log "Starting Django development server on 0.0.0.0:8000..." "STEP"
        echo -e "${BLUE}=================================${NC}"
        echo -e "${BOLD}Server Starting${NC}"
        echo -e "${BLUE}=================================${NC}"
        $PYTHON_CMD manage.py runserver 0.0.0.0:8000 | tee -a "$LOGFILE"
    fi
    exit_on_error $? "runserver"
else
    log "Running in Jenkins environment. Skipping development server startup." "INFO"
    log "Deployment completed successfully!" "SUCCESS"
    echo -e "${BLUE}=================================${NC}"
fi
