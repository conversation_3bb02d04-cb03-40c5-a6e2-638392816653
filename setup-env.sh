#!/bin/bash

# Environment Setup Script (setup-env.sh)
# Detects Git branch and creates appropriate .env file automatically

set -e  # Exit on any error

echo "=========================================="
echo "Environment Setup Script"
echo "=========================================="

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Step 1: Detect Git branch
log "Step 1: Detecting Git branch..."

# Get current branch name - handle different environments
if [ -n "$TEST_BRANCH" ]; then
    # Test mode override
    CURRENT_BRANCH="$TEST_BRANCH"
    log "🧪 TEST MODE: Simulating branch: $CURRENT_BRANCH"
elif [ -n "$GIT_BRANCH" ]; then
    # Jenkins environment - use GIT_BRANCH environment variable
    CURRENT_BRANCH=$(echo "$GIT_BRANCH" | sed 's|origin/||' | sed 's|refs/heads/||')
    log "Jenkins environment detected - Branch from GIT_BRANCH: $CURRENT_BRANCH"
elif [ -n "$BRANCH_NAME" ]; then
    # Jenkins Pipeline environment - use BRANCH_NAME environment variable
    CURRENT_BRANCH="$BRANCH_NAME"
    log "Jenkins Pipeline environment detected - Branch from BRANCH_NAME: $CURRENT_BRANCH"
else
    # Local development environment
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    log "Local environment detected - Current Git branch: $CURRENT_BRANCH"
fi

# Step 2: Determine environment based on branch
log "Step 2: Determining environment based on branch..."

if [[ "$CURRENT_BRANCH" == feature/* ]] || [[ "$CURRENT_BRANCH" == fix/* ]]; then
    ENVIRONMENT="local"
    PORT="8000"
    DB_NAME="ticketing_tool"
    DB_USER="root"
    DB_PASSWORD="admin@123"
    DB_HOST="localhost"
    BACKEND_URL="http://localhost:8000/"
    DEBUG="True"
    log "✅ Detected: LOCAL deployment (feature/fix branch)"

elif [[ "$CURRENT_BRANCH" == "development" ]]; then
    ENVIRONMENT="staging"
    PORT="9019"
    DB_NAME="nex_ticket_stg_db"
    DB_USER="nex-ticketing-stg"
    DB_PASSWORD="RsRtW8u96@N"
    DB_HOST="***************"
    BACKEND_URL="http://internal-project.nexware-global.com:9019/"
    DEBUG="False"
    log "✅ Detected: STAGING deployment (development branch)"

elif [[ "$CURRENT_BRANCH" == "main" ]] || [[ "$CURRENT_BRANCH" == hotfix/* ]] || [[ "$CURRENT_BRANCH" == release/* ]]; then
    ENVIRONMENT="production"
    PORT="9049"
    DB_NAME="nex_ticket_db"
    DB_USER="nex-ticketing"
    DB_PASSWORD="nHH9Ky@RHgTDV"
    DB_HOST="***************"
    BACKEND_URL="https://ticket.nexware-global.com:9049/"
    DEBUG="False"
    log "✅ Detected: PRODUCTION deployment (main/hotfix/release branch)"

else
    log "⚠️  Unknown branch pattern: $CURRENT_BRANCH"
    log "⚠️  Defaulting to LOCAL deployment"
    ENVIRONMENT="local"
    PORT="8000"
    DB_NAME="ticketing_tool"
    DB_USER="root"
    DB_PASSWORD="admin@123"
    DB_HOST="localhost"
    BACKEND_URL="http://localhost:8000/"
    DEBUG="True"
fi

# Step 3: Set environment-specific configurations
log "Step 3: Setting environment-specific configurations..."

# Set CORS origins based on environment
if [[ "$ENVIRONMENT" == "local" ]]; then
    CORS_ORIGINS="http://localhost:3000,http://localhost:3001,http://localhost:4173,http://localhost:5173"
    ALLOWED_HOSTS="localhost,127.0.0.1"
    CSRF_ORIGINS="http://localhost:8000"
elif [[ "$ENVIRONMENT" == "staging" ]]; then
    CORS_ORIGINS="http://internal-project.nexware-global.com:9018,http://internal-project.nexware-global.com:9019,https://internal-project.nexware-global.com:9018,https://internal-project.nexware-global.com:9019,http://localhost:3000,http://localhost:4173,http://localhost:5173"
    ALLOWED_HOSTS="***************,internal-project.nexware-global.com,localhost,127.0.0.1"
    CSRF_ORIGINS="http://internal-project.nexware-global.com:9018,http://internal-project.nexware-global.com:9019,https://internal-project.nexware-global.com:8001"
else # production
    CORS_ORIGINS="https://ticket.nexware-global.com:9048,https://ticket.nexware-global.com:9049,http://internal-project.nexware-global.com:9018,http://internal-project.nexware-global.com:9019,https://internal-project.nexware-global.com:9018,https://internal-project.nexware-global.com:9019"
    ALLOWED_HOSTS="***************,internal-project.nexware-global.com,nex-ticket.nexware-global.com,ticket.nexware-global.com,localhost,127.0.0.1"
    CSRF_ORIGINS="https://ticket.nexware-global.com:9049,https://internal-project.nexware-global.com:8001"
fi

# Step 4: Create .env file automatically
ENV_FILE=".env"
log "Step 4: Creating .env file automatically..."

cat > $ENV_FILE << EOF
# Auto-generated Environment Configuration
# Generated on: $(date)
# Git Branch: $CURRENT_BRANCH
# Environment: $ENVIRONMENT

ENVIRONMENT=$ENVIRONMENT
DEBUG=$DEBUG

# Database Configuration
DATABASE_ENGINE=django.db.backends.mysql
DATABASE_NAME=$DB_NAME
DATABASE_USER=$DB_USER
DATABASE_PASSWORD=$DB_PASSWORD
DATABASE_HOST=$DB_HOST
DATABASE_PORT=3306
DATABASE_CHARSET=utf8mb4

# Django Secret Key
SECRET_KEY=django-insecure-_\$!bmy3u@vlgu@=11o9wv^l=2c@=l99@wy41nv8e1kx7670^xo

# Backend URL
BACKEND_URL=$BACKEND_URL

# CORS Settings
CORS_ALLOW_ALL_ORIGINS=False
CORS_ALLOW_CREDENTIALS=True

# Allowed Hosts
ALLOWED_HOSTS=$ALLOWED_HOSTS

# CORS Allowed Origins
CORS_ALLOWED_ORIGINS=$CORS_ORIGINS

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.zeptomail.in
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=PHtE6r0JELzu2Gcpo0JWtKewRcCiYIss/epmeAEU4dlHC/8ETk1Sqtkjlme1rRguB/ATF/WewIw9tr/OtL/WLDu8MWpOXGqyqK3sx/VYSPOZsbq6x00asV4dd0LVU4bqdtdr1CDTvdjZNA==

# Redis/Celery Configuration
CELERY_BROKER_URL=redis://internal-project.nexware-global.com:6379/0
CELERY_RESULT_BACKEND=redis://internal-project.nexware-global.com:6379/0
CELERY_ACCEPT_CONTENT=json
CELERY_TASK_SERIALIZER=json

# Static and Media Files
STATIC_URL=/static/
MEDIA_URL=/media/

# Channel Layers (WebSocket)
CHANNEL_LAYER_BACKEND=channels.layers.InMemoryChannelLayer

# Firebase Configuration
FIREBASE_CREDENTIALS_PATH=firebase-service-account.json

# CSRF Trusted Origins
CSRF_TRUSTED_ORIGINS=$CSRF_ORIGINS

# Server Configuration
SERVER_PORT=$PORT
SERVER_HOST=0.0.0.0
EOF

log "✅ Environment file created: $ENV_FILE"

# Step 5: Display configuration summary
echo ""
echo "=========================================="
echo "🎯 DEPLOYMENT CONFIGURATION SUMMARY"
echo "=========================================="
echo "Git Branch: $CURRENT_BRANCH"
echo "Environment: $ENVIRONMENT"
echo "Port: $PORT"
echo "Database: $DB_NAME@$DB_HOST"
echo "Backend URL: $BACKEND_URL"
echo "Debug Mode: $DEBUG"
echo "Environment File: $ENV_FILE"
echo ""
echo "📋 Branch Detection Rules:"
echo "• feature/* or fix/* → Local deployment (port 8000)"
echo "• development → Staging deployment (port 9019)"
echo "• main, hotfix/*, release/* → Production deployment (port 9049)"
echo ""
echo "✅ Environment file created successfully!"
echo "=========================================="

log "Environment setup completed! 🚀"
