"""
Custom WebSocket middleware for handling authentication
"""

from channels.middleware import BaseMiddleware
from channels.auth import AuthMiddlewareStack
from django.contrib.auth.models import AnonymousUser
from urllib.parse import parse_qs
import logging

logger = logging.getLogger(__name__)


class ForceLogoutMiddleware(BaseMiddleware):
    """
    Custom middleware for force logout WebSocket that doesn't require authentication
    """
    
    async def __call__(self, scope, receive, send):
        # For force logout WebSocket, we don't need authentication
        # Just set an anonymous user and continue
        if scope["type"] == "websocket" and scope["path"].startswith("/ws/force-logout/"):
            scope["user"] = AnonymousUser()
            logger.info(f"Force logout WebSocket connection - Path: {scope['path']}")
        
        return await super().__call__(scope, receive, send)


class CustomAuthMiddleware(BaseMiddleware):
    """
    Custom authentication middleware that handles different WebSocket routes differently
    """
    
    async def __call__(self, scope, receive, send):
        if scope["type"] == "websocket":
            path = scope.get("path", "")
            
            # For force logout WebSocket, don't require authentication
            if path.startswith("/ws/force-logout/"):
                scope["user"] = AnonymousUser()
                logger.info(f"Bypassing auth for force logout WebSocket: {path}")
                return await super().__call__(scope, receive, send)
            
            # For other WebSocket connections, use normal authentication
            else:
                # Apply normal authentication middleware
                auth_middleware = AuthMiddlewareStack(self.inner)
                return await auth_middleware(scope, receive, send)
        
        # For non-WebSocket connections, continue normally
        return await super().__call__(scope, receive, send)


def CustomWebSocketMiddlewareStack(inner):
    """
    Custom middleware stack for WebSocket connections
    """
    return CustomAuthMiddleware(inner)
