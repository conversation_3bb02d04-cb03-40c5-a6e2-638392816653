from datetime import datetime, <PERSON><PERSON><PERSON>
import logging
import time
from django.shortcuts import render
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes
# Create your views here.
from rest_framework import status, permissions, viewsets
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth.models import User
from rest_framework.decorators import action
from django.contrib.auth import authenticate
from django.utils.timezone import now
from django.core.mail import send_mail
from rest_framework.decorators import api_view, permission_classes
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.tokens import default_token_generator
from rest_framework.authtoken.models import Token
from django.shortcuts import get_object_or_404
from django.conf import settings
from ..user_management.serializers import UserSerializer
from ..user_management.models import User
from rest_framework_simplejwt.tokens import AccessToken
from django.contrib.auth.hashers import make_password
# from .firebase_utils import send_fcm_notification  # Import the function
from apps.authentication.models import Module, PasswordResetToken, RoleModuleAccess, Role, SubModule, SubModuleAccess
from apps.authentication.serializers import ModuleSerializer, RoleModuleAccessSerializer, RoleModuleAccessDetailSerializer, SubModuleAccessSerializer, SubModuleSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication
from django.contrib.auth.hashers import check_password,make_password
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings
from rest_framework.views import APIView
from itsdangerous import URLSafeTimedSerializer



def generate_approval_token(ticket_id, user_email):
    serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
    return serializer.dumps({"ticket_id": ticket_id, "email": user_email}, salt=settings.APPROVAL_TOKEN_SALT)


def build_approval_link(ticket_id, user_email):
    token = generate_approval_token(ticket_id, user_email)
    return f"{settings.BACKEND_URL}/api/ticket-approvalstatus/?token={token}&is_approved=true"


class SaveFCMTokenView(APIView):
    # permission_classes = [IsAuthenticated]

    def post(self, request):
        fcm_token = request.data.get("fcm_token")
        
        if not fcm_token:
            return Response({"detail": "FCM token is required."}, status=status.HTTP_400_BAD_REQUEST)

        request.user.fcm_token = fcm_token
        request.user.save()

        return Response({"detail": "FCM token saved successfully."}, status=status.HTTP_200_OK)


class LoginAPIView(APIView):
    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')
        fcm_token = request.data.get('fcm_token')

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'detail': 'Email Not Exists'}, status=status.HTTP_404_NOT_FOUND)
        
        if not user.is_active:
            return Response({'detail': 'User account is not active'}, status=status.HTTP_401_UNAUTHORIZED)

        if not check_password(password, user.password):
            return Response({'detail': 'Password is Incorrect'}, status=status.HTTP_401_UNAUTHORIZED)

        refresh = RefreshToken.for_user(user)
        serializer = UserSerializer(user)
        user_data = serializer.data

        if user.profile_pic:
            user_data['profile_pic'] = request.build_absolute_uri(user.profile_pic.url)

        if fcm_token:
            user.fcm_token = fcm_token
            user.save()

        response_data = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': user_data,
            'message': "Login Successful"
        }

        # Check if the user is new_user
        if user.new_user:
            # Create a reset password token
            access_token = refresh.access_token
            access_token.set_exp(lifetime=timedelta(minutes=30))
            token_str = str(access_token)

            expiration_time = timezone.now() + timedelta(minutes=30)

            # Save token into PasswordResetToken table
            PasswordResetToken.objects.create(
                user=user,
                token=token_str,
                created_at=timezone.now(),
                used=False,
                lifetime=expiration_time
            )

            frontend_url = settings.BACKEND_URL  # example: "http://localhost:3000/"
            reset_url = f"{frontend_url}resetpassword/{user.id}/{token_str}/?exp={int(expiration_time.timestamp())}"

            response_data['reset_password_required'] = True
            response_data['reset_url'] = reset_url
            response_data['reset_token'] = token_str

        return Response(response_data, status=status.HTTP_200_OK)



class PasswordResetRequestAPIView(APIView):
    def post(self, request):
        email = request.data.get('email')
        user = get_object_or_404(User, email=email)

        token = default_token_generator.make_token(user)

        # Save token to the PasswordResetToken table with lifetime = now + 30 minutes
        lifetime = timezone.now() + timedelta(minutes=30)
        password_reset_token = PasswordResetToken.objects.create(
            user=user,
            token=token,
            created_at=timezone.now(),
            used=False,
            lifetime=lifetime
        )

        # Save token in cache with expiry (optional)
        cache.set(f"reset_token_{user.pk}", token, timeout=1800)  # 30 mins

        reset_url = f"http://localhost:3000/reset-password/{user.pk}/{token}"

       

        send_mail(
            'Password Reset Request',
            f'Use this link to reset your password (valid for 30 mins): {reset_url}',
            settings.EMAIL_HOST_USER,
            [email],
        )

        return Response({'detail': 'Password reset email sent'}, status=status.HTTP_200_OK)

class PasswordResetConfirmAPIView(APIView):
    def post(self, request, user_id, token):
        user = get_object_or_404(User, pk=user_id)
        try:
            reset_token = PasswordResetToken.objects.get(user=user, token=token, used=False)
        except PasswordResetToken.DoesNotExist:
            return Response({'error': 'Invalid or already used token'}, status=status.HTTP_400_BAD_REQUEST)

        # Check if token has expired
        if timezone.now() > reset_token.lifetime:
            return Response({'expired': True, 'error': "This link has expired."}, status=status.HTTP_400_BAD_REQUEST)

        if not default_token_generator.check_token(user, token):
            return Response({'error': 'Invalid or expired token'}, status=status.HTTP_400_BAD_REQUEST)

        password = request.data.get('password')
        if not password:
            return Response({'error': 'Password is required'}, status=status.HTTP_400_BAD_REQUEST)

        # Update the user's password
        user.set_password(password)
        user.save()

        # Mark the token as used
        reset_token.used = True
        reset_token.save()

        return Response({'detail': 'Password reset successful'}, status=status.HTTP_200_OK)

@api_view(['POST'])
# @permission_classes([])  
def create_module(request):
    """
    Create a new module using the provided data.
    """
    try:
        module_data = request.data

        module_name = module_data.get('module_name')
        if Module.objects.filter(module_name=module_name).exists():
            return Response({'error': 'Module already exists'}, status=status.HTTP_400_BAD_REQUEST)
        

        serializer = ModuleSerializer(data=module_data)
        if serializer.is_valid():
            module = serializer.save()

            roles = Role.objects.all()
            for role in roles:
                has_access = True if role.role_name == "Super Admin" else False
                RoleModuleAccess.objects.create(
                    role=role,
                    module=module,
                    has_access=has_access
                )
            return Response({"message": "Module created successfully!", "data":serializer.data}, status=status.HTTP_201_CREATED)
        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    
# Get all Modules (GET method)
@api_view(['GET'])
# @permission_classes([])  
def get_modules(request):
    try:
        modules = Module.objects.all()
        serializer = ModuleSerializer(modules, many=True)
        return Response({"message": "Modules fetched successfully","data":serializer.data}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error":serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
    
# Get a single Module by ID (GET method)
@api_view(['GET'])
def get_module(request, pk=None):
    try:
        module = Module.objects.get(pk=pk)
    except Module.DoesNotExist:
        return Response({"error": "Module not found"}, status=status.HTTP_404_NOT_FOUND)
        
    serializer = ModuleSerializer(module)
    return Response(serializer.data, status=status.HTTP_200_OK)
    
# Update Module (PUT method)
@api_view(['PUT'])
def update_module(request, id):
    """
    Update an existing module with the given ID.
    """
    try:
        # Retrieve the module instance
        module = Module.objects.get(id=id)

        # Pass the instance and updated data to the serializer
        serializer = ModuleSerializer(module, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "Module updated successfully!", "data": serializer.data},
                status=status.HTTP_200_OK
            )
        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

    except Module.DoesNotExist:
        return Response({"error": "Module not found"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def get_role_module_and_submodule_access(request, role_id):
    """
    Fetch both module and submodule access details for a given role.
    """
    try:
        # Fetch role information first
        role = Role.objects.get(role_id=role_id)
        
        # Fetch module access for the given role
        role_module_access = RoleModuleAccess.objects.filter(
            role__role_id=role_id,
            is_deleted=False,
            # has_access=True
        ).select_related('role', 'module')
        
        # Fetch submodule access for the given role
        role_submodule_access = SubModuleAccess.objects.filter(
            role__role_id=role_id,
            is_deleted=False,
            # has_access=True
        ).select_related('role', 'submodule', 'submodule__module')
        
        # Create a mapping of module_id to submodules
        submodule_mapping = {}
        for access in role_submodule_access:
            module_id = access.submodule.module.id
            if module_id not in submodule_mapping:
                submodule_mapping[module_id] = []
            
            submodule_mapping[module_id].append({
                "submodule_id": access.submodule.id,
                "submodule_name": access.submodule.sub_module_name,
                "has_access": access.has_access
            })
        
        # Build the modules list with their submodules
        modules = []
        for access in role_module_access:
            module_entry = {
                "module_id": access.module.id,
                "module_name": access.module.module_name,
                "has_access": access.has_access
            }
            
            # Add submodules if they exist for this module
            if access.module.id in submodule_mapping:
                module_entry["sub_module"] = submodule_mapping[access.module.id]
            
            modules.append(module_entry)
        
        # Create the final response structure
        response_data = [{
            "role_name": role.role_name,
            "modules": modules
        }]
        
        return Response(
            {
                "message": "Role Module Access fetched successfully",
                "data": response_data
            },
            status=status.HTTP_200_OK
        )
        
    except Role.DoesNotExist:
        return Response(
            {"error": "Role not found"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"error": str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
   
    
@api_view(['PUT'])
def update_role_module_and_submodule_access(request, role_id):
    """
    Update modules and submodules access for a given role ID.
    Only update records that have changed.
    """
    try:
        # Extract the modulesWithAccess and subModulesWithAccess lists from the request data
        modules_with_access = request.data.get("modulesWithAccess", [])
        submodules_with_access = request.data.get("subModulesWithAccess", [])
        updated_by = request.data.get("updatedBy")

        # Validate request data
        if not isinstance(modules_with_access, list) or not isinstance(submodules_with_access, list):
            return Response(
                {"error": "'modulesWithAccess' and 'subModulesWithAccess' should be lists of IDs."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Convert lists to sets
        provided_module_ids = set(map(int, modules_with_access))
        provided_submodule_ids = set(map(int, submodules_with_access))

        # Fetch all module access records for the given role_id
        existing_module_records = RoleModuleAccess.objects.filter(role_id=role_id)
        current_time = now()

        # Update only changed module records
        for record in existing_module_records:
            new_access = record.module_id in provided_module_ids
            if record.has_access != new_access:  # Only update if there is a change
                record.has_access = new_access
                record.updated_at = current_time
                record.updated_by_id = updated_by
                record.save()
            provided_module_ids.discard(record.module_id)  # Remove processed module

        # Fetch all submodule access records for the given role_id
        existing_submodule_records = SubModuleAccess.objects.filter(role_id=role_id)

        # Update only changed submodule records
        for record in existing_submodule_records:
            new_access = record.submodule_id in provided_submodule_ids
            if record.has_access != new_access:  # Only update if there is a change
                record.has_access = new_access
                record.updated_at = current_time
                record.updated_by_id = updated_by
                record.save()
            provided_submodule_ids.discard(record.submodule_id)  # Remove processed submodule

        # Return success response
        return Response(
            {"message": "Modules and SubModules access updated successfully!"},
            status=status.HTTP_200_OK
        )

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
def create_submodule(request):
    """
    Create a new submodule and assign default access.
    """
    try:
        submodule_data = request.data.copy()  # Copy request data to avoid modifying the original
        # Ensure 'module_id' is present in the request
        module_id = submodule_data.get("module_id")
        if not module_id:
            return Response({"error": {"module": ["This field is required."]}}, status=status.HTTP_400_BAD_REQUEST)
        # Get module instance and assign to 'module' field
        module_instance = get_object_or_404(Module, id=module_id)
        submodule_data["module"] = module_instance.id  # Convert ForeignKey to ID
        # Check for duplicate submodule under the same module
        sub_module_name = submodule_data.get('sub_module_name')
        if SubModule.objects.filter(sub_module_name=sub_module_name, module=module_instance).exists():
            return Response({'error': 'SubModule already exists'}, status=status.HTTP_400_BAD_REQUEST)
        # Pass modified data to serializer
        serializer = SubModuleSerializer(data=submodule_data)
        if serializer.is_valid():
            submodule = serializer.save()
            # Assign default access to roles (Super Admin gets access by default)
            roles = Role.objects.all()
            for role in roles:
                has_access = role.role_name == "Super Admin"
                SubModuleAccess.objects.create(role=role, submodule=submodule, has_access=has_access)
            return Response({"message": "SubModule created successfully!", "data": serializer.data}, status=status.HTTP_201_CREATED)
        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)   
@api_view(['GET'])
def get_all_submodules(request):
    """
    Retrieve all submodules.
    """
    try:
        submodules = SubModule.objects.all()
        serializer = SubModuleSerializer(submodules, many=True)
        return Response({"message": "SubModules fetched successfully!", "data": serializer.data}, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
# Forgot Password Request
class ForgotPasswordAPIView(APIView):
    def post(self, request):
        """
        Request password reset link by sending an email with JWT token and storing it in the database.
        """
        email = request.data.get('email')
        if not email:
            return Response({'detail': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'detail': 'Email not found'}, status=status.HTTP_404_NOT_FOUND)

        # Create JWT token
        refresh = RefreshToken.for_user(user)
        access_token = refresh.access_token
        access_token.set_exp(lifetime=timedelta(minutes=30))
        token_str = str(access_token)

        # Token expiration
        expiration_time = timezone.now() + timedelta(minutes=30)

        # Store token in DB
        PasswordResetToken.objects.create(
            user=user,
            token=token_str,
            created_at=timezone.now(),
            used=False,
            lifetime=expiration_time
        )

        # Create reset link
        frontend_url = settings.BACKEND_URL  # e.g. "http://localhost:3000"
        reset_url = f"{frontend_url}resetpassword/{user.id}/{token_str}/?exp={int(expiration_time.timestamp())}"

            # HTML email content with button
        html_message = f'''
        <p>Click the reset link below to reset your password:</p>
        <a href="{reset_url}" >
        Reset Password
        </a>
        <p>This link is valid for 30 minutes.</p>
        '''

        # Send email
        send_mail(
            subject='Password Reset Request',
            message='Use the link to reset your password.',  # fallback plain text
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[email],
            fail_silently=False,
            html_message=html_message
        )
        return Response({'message': 'Password reset link has been sent to your email'}, status=status.HTTP_200_OK)


class ResetPasswordAPIView(APIView):
    """
    API to reset password using a JWT token.
    """

    def post(self, request):
        token = request.data.get("token")
        user_id = request.data.get("user_id")
        new_password = request.data.get("password")
        new_user = request.data.get("new_user")

        if not new_password:
            return Response({"error": "Password is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Decode token to get user_id if token is provided
            if token:
                access_token = AccessToken(token)
                user_id = access_token["user_id"]

            if not user_id:
                return Response({"error": "Token or user_id is required"}, status=status.HTTP_400_BAD_REQUEST)

            user = User.objects.get(id=user_id)

            # Get the token from DB
            reset_token_entry = PasswordResetToken.objects.filter(user=user, token=token).first()

            if not reset_token_entry:
                return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)

            # Check if token was already used
            if reset_token_entry.used:
                return Response({"error": "This reset link has already been used."}, status=status.HTTP_400_BAD_REQUEST)

            # Check if token has expired
            if reset_token_entry.lifetime < now():
                return Response({"error": "Token has expired"}, status=status.HTTP_400_BAD_REQUEST)

            # Update user password
            user.password = make_password(new_password)
            user.new_user = new_user
            user.save()

            # Mark token as used
            reset_token_entry.used = True
            reset_token_entry.save()

            return Response({"message": "Password updated successfully!"}, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)


class ValidateResetTokenAPIView(APIView):
    def post(self, request):
        token = request.data.get("token")
        user_id = request.data.get("user_id")

        if not token or not user_id:
            return Response({"error": "Missing token or user_id"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=user_id)
            reset_token_entry = PasswordResetToken.objects.filter(user=user, token=token).first()

            if not reset_token_entry:
                return Response({"error": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST)

            if reset_token_entry.used:
                return Response({"error": "Token already used"}, status=status.HTTP_400_BAD_REQUEST)

            if reset_token_entry.lifetime < now():
                return Response({"error": "Token expired"}, status=status.HTTP_400_BAD_REQUEST)

            return Response({"valid": True}, status=status.HTTP_200_OK)

        except User.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)




class ChangePasswordAPIView(APIView):

    permission_classes = [IsAuthenticated]

    def post(self, request):
        current_password = request.data.get("current_password")
        new_password = request.data.get("new_password")
        confirm_password = request.data.get("confirm_password")

        # Check all fields are provided
        if not current_password or not new_password or not confirm_password:
            return Response({"error": "All fields are required."}, status=status.HTTP_400_BAD_REQUEST)

        user = request.user

        # Verify current password
        if not check_password(current_password, user.password):
            return Response({"error": "Current password is incorrect."}, status=status.HTTP_400_BAD_REQUEST)

        # Check new_password and confirm_password match
        if new_password != confirm_password:
            return Response({"error": "New password and confirm password do not match."}, status=status.HTTP_400_BAD_REQUEST)

        # Optional: Check if new password is same as current password
        if check_password(new_password, user.password):
            return Response({"error": "New password cannot be the same as the current password."}, status=status.HTTP_400_BAD_REQUEST)

        # Optional: Check password strength
        if len(new_password) < 8:
            return Response({"error": "New password must be at least 8 characters long."}, status=status.HTTP_400_BAD_REQUEST)

        # Update password
        user.password = make_password(new_password)
        user.save()

        return Response({"message": "Password changed successfully!"}, status=status.HTTP_200_OK)
