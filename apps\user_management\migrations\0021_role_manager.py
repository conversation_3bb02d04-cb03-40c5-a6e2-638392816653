# Generated by Django 5.1.6 on 2025-07-07 10:55

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0020_project_deleted_at_project_deleted_by_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='role',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_roles', to=settings.AUTH_USER_MODEL),
        ),
    ]
