from rest_framework.response import Response
from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from .models import User

class UserManagement(BasePermission):
    def has_permission(self, request, view):
        try:
            user = User.objects.get(email=request.user.email)
            print("uuu",user)
        except User.DoesNotExist:
            raise PermissionDenied({
                'status': 0,
                'message': 'Authentication failed'
            })

        return True
