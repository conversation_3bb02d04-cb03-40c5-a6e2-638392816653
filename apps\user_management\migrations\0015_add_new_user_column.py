from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0014_alter_user_date_joined_alter_user_is_staff_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            sql="""
            -- Check if new_user column exists, add if it doesn't
            SET @column_exists = (
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = 'user_management_user'
                AND column_name = 'new_user'
                AND table_schema = DATABASE()
            );

            SET @sql = IF(@column_exists = 0,
                'ALTER TABLE user_management_user ADD COLUMN new_user BOOLEAN DEFAULT TRUE',
                'SELECT "Column new_user already exists"');

            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            """,
            reverse_sql="""
            -- No reverse operation needed as we're only adding missing columns
            """
        ),
    ]
