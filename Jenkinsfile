pipeline {
    agent any

    environment {
        // Environment variables
        PYTHON_VERSION = '3.13'
        VENV_NAME = 'venv'
        PROJECT_NAME = 'ticketing-system-tool-backend'

        // Deployment paths
        PROD_DEPLOY_PATH = '/var/www/ticketing-system/production'
        STAGING_DEPLOY_PATH = '/var/www/ticketing-system/staging'
        DEV_DEPLOY_PATH = '/var/www/ticketing-system/development'
    }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }

        stage('Setup Environment') {
            steps {
                script {
                    // Create virtual environment if it doesn't exist
                    sh """
                        if [ ! -d "${VENV_NAME}" ]; then
                            python${PYTHON_VERSION} -m venv ${VENV_NAME}
                        fi
                        source ${VENV_NAME}/bin/activate
                        pip install --upgrade pip
                        pip install -r requirements.txt
                    """

                    // Generate environment configuration based on branch
                    if (env.BRANCH_NAME == 'main') {
                        sh 'chmod +x generate-env.sh'
                        sh './generate-env.sh production'
                    } else if (env.BRANCH_NAME == 'development') {
                        sh 'chmod +x generate-env.sh'
                        sh './generate-env.sh staging'
                    } else {
                        sh 'chmod +x generate-env.sh'
                        sh './generate-env.sh development'
                    }
                }
            }
        }

        stage('Run Tests') {
            steps {
                sh """
                    source ${VENV_NAME}/bin/activate
                    python manage.py test
                """
            }
        }

        stage('Collect Static Files') {
            steps {
                sh """
                    source ${VENV_NAME}/bin/activate
                    python manage.py collectstatic --noinput
                """
            }
        }

        stage('Run Migrations') {
            steps {
                script {
                    if (env.BRANCH_NAME == 'main') {
                        // Production migrations - extra caution
                        sh """
                            source ${VENV_NAME}/bin/activate

                            # Create backup directory
                            mkdir -p db_backups

                            # Backup production database before migrations
                            echo "Creating database backup before migrations..."
                            TIMESTAMP=\$(date +"%Y%m%d%H%M%S")
                            BACKUP_FILE="db_backups/nex_ticket_db_production_\${TIMESTAMP}.sql"
                            mysqldump -h *************** -P 3306 -u nex-ticketing -pnHH9Ky@RHgTDV nex_ticket_db > \${BACKUP_FILE} 2>/dev/null || echo "Backup failed, but continuing with migrations"

                            # Check migrations before applying
                            echo "Checking migrations..."
                            python manage.py showmigrations

                            # Check if migrations will work without applying them
                            echo "Checking if migrations will apply cleanly..."
                            python manage.py migrate --plan

                            # Apply migrations
                            echo "Applying migrations..."
                            python manage.py migrate
                        """
                    } else if (env.BRANCH_NAME == 'development') {
                        // Staging migrations
                        sh """
                            source ${VENV_NAME}/bin/activate

                            # Create backup directory
                            mkdir -p db_backups

                            # Backup staging database before migrations
                            echo "Creating database backup before migrations..."
                            TIMESTAMP=\$(date +"%Y%m%d%H%M%S")
                            BACKUP_FILE="db_backups/nex_ticket_stg_db_staging_\${TIMESTAMP}.sql"
                            mysqldump -h *************** -P 3306 -u nex-ticketing-stg -pRsRtW8u96@N nex_ticket_stg_db > \${BACKUP_FILE} 2>/dev/null || echo "Backup failed, but continuing with migrations"

                            # Check migrations before applying
                            echo "Checking migrations..."
                            python manage.py showmigrations

                            # Apply migrations
                            echo "Applying migrations..."
                            python manage.py migrate
                        """
                    } else {
                        // Development migrations
                        sh """
                            source ${VENV_NAME}/bin/activate

                            # Check migrations before applying
                            echo "Checking migrations..."
                            python manage.py showmigrations

                            # Apply migrations
                            echo "Applying migrations..."
                            python manage.py migrate
                        """
                    }
                }
            }
        }

        stage('Deploy') {
            steps {
                script {
                    if (env.BRANCH_NAME == 'main') {
                        // Deploy to production
                        sh """
                            # Create deployment directory if it doesn't exist
                            mkdir -p ${PROD_DEPLOY_PATH}

                            # Create backup of current deployment
                            TIMESTAMP=\$(date +"%Y%m%d%H%M%S")
                            if [ -d "${PROD_DEPLOY_PATH}" ] && [ "\$(ls -A ${PROD_DEPLOY_PATH})" ]; then
                                BACKUP_DIR="${PROD_DEPLOY_PATH}_backup_\${TIMESTAMP}"
                                cp -r ${PROD_DEPLOY_PATH} \${BACKUP_DIR}
                            fi

                            # Copy files to deployment directory
                            rsync -av --exclude 'venv' --exclude '.git' --exclude '__pycache__' --exclude '*.pyc' --exclude 'node_modules' . ${PROD_DEPLOY_PATH}

                            # Setup media directories and permissions using the dedicated script
                            chmod +x ${PROD_DEPLOY_PATH}/setup_media.sh
                            cd ${PROD_DEPLOY_PATH}
                            ./setup_media.sh production www-data

                            # Update README.md with deployment information
                            README_PATH="README.md"
                            TEMP_README="/tmp/readme_temp.md"

                            # Create a temporary file with the header if README doesn't exist
                            if [ ! -f "$README_PATH" ]; then
                                echo "# Ticketing System Backend" > "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "This is the backend for the Ticketing System application." >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "## Environment Setup" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "Environment: production" >> "$TEMP_README"
                                echo "Setup Date: $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
                                echo "Setup By: Jenkins CI/CD" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "## Deployment History" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                            else
                                # Extract existing content up to the Deployment History section
                                awk '/^## Deployment History/{exit} {print}' "$README_PATH" > "$TEMP_README"
                                echo "## Deployment History" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                            fi

                            # Add new deployment entry
                            echo "### Deployment - $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
                            echo "- Environment: production" >> "$TEMP_README"
                            echo "- Branch: main" >> "$TEMP_README"
                            echo "- Commit: $(git rev-parse HEAD)" >> "$TEMP_README"
                            echo "- Deployed by: Jenkins CI/CD" >> "$TEMP_README"
                            echo "- Build Number: ${BUILD_NUMBER}" >> "$TEMP_README"
                            echo "" >> "$TEMP_README"

                            # Append existing deployment history if it exists
                            if grep -q "^### Deployment" "$README_PATH"; then
                                awk '/^## Deployment History/{flag=1; next} /^##/{flag=0} flag && /^### Deployment/' "$README_PATH" >> "$TEMP_README"
                            fi

                            # Replace the original README with the new one
                            mv "$TEMP_README" "$README_PATH"

                            # Set proper permissions for the rest of the application
                            chmod -R 755 ${PROD_DEPLOY_PATH}
                            find ${PROD_DEPLOY_PATH} -type d -exec chmod 755 {} \\;
                            find ${PROD_DEPLOY_PATH} -type f -exec chmod 644 {} \\;

                            # Make scripts executable
                            chmod +x ${PROD_DEPLOY_PATH}/*.sh
                            chmod +x ${PROD_DEPLOY_PATH}/manage.py

                            # Restart services
                            sudo systemctl restart ticketing-system-prod.service
                            sudo systemctl restart daphne-prod.service
                            sudo systemctl restart celery-prod.service
                        """
                    } else if (env.BRANCH_NAME == 'development') {
                        // Deploy to staging
                        sh """
                            # Create deployment directory if it doesn't exist
                            mkdir -p ${STAGING_DEPLOY_PATH}

                            # Create backup of current deployment
                            TIMESTAMP=\$(date +"%Y%m%d%H%M%S")
                            if [ -d "${STAGING_DEPLOY_PATH}" ] && [ "\$(ls -A ${STAGING_DEPLOY_PATH})" ]; then
                                BACKUP_DIR="${STAGING_DEPLOY_PATH}_backup_\${TIMESTAMP}"
                                cp -r ${STAGING_DEPLOY_PATH} \${BACKUP_DIR}
                            fi

                            # Copy files to deployment directory
                            rsync -av --exclude 'venv' --exclude '.git' --exclude '__pycache__' --exclude '*.pyc' --exclude 'node_modules' . ${STAGING_DEPLOY_PATH}

                            # Setup media directories and permissions using the dedicated script
                            chmod +x ${STAGING_DEPLOY_PATH}/setup_media.sh
                            cd ${STAGING_DEPLOY_PATH}
                            ./setup_media.sh staging www-data

                            # Update README.md with deployment information
                            README_PATH="README.md"
                            TEMP_README="/tmp/readme_temp.md"

                            # Create a temporary file with the header if README doesn't exist
                            if [ ! -f "$README_PATH" ]; then
                                echo "# Ticketing System Backend" > "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "This is the backend for the Ticketing System application." >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "## Environment Setup" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "Environment: staging" >> "$TEMP_README"
                                echo "Setup Date: $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
                                echo "Setup By: Jenkins CI/CD" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "## Deployment History" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                            else
                                # Extract existing content up to the Deployment History section
                                awk '/^## Deployment History/{exit} {print}' "$README_PATH" > "$TEMP_README"
                                echo "## Deployment History" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                            fi

                            # Add new deployment entry
                            echo "### Deployment - $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
                            echo "- Environment: staging" >> "$TEMP_README"
                            echo "- Branch: development" >> "$TEMP_README"
                            echo "- Commit: $(git rev-parse HEAD)" >> "$TEMP_README"
                            echo "- Deployed by: Jenkins CI/CD" >> "$TEMP_README"
                            echo "- Build Number: ${BUILD_NUMBER}" >> "$TEMP_README"
                            echo "" >> "$TEMP_README"

                            # Append existing deployment history if it exists
                            if grep -q "^### Deployment" "$README_PATH"; then
                                awk '/^## Deployment History/{flag=1; next} /^##/{flag=0} flag && /^### Deployment/' "$README_PATH" >> "$TEMP_README"
                            fi

                            # Replace the original README with the new one
                            mv "$TEMP_README" "$README_PATH"

                            # Set proper permissions for the rest of the application
                            chmod -R 755 ${STAGING_DEPLOY_PATH}
                            find ${STAGING_DEPLOY_PATH} -type d -exec chmod 755 {} \\;
                            find ${STAGING_DEPLOY_PATH} -type f -exec chmod 644 {} \\;

                            # Make scripts executable
                            chmod +x ${STAGING_DEPLOY_PATH}/*.sh
                            chmod +x ${STAGING_DEPLOY_PATH}/manage.py

                            # Restart services
                            sudo systemctl restart ticketing-system-staging.service
                            sudo systemctl restart daphne-staging.service
                            sudo systemctl restart celery-staging.service
                        """
                    } else {
                        // Deploy to development
                        sh """
                            # Create deployment directory if it doesn't exist
                            mkdir -p ${DEV_DEPLOY_PATH}

                            # Create backup of current deployment
                            TIMESTAMP=\$(date +"%Y%m%d%H%M%S")
                            if [ -d "${DEV_DEPLOY_PATH}" ] && [ "\$(ls -A ${DEV_DEPLOY_PATH})" ]; then
                                BACKUP_DIR="${DEV_DEPLOY_PATH}_backup_\${TIMESTAMP}"
                                cp -r ${DEV_DEPLOY_PATH} \${BACKUP_DIR}
                            fi

                            # Copy files to deployment directory
                            rsync -av --exclude 'venv' --exclude '.git' --exclude '__pycache__' --exclude '*.pyc' --exclude 'node_modules' . ${DEV_DEPLOY_PATH}

                            # Setup media directories and permissions using the dedicated script
                            chmod +x ${DEV_DEPLOY_PATH}/setup_media.sh
                            cd ${DEV_DEPLOY_PATH}
                            ./setup_media.sh development www-data

                            # Update README.md with deployment information
                            README_PATH="README.md"
                            TEMP_README="/tmp/readme_temp.md"

                            # Create a temporary file with the header if README doesn't exist
                            if [ ! -f "$README_PATH" ]; then
                                echo "# Ticketing System Backend" > "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "This is the backend for the Ticketing System application." >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "## Environment Setup" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "Environment: development" >> "$TEMP_README"
                                echo "Setup Date: $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
                                echo "Setup By: Jenkins CI/CD" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                                echo "## Deployment History" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                            else
                                # Extract existing content up to the Deployment History section
                                awk '/^## Deployment History/{exit} {print}' "$README_PATH" > "$TEMP_README"
                                echo "## Deployment History" >> "$TEMP_README"
                                echo "" >> "$TEMP_README"
                            fi

                            # Add new deployment entry
                            echo "### Deployment - $(date '+%Y-%m-%d %H:%M:%S')" >> "$TEMP_README"
                            echo "- Environment: development" >> "$TEMP_README"
                            echo "- Branch: $(git rev-parse --abbrev-ref HEAD)" >> "$TEMP_README"
                            echo "- Commit: $(git rev-parse HEAD)" >> "$TEMP_README"
                            echo "- Deployed by: Jenkins CI/CD" >> "$TEMP_README"
                            echo "- Build Number: ${BUILD_NUMBER}" >> "$TEMP_README"
                            echo "" >> "$TEMP_README"

                            # Append existing deployment history if it exists
                            if grep -q "^### Deployment" "$README_PATH"; then
                                awk '/^## Deployment History/{flag=1; next} /^##/{flag=0} flag && /^### Deployment/' "$README_PATH" >> "$TEMP_README"
                            fi

                            # Replace the original README with the new one
                            mv "$TEMP_README" "$README_PATH"

                            # Set proper permissions for the rest of the application
                            chmod -R 755 ${DEV_DEPLOY_PATH}
                            find ${DEV_DEPLOY_PATH} -type d -exec chmod 755 {} \\;
                            find ${DEV_DEPLOY_PATH} -type f -exec chmod 644 {} \\;

                            # Make scripts executable
                            chmod +x ${DEV_DEPLOY_PATH}/*.sh
                            chmod +x ${DEV_DEPLOY_PATH}/manage.py

                            # Restart services
                            sudo systemctl restart ticketing-system-dev.service
                            sudo systemctl restart daphne-dev.service
                            sudo systemctl restart celery-dev.service
                        """
                    }
                }
            }
        }
    }

    post {
        success {
            echo 'Deployment completed successfully!'
        }
        failure {
            echo 'Deployment failed!'
        }
        always {
            echo 'Cleaning up workspace...'
            cleanWs()
        }
    }
}
