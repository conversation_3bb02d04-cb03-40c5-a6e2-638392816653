pipeline {
    agent any

    environment {
        // Environment variables
        PYTHON_VERSION = '3.10'
        VENV_NAME = 'venv'
        DEPLOY_DIR = '/var/www/ticketing-system'
        BACKUP_DIR = '/var/backups/ticketing-system'
        NOTIFICATION_CHANNEL = 'ticketing-system-deployments'

        // Determine environment based on branch
        DEPLOY_ENV = "${env.BRANCH_NAME == 'main' ? 'production' : env.BRANCH_NAME == 'development' ? 'staging' : 'development'}"
    }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
                script {
                    // Set environment-specific variables
                    if (env.DEPLOY_ENV == 'production') {
                        env.APP_URL = 'https://ticket.nexware-global.com:9048/'
                        env.DB_HOST = 'prod-db-host'
                        env.DEBUG = 'False'
                    } else if (env.DEPLOY_ENV == 'staging') {
                        env.APP_URL = 'http://internal-project.nexware-global.com:9018/'
                        env.DB_HOST = 'staging-db-host'
                        env.DEBUG = 'True'
                    } else {
                        env.APP_URL = 'http://localhost:3000/'
                        env.DB_HOST = 'localhost'
                        env.DEBUG = 'True'
                    }

                    // Log deployment information
                    echo "Deploying to ${env.DEPLOY_ENV} environment"
                    echo "Application URL: ${env.APP_URL}"
                }
            }
        }

        stage('Setup Environment') {
            steps {
                sh '''
                    # Determine the correct Python and pip commands
                    if command -v python &> /dev/null; then
                        PYTHON_CMD="python"
                    elif command -v python3 &> /dev/null; then
                        PYTHON_CMD="python3"
                    else
                        echo "ERROR: Neither 'python' nor 'python3' commands are available. Please install Python."
                        exit 1
                    fi

                    if command -v pip &> /dev/null; then
                        PIP_CMD="pip"
                    elif command -v pip3 &> /dev/null; then
                        PIP_CMD="pip3"
                    else
                        echo "ERROR: Neither 'pip' nor 'pip3' commands are available. Please install pip."
                        exit 1
                    fi

                    # Make manage.py executable
                    chmod +x manage.py

                    # Install MySQL client libraries based on OS
                    if [ "$(uname)" == "Darwin" ]; then
                        # macOS
                        brew install mysql-client
                        export LDFLAGS="-L/opt/homebrew/opt/mysql-client/lib"
                        export CPPFLAGS="-I/opt/homebrew/opt/mysql-client/include"
                    elif [ "$(uname)" == "Linux" ]; then
                        # Linux (Ubuntu/Debian)
                        if command -v apt-get &> /dev/null; then
                            sudo apt-get update
                            sudo apt-get install -y default-libmysqlclient-dev
                        # RHEL/CentOS
                        elif command -v yum &> /dev/null; then
                            sudo yum install -y mysql-devel
                        fi
                    fi

                    # Create virtual environment if it doesn't exist
                    if [ ! -d "${VENV_NAME}" ]; then
                        $PYTHON_CMD -m venv ${VENV_NAME} || $PYTHON_CMD -m virtualenv ${VENV_NAME}
                    fi

                    # Activate virtual environment and install dependencies
                    . ${VENV_NAME}/bin/activate
                    $PIP_CMD install --upgrade pip

                    # PyMySQL is already in requirements.txt and will be installed

                    # Also try to install mysqlclient as a fallback if needed
                    if [ "$(uname)" == "Darwin" ]; then
                        LDFLAGS="-L/opt/homebrew/opt/mysql-client/lib" CPPFLAGS="-I/opt/homebrew/opt/mysql-client/include" $PIP_CMD install mysqlclient || echo "mysqlclient installation failed, will use PyMySQL instead"
                    else
                        $PIP_CMD install mysqlclient || echo "mysqlclient installation failed, will use PyMySQL instead"
                    fi

                    # Install the rest of the dependencies
                    $PIP_CMD install -r requirements.txt
                '''
            }
        }

        stage('Run Tests') {
            steps {
                sh '''
                    # Determine the correct Python command if not already set
                    if [ -z "$PYTHON_CMD" ]; then
                        if command -v python &> /dev/null; then
                            PYTHON_CMD="python"
                        elif command -v python3 &> /dev/null; then
                            PYTHON_CMD="python3"
                        else
                            echo "ERROR: Neither 'python' nor 'python3' commands are available. Please install Python."
                            exit 1
                        fi
                    fi

                    # Activate virtual environment and run tests
                    . ${VENV_NAME}/bin/activate
                    $PYTHON_CMD manage.py test
                '''
            }
        }

        stage('Pre-Setup') {
            steps {
                sh '''
                    # Check Python installation
                    if command -v python &> /dev/null; then
                        PYTHON_CMD="python"
                        echo "Using 'python' command"
                    elif command -v python3 &> /dev/null; then
                        PYTHON_CMD="python3"
                        echo "Using 'python3' command"
                    else
                        echo "ERROR: Neither 'python' nor 'python3' commands are available. Please install Python."
                        exit 1
                    fi

                    # Check Python version
                    $PYTHON_CMD --version

                    # Check pip installation
                    if command -v pip &> /dev/null; then
                        PIP_CMD="pip"
                        echo "Using 'pip' command"
                    elif command -v pip3 &> /dev/null; then
                        PIP_CMD="pip3"
                        echo "Using 'pip3' command"
                    else
                        echo "ERROR: Neither 'pip' nor 'pip3' commands are available. Please install pip."
                        exit 1
                    fi

                    # Install PyMySQL globally to ensure it's available for settings.py
                    # This is needed because settings.py imports it before the virtual environment is activated
                    $PIP_CMD install pymysql

                    # Verify PyMySQL installation
                    $PYTHON_CMD -c "import pymysql; print(f'PyMySQL version: {pymysql.__version__}')"
                '''
            }
        }

        stage('Setup Environment File') {
            steps {
                sh '''
                    # Create .env file directly without using setup-env.sh
                    echo "Creating .env file directly..."

                    # Set environment-specific variables
                    if [ "${DEPLOY_ENV}" == "production" ]; then
                        cat > .env << EOL
# Production Environment Configuration
DEBUG=False
DB_ENGINE=django.db.backends.mysql
DB_NAME=ticketing_tool
DB_USER=root
DB_PASSWORD=admin@123
DB_HOST=***************
DB_PORT=3306
BACKEND_URL=https://ticket.nexware-global.com:9049
ALLOWED_HOSTS=["ticket.nexware-global.com", "localhost", "127.0.0.1"]
CSRF_TRUSTED_ORIGINS=["https://ticket.nexware-global.com:9048"]
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
ROOT_URLCONF=ticketing_system.urls
EOL
                    elif [ "${DEPLOY_ENV}" == "staging" ]; then
                        cat > .env << EOL
# Staging Environment Configuration
DEBUG=True
DB_ENGINE=django.db.backends.mysql
DB_NAME=nex_ticket_stg_db
DB_USER=nex-ticketing-stg
DB_PASSWORD=RsRtW8u96@N
DB_HOST=***************
DB_PORT=3306
BACKEND_URL=http://internal-project.nexware-global.com:9019
ALLOWED_HOSTS=["internal-project.nexware-global.com", "localhost", "127.0.0.1"]
CSRF_TRUSTED_ORIGINS=["http://internal-project.nexware-global.com:9018"]
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
ROOT_URLCONF=ticketing_system.urls
EOL
                    else
                        cat > .env << EOL
# Development Environment Configuration
DEBUG=True
DB_ENGINE=django.db.backends.mysql
DB_NAME=ticketing_tool
DB_USER=root
DB_PASSWORD=
DB_HOST=localhost
DB_PORT=3306
BACKEND_URL=http://localhost:8000
ALLOWED_HOSTS=["localhost", "127.0.0.1"]
CSRF_TRUSTED_ORIGINS=["http://localhost:3000"]
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
ROOT_URLCONF=ticketing_system.urls
EOL
                    fi

                    # Verify that the .env file was created
                    if [ ! -f ".env" ]; then
                        echo "Error: .env file was not created"
                        exit 1
                    fi

                    echo "Environment file (.env) created successfully"

                    # Show the created .env file for debugging (mask sensitive values)
                    echo "Contents of .env file (with passwords masked):"
                    cat .env | sed 's/PASSWORD=.*/PASSWORD=********/g'
                '''
            }
        }

        stage('Backup') {
            when {
                expression { env.DEPLOY_ENV == 'production' || env.DEPLOY_ENV == 'staging' }
            }
            steps {
                sh '''
                    # Create backup directory if it doesn't exist
                    mkdir -p ${BACKUP_DIR}/${DEPLOY_ENV}/$(date +%Y%m%d_%H%M%S)

                    # Backup database
                    if [ "${DEPLOY_ENV}" == "production" ]; then
                        mysqldump -h ${DB_HOST} -u root -padmin@123 ticketing_tool > ${BACKUP_DIR}/${DEPLOY_ENV}/$(date +%Y%m%d_%H%M%S)/db_backup.sql
                    else
                        mysqldump -h ${DB_HOST} -u nex-ticketing-stg -pRsRtW8u96@N nex_ticket_stg_db > ${BACKUP_DIR}/${DEPLOY_ENV}/$(date +%Y%m%d_%H%M%S)/db_backup.sql
                    fi

                    # Backup media files
                    if [ -d "${DEPLOY_DIR}/${DEPLOY_ENV}/media" ]; then
                        cp -r ${DEPLOY_DIR}/${DEPLOY_ENV}/media ${BACKUP_DIR}/${DEPLOY_ENV}/$(date +%Y%m%d_%H%M%S)/
                    fi
                '''
            }
        }

        stage('Database Migration') {
            when {
                expression { env.DEPLOY_ENV == 'production' || env.DEPLOY_ENV == 'staging' }
            }
            steps {
                sh '''
                    # Determine the correct Python command if not already set
                    if [ -z "$PYTHON_CMD" ]; then
                        if command -v python &> /dev/null; then
                            PYTHON_CMD="python"
                        elif command -v python3 &> /dev/null; then
                            PYTHON_CMD="python3"
                        else
                            echo "ERROR: Neither 'python' nor 'python3' commands are available. Please install Python."
                            exit 1
                        fi
                    fi

                    # Activate virtual environment
                    . ${VENV_NAME}/bin/activate

                    # Run migrations
                    $PYTHON_CMD manage.py migrate --noinput

                    # Collect static files
                    $PYTHON_CMD manage.py collectstatic --noinput
                '''
            }
        }

        stage('Build') {
            when {
                expression { env.DEPLOY_ENV == 'production' || env.DEPLOY_ENV == 'staging' }
            }
            steps {
                sh '''
                    # Create deployment directory if it doesn't exist
                    mkdir -p ${DEPLOY_DIR}/${DEPLOY_ENV}

                    # Copy application files
                    rsync -av --exclude='.git' --exclude='venv' --exclude='__pycache__' --exclude='*.pyc' ./ ${DEPLOY_DIR}/${DEPLOY_ENV}/

                    # Set proper permissions
                    chmod -R 755 ${DEPLOY_DIR}/${DEPLOY_ENV}

                    # Create media directory if it doesn't exist
                    mkdir -p ${DEPLOY_DIR}/${DEPLOY_ENV}/media
                    chmod -R 777 ${DEPLOY_DIR}/${DEPLOY_ENV}/media

                    # Create staticfiles directory if it doesn't exist
                    mkdir -p ${DEPLOY_DIR}/${DEPLOY_ENV}/staticfiles
                    chmod -R 755 ${DEPLOY_DIR}/${DEPLOY_ENV}/staticfiles

                    # Create logs directory if it doesn't exist
                    mkdir -p ${DEPLOY_DIR}/${DEPLOY_ENV}/logs
                    chmod -R 777 ${DEPLOY_DIR}/${DEPLOY_ENV}/logs

                    # Set environment variables for systemd service
                    echo "Setting up environment for systemd service..."

                    # Create a systemd environment file from our .env
                    ENV_FILE="/var/lib/jenkins/environments/${DEPLOY_ENV}.env"

                    # Create directory if it doesn't exist
                    mkdir -p "$(dirname "$ENV_FILE")"

                    # Copy and format the .env file for systemd
                    grep -v "^#" .env | grep -v "^$" | grep -v "\\[" | grep -v "\\]" > "$ENV_FILE"

                    # Set permissions
                    chmod 600 "$ENV_FILE"

                    # Set service name based on environment
                    if [ "${DEPLOY_ENV}" == "production" ]; then
                        SERVICE_NAME="nex-ticketing-prod"
                    else
                        SERVICE_NAME="nex-ticketing-stage"
                    fi

                    echo "Build completed for service: $SERVICE_NAME"
                    echo "Application is ready to be deployed at ${DEPLOY_DIR}/${DEPLOY_ENV}"

                    # Create a simple deployment report
                    cat > deployment_report.txt << EOL
Deployment Report
----------------
Environment: ${DEPLOY_ENV}
Service Name: ${SERVICE_NAME}
Deployment Directory: ${DEPLOY_DIR}/${DEPLOY_ENV}
Build Number: ${BUILD_NUMBER}
Branch: ${BRANCH_NAME}
Date: $(date)

To manually deploy this build:
1. Restart the service: sudo systemctl restart ${SERVICE_NAME}
2. Verify the service is running: sudo systemctl status ${SERVICE_NAME}
EOL

                    echo "Deployment report created: deployment_report.txt"
                    cat deployment_report.txt
                '''
            }
        }
    }

    post {
        success {
            script {
                // Send success notification to Zoho Cliq
                sh """
                    curl -X POST -H 'Content-Type: application/json' -d '{
                        "text": "✅ *Build Successful*\\n*Environment:* ${env.DEPLOY_ENV}\\n*Branch:* ${env.BRANCH_NAME}\\n*Build:* ${env.BUILD_NUMBER}\\n*Location:* ${env.DEPLOY_DIR}/${env.DEPLOY_ENV}"
                    }' https://cliq.zoho.com/api/v2/channelsbyname/${NOTIFICATION_CHANNEL}/message
                """

                // Archive the deployment report
                archiveArtifacts artifacts: 'deployment_report.txt', allowEmptyArchive: true
            }
        }
        failure {
            script {
                // Send failure notification to Zoho Cliq
                sh """
                    curl -X POST -H 'Content-Type: application/json' -d '{
                        "text": "❌ *Build Failed*\\n*Environment:* ${env.DEPLOY_ENV}\\n*Branch:* ${env.BRANCH_NAME}\\n*Build:* ${env.BUILD_NUMBER}\\n*Details:* Check Jenkins logs for more information"
                    }' https://cliq.zoho.com/api/v2/channelsbyname/${NOTIFICATION_CHANNEL}/message
                """
            }
        }
        always {
            // Keep the workspace for manual deployment if needed
            echo "Build process completed. The application is ready to be deployed manually."
        }
    }
}
