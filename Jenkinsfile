pipeline {
    agent any

    environment {
        // Set environment variables based on branch
        DEPLOY_ENV = "${env.BRANCH_NAME == 'main' ? 'production' : 'staging'}"
        DEPLOY_PORT = "${env.BRANCH_NAME == 'main' ? '9049' : '9019'}"
        PROJECT_NAME = 'Nex-Ticketing-System'
    }

    stages {
        stage('Checkout') {
            steps {
                echo "🔄 Checking out ${env.BRANCH_NAME} branch..."
                checkout scm

                script {
                    // Get commit information
                    env.GIT_COMMIT_MSG = sh(
                        script: 'git log -1 --pretty=%B',
                        returnStdout: true
                    ).trim()
                    env.GIT_AUTHOR = sh(
                        script: 'git log -1 --pretty=%an',
                        returnStdout: true
                    ).trim()
                }

                echo "📝 Commit: ${env.GIT_COMMIT_MSG}"
                echo "👤 Author: ${env.GIT_AUTHOR}"
            }
        }

        stage('Environment Detection') {
            steps {
                script {
                    echo "🎯 Branch: ${env.BRANCH_NAME}"
                    echo "🌍 Environment: ${env.DEPLOY_ENV}"
                    echo "🚀 Port: ${env.DEPLOY_PORT}"

                    if (env.BRANCH_NAME == 'main') {
                        echo "🔴 PRODUCTION DEPLOYMENT INITIATED"
                        env.DB_NAME = 'nex_ticket_db'
                        env.DB_USER = 'nex-ticketing'
                        env.BACKEND_URL = 'https://ticket.nexware-global.com:9049/'
                    } else if (env.BRANCH_NAME == 'development') {
                        echo "🟡 STAGING DEPLOYMENT INITIATED"
                        env.DB_NAME = 'nex_ticket_stg_db'
                        env.DB_USER = 'nex-ticketing-stg'
                        env.BACKEND_URL = 'http://internal-project.nexware-global.com:9019/'
                    } else {
                        error("❌ Unsupported branch: ${env.BRANCH_NAME}")
                    }
                }
            }
        }

        stage('Pre-deployment Checks') {
            steps {
                echo "🔍 Running pre-deployment checks..."

                // Check if required files exist
                sh '''
                    echo "📁 Checking required files..."
                    if [ ! -f "deploy.sh" ]; then
                        echo "❌ deploy.sh not found!"
                        exit 1
                    fi

                    if [ ! -f "setup-env.sh" ]; then
                        echo "❌ setup-env.sh not found!"
                        exit 1
                    fi

                    if [ ! -f "requirements.txt" ]; then
                        echo "❌ requirements.txt not found!"
                        exit 1
                    fi

                    echo "✅ All required files present"
                '''

                // Make scripts executable
                sh '''
                    chmod +x deploy.sh
                    chmod +x setup-env.sh
                    echo "✅ Scripts made executable"
                '''
            }
        }

        stage('Stop Existing Service') {
            steps {
                echo "🛑 Stopping existing services on port ${env.DEPLOY_PORT}..."
                sh '''
                    # Stop any existing Django processes on the target port
                    pkill -f "manage.py runserver.*:${DEPLOY_PORT}" || true
                    sleep 3

                    # Force kill if still running
                    lsof -ti:${DEPLOY_PORT} | xargs kill -9 2>/dev/null || true
                    sleep 2

                    echo "✅ Existing services stopped"
                '''
            }
        }

        stage('Deploy Application') {
            steps {
                echo "🚀 Deploying ${env.PROJECT_NAME} to ${env.DEPLOY_ENV}..."

                script {
                    // Set Git branch environment variable for deploy script
                    env.GIT_BRANCH = "origin/${env.BRANCH_NAME}"

                    // Run deployment script with better error handling
                    def deployResult = sh(
                        script: '''
                            set +e  # Don't exit on error immediately
                            ./deploy.sh
                            exit_code=$?

                            # Handle rsync warning (code 24) as success
                            if [ $exit_code -eq 24 ]; then
                                echo "⚠️  Deployment completed with rsync warnings (code 24) - treating as success"
                                exit 0
                            elif [ $exit_code -eq 0 ]; then
                                echo "✅ Deployment completed successfully"
                                exit 0
                            else
                                echo "❌ Deployment failed with exit code: $exit_code"
                                exit $exit_code
                            fi
                        ''',
                        returnStatus: true
                    )

                    if (deployResult != 0) {
                        error("❌ Deployment failed with exit code: ${deployResult}")
                    }
                }

                echo "✅ Deployment completed successfully!"
            }
        }

        stage('Health Check') {
            steps {
                echo "🏥 Running health checks..."

                script {
                    // Wait for application to start
                    sleep(10)

                    // Check if process is running
                    def processCheck = sh(
                        script: "ps aux | grep 'manage.py runserver.*:${env.DEPLOY_PORT}' | grep -v grep",
                        returnStatus: true
                    )

                    if (processCheck != 0) {
                        error("❌ Application process not running on port ${env.DEPLOY_PORT}")
                    }

                    // Check if port is listening
                    def portCheck = sh(
                        script: "lsof -i:${env.DEPLOY_PORT}",
                        returnStatus: true
                    )

                    if (portCheck != 0) {
                        error("❌ Port ${env.DEPLOY_PORT} is not listening")
                    }

                    echo "✅ Application is running on port ${env.DEPLOY_PORT}"
                }
            }
        }

        stage('API Health Check') {
            steps {
                echo "🔗 Testing API endpoints..."

                script {
                    // Test API endpoint
                    def apiCheck = sh(
                        script: "curl -f -s -o /dev/null -w '%{http_code}' http://localhost:${env.DEPLOY_PORT}/api/ || echo 'FAILED'",
                        returnStdout: true
                    ).trim()

                    if (apiCheck != '200') {
                        echo "⚠️  API health check returned: ${apiCheck}"
                        echo "🔄 Waiting additional time for application startup..."
                        sleep(15)

                        // Retry API check
                        apiCheck = sh(
                            script: "curl -f -s -o /dev/null -w '%{http_code}' http://localhost:${env.DEPLOY_PORT}/api/ || echo 'FAILED'",
                            returnStdout: true
                        ).trim()

                        if (apiCheck != '200') {
                            echo "❌ API health check failed: ${apiCheck}"
                            // Don't fail the build, just warn
                            echo "⚠️  Deployment completed but API may need manual verification"
                        } else {
                            echo "✅ API is responding correctly"
                        }
                    } else {
                        echo "✅ API is responding correctly"
                    }
                }
            }
        }
    }

    post {
        success {
            echo "🎉 Deployment to ${env.DEPLOY_ENV} completed successfully!"
            echo "🌐 Application URL: ${env.BACKEND_URL}"
            echo "📊 Port: ${env.DEPLOY_PORT}"
            echo "💾 Database: ${env.DB_NAME}"

            // You can add notification here (Zoho Cliq, email, etc.)
            script {
                def message = """
🎉 **Deployment Successful!**

**Environment:** ${env.DEPLOY_ENV}
**Branch:** ${env.BRANCH_NAME}
**Commit:** ${env.GIT_COMMIT_MSG}
**Author:** ${env.GIT_AUTHOR}
**URL:** ${env.BACKEND_URL}
**Port:** ${env.DEPLOY_PORT}

✅ Application is running and healthy!
                """.trim()

                echo message
            }
        }

        failure {
            echo "❌ Deployment to ${env.DEPLOY_ENV} failed!"

            // Collect logs for debugging
            sh '''
                echo "📋 Collecting deployment logs..."
                if [ -f "/tmp/django_${DEPLOY_ENV}.log" ]; then
                    echo "=== Django Logs ==="
                    tail -50 /tmp/django_${DEPLOY_ENV}.log || true
                fi

                echo "=== Process Status ==="
                ps aux | grep python || true

                echo "=== Port Status ==="
                lsof -i:${DEPLOY_PORT} || true
            '''

            script {
                def message = """
❌ **Deployment Failed!**

**Environment:** ${env.DEPLOY_ENV}
**Branch:** ${env.BRANCH_NAME}
**Commit:** ${env.GIT_COMMIT_MSG}
**Author:** ${env.GIT_AUTHOR}

Please check Jenkins logs for details.
                """.trim()

                echo message
            }
        }

        always {
            echo "🧹 Cleaning up..."

            // Archive logs
            archiveArtifacts artifacts: '*.log', allowEmptyArchive: true

            // Clean workspace if needed
            // cleanWs()
        }
    }
}
