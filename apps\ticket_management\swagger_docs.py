"""
Swagger documentation decorators and schemas for Ticket Management API endpoints.
This file contains comprehensive API documentation for ticket-related endpoints.
"""

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

# 🎫 Ticket List/Create Documentation
ticket_list_swagger_docs = swagger_auto_schema(
    operation_summary="🎫 List/Create Tickets",
    operation_description="""
    **GET**: Retrieve tickets with filtering, searching, and pagination
    **POST**: Create a new support ticket
    
    **Filtering Options:**
    - Status (Open, In Progress, Resolved, Closed)
    - Priority (Low, Medium, High, Critical)
    - Category (Hardware, Software, Network, etc.)
    - Assigned technician
    - Date range
    - Project
    
    **Search**: Search by ticket ID, title, or description
    **Pagination**: Supports page-based pagination
    """,
    manual_parameters=[
        openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, default=1),
        openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page (or 'all')", type=openapi.TYPE_STRING, default=10),
        openapi.Parameter('search', openapi.IN_QUERY, description="Search in ticket ID, title", type=openapi.TYPE_STRING),
        openapi.Parameter('status', openapi.IN_QUERY, description="Filter by status ID", type=openapi.TYPE_INTEGER),
        openapi.Parameter('priority', openapi.IN_QUERY, description="Filter by priority ID", type=openapi.TYPE_INTEGER),
        openapi.Parameter('category', openapi.IN_QUERY, description="Filter by category ID", type=openapi.TYPE_INTEGER),
        openapi.Parameter('assigned_to', openapi.IN_QUERY, description="Filter by assigned user ID", type=openapi.TYPE_INTEGER),
        openapi.Parameter('project_id', openapi.IN_QUERY, description="Filter by project ID", type=openapi.TYPE_INTEGER),
        openapi.Parameter('start_date', openapi.IN_QUERY, description="Start date (YYYY-MM-DD)", type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
        openapi.Parameter('end_date', openapi.IN_QUERY, description="End date (YYYY-MM-DD)", type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['title', 'description', 'category', 'priority'],
        properties={
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Ticket title/subject',
                example='Computer not starting up'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Detailed description of the issue',
                example='My computer shows a blue screen when I try to start it. This started happening after the latest Windows update.'
            ),
            'category': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Category ID (Hardware, Software, Network, etc.)',
                example=1
            ),
            'priority': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Priority level ID (1=Low, 2=Medium, 3=High, 4=Critical)',
                example=2
            ),
            'project_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Project ID (optional)',
                example=1
            ),
            'attachments': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_BINARY),
                description='File attachments (images, documents, etc.)'
            ),
        }
    ),
    responses={
        200: openapi.Response(
            description="Tickets retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'code': openapi.Schema(type=openapi.TYPE_INTEGER, example=200),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'ticket_id': openapi.Schema(type=openapi.TYPE_STRING, example='TKT-2024-001'),
                                'title': openapi.Schema(type=openapi.TYPE_STRING),
                                'description': openapi.Schema(type=openapi.TYPE_STRING),
                                'status': openapi.Schema(type=openapi.TYPE_STRING, example='Open'),
                                'priority': openapi.Schema(type=openapi.TYPE_STRING, example='Medium'),
                                'category': openapi.Schema(type=openapi.TYPE_STRING, example='Hardware'),
                                'created_by': openapi.Schema(type=openapi.TYPE_STRING),
                                'assigned_to': openapi.Schema(type=openapi.TYPE_STRING),
                                'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                                'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                            }
                        )
                    ),
                    'pagination': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'current_page': openapi.Schema(type=openapi.TYPE_INTEGER),
                            'total_pages': openapi.Schema(type=openapi.TYPE_INTEGER),
                            'total_items': openapi.Schema(type=openapi.TYPE_INTEGER),
                            'items_per_page': openapi.Schema(type=openapi.TYPE_INTEGER),
                        }
                    )
                }
            )
        ),
        201: openapi.Response(description="Ticket created successfully"),
        400: openapi.Response(description="Invalid data provided"),
        401: openapi.Response(description="Authentication required"),
    },
    tags=['🎫 Ticket Management']
)

# 🎫 Ticket Detail Documentation
ticket_detail_swagger_docs = swagger_auto_schema(
    operation_summary="🎫 Ticket Details",
    operation_description="""
    **GET**: Retrieve detailed information about a specific ticket
    **PUT**: Update ticket information
    **DELETE**: Delete/Archive ticket
    
    **Includes:**
    - Complete ticket information
    - Chat/comment history
    - Attachment files
    - Status tracking history
    - Assigned technician details
    """,
    responses={
        200: openapi.Response(
            description="Ticket details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'ticket_id': openapi.Schema(type=openapi.TYPE_STRING),
                    'title': openapi.Schema(type=openapi.TYPE_STRING),
                    'description': openapi.Schema(type=openapi.TYPE_STRING),
                    'status': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'priority': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'category': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'created_by': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'assigned_to': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'attachments': openapi.Schema(type=openapi.TYPE_ARRAY),
                    'chat_history': openapi.Schema(type=openapi.TYPE_ARRAY),
                    'status_tracking': openapi.Schema(type=openapi.TYPE_ARRAY),
                }
            )
        ),
        404: openapi.Response(description="Ticket not found"),
        401: openapi.Response(description="Authentication required"),
    },
    tags=['🎫 Ticket Management']
)

# 💬 Chat/Comments Documentation
chat_swagger_docs = swagger_auto_schema(
    operation_summary="💬 Ticket Chat/Comments",
    operation_description="""
    **GET**: Retrieve chat messages for a ticket
    **POST**: Add new chat message/comment
    
    **Features:**
    - Real-time messaging
    - File attachments in messages
    - Message history
    - User identification
    - Timestamp tracking
    """,
    manual_parameters=[
        openapi.Parameter('ticket_id', openapi.IN_QUERY, description="Ticket ID to get chats for", type=openapi.TYPE_INTEGER, required=True),
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['message', 'ticket'],
        properties={
            'message': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Chat message content',
                example='I have tried restarting the computer but the issue persists.'
            ),
            'ticket': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Ticket ID',
                example=1
            ),
            'attachments': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_BINARY),
                description='File attachments for the message'
            ),
        }
    ),
    responses={
        200: openapi.Response(description="Chat messages retrieved successfully"),
        201: openapi.Response(description="Chat message added successfully"),
        400: openapi.Response(description="Invalid data provided"),
        401: openapi.Response(description="Authentication required"),
    },
    tags=['💬 Communication']
)

# 📊 Ticket Status Documentation
ticket_status_swagger_docs = swagger_auto_schema(
    operation_summary="📊 Ticket Status Options",
    operation_description="""
    Get list of all available ticket statuses.
    
    **Common Statuses:**
    - Open (New tickets)
    - In Progress (Being worked on)
    - Pending (Waiting for user response)
    - Resolved (Fixed, awaiting confirmation)
    - Closed (Completed)
    - Cancelled (Cancelled by user)
    """,
    responses={
        200: openapi.Response(
            description="Status list retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'status_name': openapi.Schema(type=openapi.TYPE_STRING),
                        'description': openapi.Schema(type=openapi.TYPE_STRING),
                        'color_code': openapi.Schema(type=openapi.TYPE_STRING, example='#28a745'),
                        'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            )
        ),
    },
    tags=['🎫 Ticket Management']
)

# 🔔 Notifications Documentation
notifications_swagger_docs = swagger_auto_schema(
    operation_summary="🔔 User Notifications",
    operation_description="""
    **GET**: Retrieve user notifications
    **POST**: Mark notifications as read
    **DELETE**: Clear notifications
    
    **Notification Types:**
    - Ticket assignments
    - Status updates
    - New comments
    - System announcements
    """,
    responses={
        200: openapi.Response(
            description="Notifications retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'unread_count': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'notifications': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'title': openapi.Schema(type=openapi.TYPE_STRING),
                                'message': openapi.Schema(type=openapi.TYPE_STRING),
                                'type': openapi.Schema(type=openapi.TYPE_STRING),
                                'is_read': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                                'ticket_id': openapi.Schema(type=openapi.TYPE_INTEGER),
                            }
                        )
                    )
                }
            )
        ),
    },
    tags=['🔔 Notifications']
)
