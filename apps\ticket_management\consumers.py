# import json
# from channels.generic.websocket import AsyncWebsocketConsumer

# class ChatConsumer(AsyncWebsocketConsumer):
#     async def connect(self):
#         self.group_name = "chat_group"
        
#         # Join the group
#         await self.channel_layer.group_add(
#             self.group_name,
#             self.channel_name
#         )
#         await self.accept()

#     async def disconnect(self, close_code):
#         # Leave the group
#         await self.channel_layer.group_discard(
#             self.group_name,
#             self.channel_name
#         )

#     async def receive(self, text_data):
#         # Handle incoming WebSocket messages
#         text_data_json = json.loads(text_data)
#         message = text_data_json['message']
        
#         # Broadcast the message to the group
#         await self.channel_layer.group_send(
#             self.group_name,
#             {
#                 'type': 'chat_message',
#                 'message': message
#             }
#         )

#     async def chat_message(self, event):
#         # Send the message to the WebSocket
#         message = event['message']
#         await self.send(text_data=json.dumps({
#             'message': message
#         }))

# your_app_name/consumers.py

# import json
# from channels.generic.websocket import AsyncWebsocketConsumer

# class CommentConsumer(AsyncWebsocketConsumer):
#     async def connect(self):
#         self.ticket_id = self.scope["url_route"]["kwargs"]["ticket_id"]
#         self.room_group_name = f"chat_{self.ticket_id}"

#         # Add this socket to the chat room group
#         await self.channel_layer.group_add(self.room_group_name, self.channel_name)
#         await self.accept()

#     async def disconnect(self, close_code):
#         # Remove from chat room group
#         await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

#     async def receive(self, text_data):
#         data = json.loads(text_data)
#         message = data["message"]
#         sender = data["sender"]

#         # Broadcast message to room group
#         await self.channel_layer.group_send(
#             self.room_group_name,
#             {
#                 "type": "chat_message",
#                 "message": message,
#                 "sender": sender,
#             },
#         )

#     async def chat_message(self, event):
#         message = event["message"]
#         sender = event["sender"]

#         # Send message to WebSocket
#         await self.send(text_data=json.dumps({"message": message, "sender": sender}))

