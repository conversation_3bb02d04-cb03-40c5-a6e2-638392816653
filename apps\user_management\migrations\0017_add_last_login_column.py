from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0016_merge_20250508_0445'),
    ]

    operations = [
        migrations.RunSQL(
            sql="""
            -- Check if last_login column exists, add if it doesn't
            SET @column_exists = (
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = 'user_management_user'
                AND column_name = 'last_login'
                AND table_schema = DATABASE()
            );
            
            SET @sql = IF(@column_exists = 0, 
                'ALTER TABLE user_management_user ADD COLUMN last_login DATETIME NULL',
                'SELECT "Column last_login already exists"');
            
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            """,
            reverse_sql="""
            -- No reverse operation needed
            """
        ),
    ]
