# import socketio

# # Create a Socket.IO client instance
# sio = socketio.Client()

# # Connection event
# @sio.event
# def connect():
#     print("Connected to server!")
#     sio.emit("chat_message", {"msg": "Hello, Server welcome to ticketing tool system!"})

# # Disconnect event
# @sio.event
# def disconnect():
#     print("Disconnected from server!")

# # Reply event handler
# @sio.event
# def reply(data):
#     print(f"Server reply: {data['msg']}")

# @sio.on("create-something")
# def another_event(sid, data):
#     print(f"Server reply: {data}")
# # Connect to the server
# if __name__ == "__main__":
#     sio.connect("http://localhost:8080")
#     sio.wait()
