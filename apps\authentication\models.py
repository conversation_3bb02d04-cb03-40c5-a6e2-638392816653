from django.db import models
from apps.user_management.models import User,Role
from django.utils.timezone import now


class Module(models.Model):
    module_name=models.CharField(max_length=50, unique=True, null=False, blank=False)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='modules_created', null=True, blank=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='modules_updated', null=True, blank=True)
   
    def str(self):
        return self.module_name

class SubModule(models.Model):
    module = models.ForeignKey(Module, on_delete=models.CASCADE, related_name='submodules', null=False)
    sub_module_name = models.CharField(max_length=50, unique=True, null=False, blank=False)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)

    def str(self):
        return self.sub_module_name

class RoleModuleAccess(models.Model):
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='role_premissions', to_field='role_id')
    module = models.ForeignKey(Module, on_delete=models.CASCADE, related_name='role_premissions', to_field='id')
    has_access = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='rolepremissions_created', null=True, blank=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='rolepremissions_updated', null=True, blank=True)

    def str(self):
        return {self.role} - {self.module}
    
class SubModuleAccess(models.Model):
    submodule = models.ForeignKey(SubModule, on_delete=models.CASCADE, related_name='submodule_permissions', to_field="id")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name="role_permisions", to_field='role_id')
    has_access = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='submodulepermissions_created', null=True, blank=True)
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='submodulepremissions_updated', null=True, blank=True)

    def str(self):
        return {self.role}-{self.submodule_id}
    
class PasswordResetToken(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    token = models.CharField(max_length=1000)
    created_at = models.DateTimeField(auto_now_add=True)
    used = models.BooleanField(default=False)
    lifetime = models.DateTimeField()

    def __str__(self):
        return f"{self.user.email} - Token: {self.token}"
