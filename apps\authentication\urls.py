from django.urls import path
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView, TokenBlacklistView
from apps.authentication.views import (
    ChangePasswordAPIView,
    LoginAPIView,
    PasswordResetRequestAPIView,
    PasswordResetConfirmAPIView,
    ForgotPasswordAPIView,
    ResetPasswordAPIView,
    SaveFCMTokenView,
    ValidateResetTokenAPIView,
    create_module,
    get_modules,
    get_module,
    get_role_module_and_submodule_access,
    update_module,
    create_submodule,
    update_role_module_and_submodule_access,
    get_all_submodules
)


urlpatterns = [
    # Login
    path('login', LoginAPIView.as_view(), name='login'),
    #FCM token
    path("save-fcm-token/", SaveFCMTokenView.as_view(), name="save-fcm-token"),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('token/logout/', TokenBlacklistView.as_view(), name='token_blacklist'),


    # Password reset
    path('password-reset-request/', PasswordResetRequestAPIView.as_view(), name='password_reset_request'),
    path('password-reset-confirm/<int:user_id>/<str:token>/', PasswordResetConfirmAPIView.as_view(), name='password_reset_confirm'),

    #change password
    path('change-password/', ChangePasswordAPIView.as_view(), name='change_password'),
    # Forgot Password
    path('forgot-password-request', ForgotPasswordAPIView.as_view(), name='password_reset_request'),
    path('password-reset-confirm', ResetPasswordAPIView.as_view(), name='password_reset_confirm'),
    path('validate-reset-token/', ValidateResetTokenAPIView.as_view(), name='validate_reset_token'),

    # Module
    path('modules/', get_modules, name='get_modules'),  # Get all modules
    path('modules/create/', create_module, name='create_module'),  # Create a module
    path('modules/<int:pk>/', get_module, name='get_module'),  # Get a single module by ID
    path('modules/update/<int:id>/', update_module, name='update_module'),  # Update a module

    #SubModule
    path('modules/submodules/create/', create_submodule, name='create-sub_module'),
    path('modules/submodules/', get_all_submodules, name='get-all-submodules'),

    #RoleModuleAccess
    path('user_access/<str:role_id>', get_role_module_and_submodule_access, name="get-access"),
    path('user_access/update/<str:role_id>', update_role_module_and_submodule_access, name="update-user-access"),
]

