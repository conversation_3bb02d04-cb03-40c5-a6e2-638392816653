#!/usr/bin/env python
"""
Test script to verify the Japanese filename fix works
Tests the specific filename that was causing the error
"""

import os
import re
import unicodedata
import hashlib
from datetime import datetime

def test_problematic_filename():
    """Test the specific filename that was causing the error"""
    
    print("🧪 Testing Problematic Japanese Filename")
    print("=" * 60)
    
    # The exact filename that was causing the error
    problematic_filename = "【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx"
    ticket_id = 52
    
    print(f"Testing filename: {problematic_filename}")
    print(f"Unicode characters: {[ord(c) for c in problematic_filename if ord(c) > 127]}")
    
    try:
        base, ext = os.path.splitext(problematic_filename)
        print(f"Base: {base}")
        print(f"Extension: {ext}")
        
        # Step 1: Normalize Unicode characters
        safe_base = unicodedata.normalize('NFKD', base)
        print(f"1. Normalized: {safe_base}")
        
        # Step 2: Try to transliterate Unicode to ASCII using unidecode
        try:
            from unidecode import unidecode
            safe_base = unidecode(safe_base)
            print(f"2. Unidecode result: {safe_base}")
        except ImportError:
            print("2. Unidecode not available, using fallback")
            safe_base = ''.join(char for char in safe_base if ord(char) < 128)
            print(f"2. Fallback result: {safe_base}")
        except Exception as e:
            print(f"2. Unidecode error: {e}")
            safe_base = ''.join(char for char in safe_base if ord(char) < 128)
            print(f"2. Fallback result: {safe_base}")
        
        # Step 3: Clean up the result
        safe_base = re.sub(r'[^\w\s\-_.]', '_', safe_base)
        safe_base = re.sub(r'\s+', '_', safe_base)
        safe_base = re.sub(r'_+', '_', safe_base)
        safe_base = safe_base.strip('_')[:40]
        print(f"3. Cleaned: {safe_base}")
        
        # Step 4: Create final filename
        if safe_base and len(safe_base) > 2:
            ascii_safe_name = f"{safe_base}_ticket_{ticket_id}{ext}"
            print(f"4. Final filename: {ascii_safe_name}")
            
            # Test ASCII compatibility
            try:
                ascii_safe_name.encode('ascii')
                print("✅ Final filename is ASCII-safe!")
                return ascii_safe_name
            except UnicodeEncodeError as e:
                print(f"❌ Still has encoding issues: {e}")
                raise
        else:
            raise ValueError("Sanitized name too short")
            
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        # Ultimate fallback
        original_hash = hashlib.md5(problematic_filename.encode('utf-8')).hexdigest()[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        fallback_name = f"file_{ticket_id}_{timestamp}_{original_hash}{ext}"
        print(f"🔄 Fallback filename: {fallback_name}")
        return fallback_name

def test_error_message_safety():
    """Test that error messages are ASCII-safe"""
    
    print(f"\n🛡️ Testing Error Message Safety")
    print("=" * 60)
    
    problematic_filename = "【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx"
    
    # Test filename conversion for error messages
    try:
        from unidecode import unidecode
        safe_file_name = unidecode(problematic_filename)
        print(f"Safe filename for errors: {safe_file_name}")
    except ImportError:
        safe_file_name = problematic_filename.encode('ascii', errors='replace').decode('ascii')
        print(f"Fallback safe filename: {safe_file_name}")
    except Exception:
        safe_file_name = "japanese_filename"
        print(f"Ultimate fallback: {safe_file_name}")
    
    # Test error message conversion
    test_error = "Error with file 【JRタワー】: 'ascii' codec can't encode character '\\u3010'"
    safe_error = test_error.encode('ascii', errors='replace').decode('ascii')
    print(f"Original error: {test_error}")
    print(f"Safe error: {safe_error}")
    
    # Test creating safe error response
    error_response = {
        "status": 0,
        "code": 400,
        "message": f"Error saving file '{safe_file_name}': File upload failed. Please try with a different filename or contact support.",
        "original_error": safe_error
    }
    
    print(f"✅ Safe error response created:")
    for key, value in error_response.items():
        print(f"  {key}: {value}")

def test_multiple_problematic_filenames():
    """Test multiple problematic Japanese filenames"""
    
    print(f"\n📁 Testing Multiple Problematic Filenames")
    print("=" * 60)
    
    problematic_files = [
        "【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx",
        "〈重要〉売上データ_2024年度.pdf",
        "《会議資料》提案書_最終版.docx",
        "「プロジェクト」進捗レポート.pptx",
        "『年次報告書』2024年版.pdf",
        "＃データ分析＃結果.csv",
        "★重要★契約書_修正版.pdf",
        "◆緊急◆システム障害報告.txt"
    ]
    
    ticket_id = 123
    
    for i, filename in enumerate(problematic_files, 1):
        print(f"\n{i}. Testing: {filename}")
        
        try:
            base, ext = os.path.splitext(filename)
            
            # Apply the same conversion logic
            safe_base = unicodedata.normalize('NFKD', base)
            
            try:
                from unidecode import unidecode
                safe_base = unidecode(safe_base)
            except:
                safe_base = ''.join(char for char in safe_base if ord(char) < 128)
            
            safe_base = re.sub(r'[^\w\s\-_.]', '_', safe_base)
            safe_base = re.sub(r'\s+', '_', safe_base)
            safe_base = re.sub(r'_+', '_', safe_base)
            safe_base = safe_base.strip('_')[:40]
            
            if safe_base and len(safe_base) > 2:
                result = f"{safe_base}_ticket_{ticket_id}{ext}"
            else:
                original_hash = hashlib.md5(filename.encode('utf-8')).hexdigest()[:8]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result = f"file_{ticket_id}_{timestamp}_{original_hash}{ext}"
            
            # Test ASCII safety
            try:
                result.encode('ascii')
                print(f"   ✅ Result: {result}")
            except UnicodeEncodeError:
                print(f"   ❌ Still not ASCII-safe: {result}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_unidecode_installation():
    """Test if unidecode is properly installed"""
    
    print(f"\n📦 Testing Unidecode Installation")
    print("=" * 60)
    
    try:
        from unidecode import unidecode
        
        # Test with the problematic characters
        test_cases = [
            ("【", "["),  # Japanese brackets
            ("】", "]"),
            ("タワー", "tawa"),  # Tower
            ("月度", "getsu du"),  # Monthly
            ("報告書", "bao gao shu"),  # Report
        ]
        
        print("Testing specific character conversions:")
        for original, expected_pattern in test_cases:
            converted = unidecode(original)
            print(f"  {original} → {converted}")
        
        print("✅ Unidecode is working correctly!")
        return True
        
    except ImportError:
        print("❌ Unidecode is not installed!")
        print("Install with: pip install unidecode")
        return False
    except Exception as e:
        print(f"❌ Unidecode error: {e}")
        return False

def simulate_staging_upload():
    """Simulate the staging server upload process"""
    
    print(f"\n🚀 Simulating Staging Server Upload")
    print("=" * 60)
    
    filename = "【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx"
    
    print(f"Uploading: {filename}")
    
    try:
        # Simulate the exact process in views.py
        original_name = filename
        base, ext = os.path.splitext(original_name)
        
        print("Step 1: Starting filename conversion...")
        
        # Normalize
        safe_base = unicodedata.normalize('NFKD', base)
        print(f"Step 2: Normalized: {safe_base}")
        
        # Convert with unidecode
        try:
            from unidecode import unidecode
            safe_base = unidecode(safe_base)
            print(f"Step 3: Unidecode result: {safe_base}")
        except Exception as e:
            print(f"Step 3: Unidecode failed ({e}), using fallback")
            safe_base = ''.join(char for char in safe_base if ord(char) < 128)
        
        # Clean up
        safe_base = re.sub(r'[^\w\s\-_.]', '_', safe_base)
        safe_base = re.sub(r'\s+', '_', safe_base)
        safe_base = re.sub(r'_+', '_', safe_base)
        safe_base = safe_base.strip('_')[:40]
        print(f"Step 4: Cleaned: {safe_base}")
        
        # Create final filename
        ticket_id = 52
        ascii_safe_name = f"{safe_base}_ticket_{ticket_id}{ext}"
        
        # Test ASCII safety
        ascii_safe_name.encode('ascii')
        print(f"Step 5: Final ASCII-safe filename: {ascii_safe_name}")
        
        print("✅ Upload would succeed!")
        print(f"   Storage filename: {ascii_safe_name}")
        print(f"   Display filename: {original_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload would fail: {e}")
        
        # Test error message safety
        try:
            from unidecode import unidecode
            safe_filename = unidecode(filename)
        except:
            safe_filename = filename.encode('ascii', errors='replace').decode('ascii')
        
        safe_error = str(e).encode('ascii', errors='replace').decode('ascii')
        
        print(f"Safe error message would be:")
        print(f"Error saving file '{safe_filename}': File upload failed.")
        
        return False

def main():
    """Main test function"""
    
    print("🇯🇵 Japanese Filename Fix - Comprehensive Test")
    print("=" * 80)
    
    # Test the specific problematic filename
    test_problematic_filename()
    
    # Test error message safety
    test_error_message_safety()
    
    # Test unidecode installation
    unidecode_ok = test_unidecode_installation()
    
    # Test multiple problematic filenames
    test_multiple_problematic_filenames()
    
    # Simulate staging upload
    upload_ok = simulate_staging_upload()
    
    print(f"\n🎉 Test Summary")
    print("=" * 60)
    print(f"✅ Unidecode installed: {'Yes' if unidecode_ok else 'No'}")
    print(f"✅ Upload simulation: {'Success' if upload_ok else 'Failed'}")
    print(f"✅ Error handling: ASCII-safe")
    print(f"✅ Multiple filenames: Tested")
    
    if unidecode_ok and upload_ok:
        print(f"\n🚀 Ready for deployment!")
        print(f"Your staging server should now handle:")
        print(f"  【JRタワー】202505月度_月次報告書-v0.2 (1).xlsx")
        print(f"  Without any ASCII encoding errors!")
    else:
        print(f"\n⚠️ Issues found:")
        if not unidecode_ok:
            print(f"  - Install unidecode: pip install unidecode")
        if not upload_ok:
            print(f"  - Check the filename conversion logic")

if __name__ == "__main__":
    main()
