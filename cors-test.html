<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>CORS Test Tool</h1>
    <p>This page tests CORS functionality by making requests to your API.</p>

    <div>
        <h2>API URL</h2>
        <input type="text" id="apiUrl" value="http://localhost:8000/api/cors-test" style="width: 100%; padding: 8px;">
    </div>

    <div>
        <h2>Test Requests</h2>
        <button onclick="testGet()">Test GET</button>
        <button onclick="testOptions()">Test OPTIONS</button>
        <button onclick="testPost()">Test POST</button>
        <button onclick="testLogin()">Test Login</button>
    </div>

    <div>
        <h2>Results</h2>
        <pre id="results">Results will appear here...</pre>
    </div>

    <script>
        function displayResult(data, isError = false) {
            const resultsElement = document.getElementById('results');
            if (isError) {
                resultsElement.innerHTML = `<span class="error">ERROR: ${JSON.stringify(data, null, 2)}</span>`;
            } else {
                resultsElement.innerHTML = `<span class="success">SUCCESS:</span>\n${JSON.stringify(data, null, 2)}`;
            }
        }

        function getApiUrl() {
            return document.getElementById('apiUrl').value;
        }

        async function testGet() {
            try {
                const response = await fetch(getApiUrl(), {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();
                displayResult(data);
            } catch (error) {
                displayResult(error.toString(), true);
            }
        }

        async function testOptions() {
            try {
                const response = await fetch(getApiUrl(), {
                    method: 'OPTIONS'
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                // Get all headers
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                });

                displayResult({
                    status: response.status,
                    statusText: response.statusText,
                    headers: headers
                });
            } catch (error) {
                displayResult(error.toString(), true);
            }
        }

        async function testPost() {
            try {
                const response = await fetch(getApiUrl(), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        test: 'data'
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const data = await response.json();
                displayResult(data);
            } catch (error) {
                displayResult(error.toString(), true);
            }
        }

        async function testLogin() {
            try {
                const loginUrl = getApiUrl().replace('/cors-test', '/login');
                console.log('Attempting login to:', loginUrl);

                const response = await fetch(loginUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'admin123'
                    })
                });

                console.log('Login response status:', response.status);

                // Get all headers
                const headers = {};
                response.headers.forEach((value, key) => {
                    headers[key] = value;
                    console.log(`Response header: ${key} = ${value}`);
                });

                if (!response.ok) {
                    let errorData;
                    try {
                        errorData = await response.json();
                    } catch (e) {
                        errorData = { error: 'Could not parse error response' };
                    }
                    throw new Error(JSON.stringify(errorData));
                }

                const data = await response.json();
                displayResult(data);
            } catch (error) {
                console.error('Login error:', error);
                displayResult(error.toString(), true);
            }
        }
    </script>
</body>
</html>
