#!/bin/bash

LOGFILE="dependency-update.log"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOGFILE"
}

if ! command -v jq &> /dev/null; then
    log "ERROR: 'jq' is not installed. Please install jq to use this script."
    exit 1
fi

log "Checking and updating requirements.txt..."

tmp_file=$(mktemp)
while IFS= read -r line; do
    [[ -z "$line" || "$line" == \#* ]] && echo "$line" >> "$tmp_file" && continue
    pkg=$(echo "$line" | cut -d'=' -f1 | tr -d ' ')
    latest=$(curl -s "https://pypi.org/pypi/$pkg/json" | jq -r '.info.version')
    if [[ "$latest" == "null" || -z "$latest" ]]; then
        echo "$line" >> "$tmp_file"
        log "Warning: Could not fetch version for $pkg"
    else
        echo "$pkg==$latest" >> "$tmp_file"
        log "Updated $pkg to $latest"
    fi
done < requirements.txt

mv "$tmp_file" requirements.txt
log "requirements.txt updated. You may now run: pip install -r requirements.txt"
