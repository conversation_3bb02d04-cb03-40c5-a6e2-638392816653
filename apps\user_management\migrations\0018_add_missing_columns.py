from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0017_add_last_login_column'),
    ]

    operations = [
        migrations.RunSQL(
            sql="""
            -- Check if date_joined column exists, add if it doesn't
            SET @column_exists = (
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = 'user_management_user'
                AND column_name = 'date_joined'
                AND table_schema = DATABASE()
            );
            
            SET @sql = IF(@column_exists = 0, 
                'ALTER TABLE user_management_user ADD COLUMN date_joined DATETIME NOT NULL DEFAULT NOW()',
                'SELECT "Column date_joined already exists"');
            
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Check if is_staff column exists, add if it doesn't
            SET @column_exists = (
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = 'user_management_user'
                AND column_name = 'is_staff'
                AND table_schema = DATABASE()
            );
            
            SET @sql = IF(@column_exists = 0, 
                'ALTER TABLE user_management_user ADD COLUMN is_staff BOOLEAN NOT NULL DEFAULT FALSE',
                'SELECT "Column is_staff already exists"');
            
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            
            -- Check if is_superuser column exists, add if it doesn't
            SET @column_exists = (
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = 'user_management_user'
                AND column_name = 'is_superuser'
                AND table_schema = DATABASE()
            );
            
            SET @sql = IF(@column_exists = 0, 
                'ALTER TABLE user_management_user ADD COLUMN is_superuser BOOLEAN NOT NULL DEFAULT FALSE',
                'SELECT "Column is_superuser already exists"');
            
            PREPARE stmt FROM @sql;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
            """,
            reverse_sql="""
            -- No reverse operation needed
            """
        ),
    ]
