from django.core.mail import send_mail
from django.conf import settings

def send_email(subject, message, recipient_list, fail_silently=False):
    """
    Utility function to send emails using the configured SMTP backend.
    """
    from_email = "<EMAIL>"
    send_mail(
        subject=subject,
        message=message,
        from_email=from_email,  # Global sender email
        recipient_list=recipient_list,
        fail_silently=fail_silently,
    )
