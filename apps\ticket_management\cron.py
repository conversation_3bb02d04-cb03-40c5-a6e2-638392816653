# cron.py
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ticketing_system.settings')

django.setup()

from datetime import timedelta
from django.utils.timezone import now, make_aware
from django.db.models import OuterRef
from apps.ticket_management.models import (
    Ticket, Status, StatusTracking, FeedbackValue, Feedback
)

def update_solved_tickets_to_closed():
    """
    Updates tickets from 'solved' to 'closed' if they have been
    in 'solved' status for more than 72 hours.
    ...
    """
    solved_status = Status.objects.filter(name='solved').first()
    closed_status = Status.objects.filter(name='closed').first()
    if not solved_status or not closed_status:
        return "Error: 'solved' or 'closed' status not found in the database."

    threshold_time = now() - timedelta(minutes=5)
    if threshold_time.tzinfo is None:
        threshold_time = make_aware(threshold_time)

    tickets_to_update = Ticket.objects.filter(
        status=solved_status,
        ticket_id__in=StatusTracking.objects.filter(
            current_status=solved_status,
            updated_at__lte=threshold_time
        ).values('ticket_id')
    )

    feedback_value_5 = FeedbackValue.objects.filter(value_id=5).first()
    if not feedback_value_5:
        return "Error: No FeedbackValue found with value_id=5."

    updated_count = 0

    for ticket in tickets_to_update:
        ticket.status = closed_status
        ticket.closed_at = now()
        ticket.save()

        StatusTracking.objects.create(
            ticket=ticket,
            current_status=closed_status,
            updated_by=ticket.created_by,
            created_by=ticket.created_by,
            created_at=now(),
            updated_at=now(),
        )

        Feedback.objects.create(
            ticket=ticket,
            user=ticket.created_by,
            feedback_value=feedback_value_5,
            reason="Auto Closed the Ticket",
        )

        updated_count += 1

    return f"Updated {updated_count} tickets from 'solved' to 'closed'."

if __name__ == "__main__":
    result = update_solved_tickets_to_closed()
