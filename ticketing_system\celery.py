import os
from celery import Celery
from celery.schedules import crontab

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ticketing_system.settings')

app = Celery('ticketing_system')

app.conf.beat_schedule = {
    'update_solved_tickets_to_closed': {
        'task': 'apps.ticket_management.tasks.update_solved_tickets_to_closed',
        'schedule': crontab(minute='*/5'),  
    },
}

app.conf.timezone = 'UTC'

# Load task modules from all registered Django app configs.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Auto-discover tasks from installed Django apps
app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')
