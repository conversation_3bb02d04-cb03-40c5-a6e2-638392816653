"""
Swagger documentation decorators and schemas for Authentication API endpoints.
This file contains comprehensive API documentation for authentication-related endpoints.
"""

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

# 🔐 Login API Documentation
login_swagger_docs = swagger_auto_schema(
    operation_summary="🔐 User Login",
    operation_description="""
    Authenticate user with email and password to get JWT tokens.
    
    **Features:**
    - JWT token authentication
    - FCM token registration for push notifications
    - User profile information in response
    - Refresh token for token renewal
    
    **Response includes:**
    - Access token (expires in 2 days)
    - Refresh token (expires in 1 day)
    - Complete user profile data
    - Profile picture URL (if available)
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['email', 'password'],
        properties={
            'email': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                description='User email address',
                example='<EMAIL>'
            ),
            'password': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_PASSWORD,
                description='User password',
                example='SecurePassword123!'
            ),
            'fcm_token': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Firebase Cloud Messaging token for push notifications (optional)',
                example='dGhpcyBpcyBhIGZha2UgZmNtIHRva2Vu...'
            ),
        }
    ),
    responses={
        200: openapi.Response(
            description="Login successful",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'refresh': openapi.Schema(type=openapi.TYPE_STRING, description='JWT refresh token'),
                    'access': openapi.Schema(type=openapi.TYPE_STRING, description='JWT access token'),
                    'user': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        description='User profile information',
                        properties={
                            'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                            'email': openapi.Schema(type=openapi.TYPE_STRING),
                            'first_name': openapi.Schema(type=openapi.TYPE_STRING),
                            'last_name': openapi.Schema(type=openapi.TYPE_STRING),
                            'role_id': openapi.Schema(type=openapi.TYPE_STRING),
                            'profile_pic': openapi.Schema(type=openapi.TYPE_STRING, description='Profile picture URL'),
                        }
                    ),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Login Successful'),
                }
            )
        ),
        401: openapi.Response(
            description="Authentication failed",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        enum=['Email Not Exists', 'Password is Incorrect', 'User account is not active']
                    )
                }
            )
        ),
        404: openapi.Response(description="User not found"),
    },
    tags=['🔐 Authentication']
)

# 🔄 Token Refresh Documentation
token_refresh_swagger_docs = swagger_auto_schema(
    operation_summary="🔄 Refresh JWT Token",
    operation_description="""
    Refresh an expired access token using a valid refresh token.
    
    **Usage:**
    - Send refresh token to get new access token
    - Refresh tokens are rotated for security
    - Access tokens expire in 2 days
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['refresh'],
        properties={
            'refresh': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Valid JWT refresh token',
                example='eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...'
            ),
        }
    ),
    responses={
        200: openapi.Response(
            description="Token refreshed successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'access': openapi.Schema(type=openapi.TYPE_STRING, description='New JWT access token'),
                    'refresh': openapi.Schema(type=openapi.TYPE_STRING, description='New JWT refresh token (if rotation enabled)'),
                }
            )
        ),
        401: openapi.Response(description="Invalid or expired refresh token"),
    },
    tags=['🔐 Authentication']
)

# 🔒 Change Password Documentation
change_password_swagger_docs = swagger_auto_schema(
    operation_summary="🔒 Change Password",
    operation_description="""
    Change user password with current password verification.
    
    **Security Features:**
    - Current password verification required
    - Password strength validation
    - Secure password hashing
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['current_password', 'new_password'],
        properties={
            'current_password': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_PASSWORD,
                description='Current user password'
            ),
            'new_password': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_PASSWORD,
                description='New password (must meet security requirements)'
            ),
        }
    ),
    responses={
        200: openapi.Response(description="Password changed successfully"),
        400: openapi.Response(description="Invalid current password or weak new password"),
        401: openapi.Response(description="Authentication required"),
    },
    tags=['🔐 Authentication']
)

# 📧 Password Reset Request Documentation
password_reset_request_swagger_docs = swagger_auto_schema(
    operation_summary="📧 Request Password Reset",
    operation_description="""
    Request password reset email for forgotten password.
    
    **Process:**
    1. User provides email address
    2. System sends reset email with secure token
    3. User clicks link in email to reset password
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['email'],
        properties={
            'email': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                description='User email address',
                example='<EMAIL>'
            ),
        }
    ),
    responses={
        200: openapi.Response(description="Password reset email sent"),
        404: openapi.Response(description="Email address not found"),
    },
    tags=['🔐 Authentication']
)

# 🔔 FCM Token Documentation
fcm_token_swagger_docs = swagger_auto_schema(
    operation_summary="🔔 Save FCM Token",
    operation_description="""
    Save Firebase Cloud Messaging token for push notifications.
    
    **Purpose:**
    - Enable push notifications for ticket updates
    - Support mobile app notifications
    - Real-time communication
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['fcm_token'],
        properties={
            'fcm_token': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Firebase Cloud Messaging token',
                example='dGhpcyBpcyBhIGZha2UgZmNtIHRva2Vu...'
            ),
        }
    ),
    responses={
        200: openapi.Response(description="FCM token saved successfully"),
        400: openapi.Response(description="FCM token is required"),
    },
    tags=['🔔 Notifications']
)

# 🏢 Module Management Documentation
module_list_swagger_docs = swagger_auto_schema(
    operation_summary="🏢 List All Modules",
    operation_description="""
    Get list of all system modules for role-based access control.
    
    **Modules include:**
    - Ticket Management
    - User Management
    - Reports & Analytics
    - System Configuration
    """,
    responses={
        200: openapi.Response(
            description="Modules retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(type=openapi.TYPE_STRING),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_STRING),
                                'module_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'description': openapi.Schema(type=openapi.TYPE_STRING),
                                'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                            }
                        )
                    )
                }
            )
        ),
    },
    tags=['🏢 System Management']
)
