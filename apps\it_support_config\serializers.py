from rest_framework import serializers
from .models import Category, SubCategory


class CategorySerializer(serializers.ModelSerializer):
    cat_name = serializers.CharField()  # Use `cat_name` as per the model
    created_by_fullname = serializers.SerializerMethodField()
    updated_by_fullname = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = [
            'id', 
            'cat_name', 
            'is_active', 
            'created_at', 
            'updated_at', 
            'created_by_fullname', 
            'updated_by_fullname'
        ]

    def get_created_by_fullname(self, obj):
        created_by = obj.created_by
        if created_by:
            return f"{created_by.first_name} {created_by.last_name}"
        return None

    def get_updated_by_fullname(self, obj):
        updated_by = obj.updated_by
        if updated_by:
            return f"{updated_by.first_name} {updated_by.last_name}"
        return None


class SubCategorySerializer(serializers.ModelSerializer):
    subcat_name = serializers.Char<PERSON>ield()  # Use `subcat_name` as per the model
    category_name = serializers.Char<PERSON>ield(source='category.cat_name', read_only=True)
    created_by_fullname = serializers.SerializerMethodField()
    updated_by_fullname = serializers.SerializerMethodField()

    class Meta:
        model = SubCategory
        fields = [
            'id', 
            'subcat_name', 
            'category', 
            'category_name', 
            'is_active', 
            'created_at', 
            'updated_at', 
            'created_by_fullname', 
            'updated_by_fullname'
        ]

    def get_created_by_fullname(self, obj):
        created_by = obj.created_by
        if created_by:
            return f"{created_by.first_name} {created_by.last_name}"
        return None

    def get_updated_by_fullname(self, obj):
        updated_by = obj.updated_by
        if updated_by:
            return f"{updated_by.first_name} {updated_by.last_name}"
        return None

#