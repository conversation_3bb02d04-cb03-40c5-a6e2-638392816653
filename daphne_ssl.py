from twisted.internet import ssl, reactor
from daphne.server import Server
from ticketing_system.asgi import application

ssl_context = ssl.DefaultOpenSSLContextFactory(
    '/etc/ssl/Ticketing-SSL/privkey.pem',     # Private key
    '/etc/ssl/Ticketing-SSL/fullchain.pem'    # Certificate chain
)

server = Server(
    application=application,
    endpoints=['ssl:port=443:interface=0.0.0.0'],
    signal_handlers=False,
)

server.run()
