"""
Swagger documentation decorators and schemas for User Management API endpoints.
This file contains comprehensive API documentation for user-related endpoints.
"""

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

# 👥 User List/Create Documentation
user_list_swagger_docs = swagger_auto_schema(
    operation_summary="👥 List/Create Users",
    operation_description="""
    **GET**: Retrieve list of users with filtering and pagination
    **POST**: Create a new user account
    
    **Filtering Options:**
    - Role (Admin, Manager, Technician, User)
    - Location/Department
    - Active status
    - Search by name or email
    
    **Features:**
    - Automatic password generation
    - Welcome email with credentials
    - Role-based access control
    - Profile picture support
    """,
    manual_parameters=[
        openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, default=1),
        openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, default=10),
        openapi.Parameter('search', openapi.IN_QUERY, description="Search by name or email", type=openapi.TYPE_STRING),
        openapi.Parameter('role_id', openapi.IN_QUERY, description="Filter by role ID", type=openapi.TYPE_STRING),
        openapi.Parameter('location_id', openapi.IN_QUERY, description="Filter by location ID", type=openapi.TYPE_STRING),
        openapi.Parameter('is_active', openapi.IN_QUERY, description="Filter by active status", type=openapi.TYPE_BOOLEAN),
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['first_name', 'last_name', 'email', 'role_id', 'location_id'],
        properties={
            'employee_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Unique employee identifier',
                example='EMP001'
            ),
            'first_name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User first name',
                example='John'
            ),
            'last_name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User last name',
                example='Doe'
            ),
            'email': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                description='User email address (used for login)',
                example='<EMAIL>'
            ),
            'phone_number': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='User phone number',
                example='+1234567890'
            ),
            'role_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Role ID (R001=Super Admin, R002=Admin, R003=Manager, R004=Project Manager, R005=Employee, R006=Support Agent)',
                example='R005'
            ),
            'location_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Location/Department ID',
                example='LOC001'
            ),
            'profile_pic': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_BINARY,
                description='Profile picture file (optional)'
            ),
        }
    ),
    responses={
        200: openapi.Response(
            description="Users retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(type=openapi.TYPE_STRING),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'employee_id': openapi.Schema(type=openapi.TYPE_STRING),
                                'first_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'last_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'email': openapi.Schema(type=openapi.TYPE_STRING),
                                'role_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'location_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                'profile_pic': openapi.Schema(type=openapi.TYPE_STRING),
                                'phone_number': openapi.Schema(type=openapi.TYPE_STRING),
                            }
                        )
                    )
                }
            )
        ),
        201: openapi.Response(
            description="User created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(type=openapi.TYPE_STRING),
                    'data': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'generated_password': openapi.Schema(type=openapi.TYPE_STRING, description='Auto-generated password'),
                }
            )
        ),
        400: openapi.Response(description="Invalid data or email already exists"),
        401: openapi.Response(description="Authentication required"),
    },
    tags=['👥 User Management']
)

# 👤 User Detail Documentation
user_detail_swagger_docs = swagger_auto_schema(
    operation_summary="👤 User Details",
    operation_description="""
    **GET**: Retrieve detailed information about a specific user
    **PUT**: Update user information
    **DELETE**: Delete/Deactivate user account
    
    **Includes:**
    - Complete user profile
    - Role and permissions
    - Location/Department info
    - Activity status
    - Profile picture
    """,
    responses={
        200: openapi.Response(
            description="User details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                    'employee_id': openapi.Schema(type=openapi.TYPE_STRING),
                    'first_name': openapi.Schema(type=openapi.TYPE_STRING),
                    'last_name': openapi.Schema(type=openapi.TYPE_STRING),
                    'email': openapi.Schema(type=openapi.TYPE_STRING),
                    'phone_number': openapi.Schema(type=openapi.TYPE_STRING),
                    'role': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'location': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    'profile_pic': openapi.Schema(type=openapi.TYPE_STRING),
                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                    'last_login': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                }
            )
        ),
        404: openapi.Response(description="User not found"),
        401: openapi.Response(description="Authentication required"),
    },
    tags=['👥 User Management']
)

# 🏢 Role Management Documentation
role_list_swagger_docs = swagger_auto_schema(
    operation_summary="🏢 List/Create Roles",
    operation_description="""
    **GET**: Retrieve list of all user roles
    **POST**: Create a new role
    
    **Default Roles:**
    - R001: Super Admin (Full system access)
    - R002: Admin (Administrative access)
    - R003: Manager (Department management)
    - R004: Project Manager (Project oversight)
    - R005: Employee (Standard user)
    - R006: Support Agent (Ticket handling)
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['role_id', 'role_name'],
        properties={
            'role_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Unique role identifier',
                example='R007'
            ),
            'role_name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Role display name',
                example='Senior Technician'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Role description',
                example='Senior level technical support specialist'
            ),
        }
    ),
    responses={
        200: openapi.Response(
            description="Roles retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'role_id': openapi.Schema(type=openapi.TYPE_STRING),
                        'role_name': openapi.Schema(type=openapi.TYPE_STRING),
                        'description': openapi.Schema(type=openapi.TYPE_STRING),
                        'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            )
        ),
        201: openapi.Response(description="Role created successfully"),
    },
    tags=['🏢 Role Management']
)

# 📍 Location Management Documentation
location_list_swagger_docs = swagger_auto_schema(
    operation_summary="📍 List/Create Locations",
    operation_description="""
    **GET**: Retrieve list of all locations/departments
    **POST**: Create a new location/department
    
    **Purpose:**
    - Organize users by department
    - Location-based reporting
    - Ticket assignment by location
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['location_id', 'location_name'],
        properties={
            'location_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Unique location identifier',
                example='LOC003'
            ),
            'location_name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Location/Department name',
                example='IT Department'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Location description',
                example='Information Technology Department - 3rd Floor'
            ),
        }
    ),
    responses={
        200: openapi.Response(
            description="Locations retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'location_id': openapi.Schema(type=openapi.TYPE_STRING),
                        'location_name': openapi.Schema(type=openapi.TYPE_STRING),
                        'description': openapi.Schema(type=openapi.TYPE_STRING),
                        'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            )
        ),
        201: openapi.Response(description="Location created successfully"),
    },
    tags=['📍 Location Management']
)

# 📊 Project Management Documentation
project_list_swagger_docs = swagger_auto_schema(
    operation_summary="📊 List/Create Projects",
    operation_description="""
    **GET**: Retrieve list of projects
    **POST**: Create a new project
    
    **Features:**
    - Project-based ticket organization
    - User-project assignments
    - Project-specific reporting
    """,
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['project_name'],
        properties={
            'project_name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Project name',
                example='Website Redesign Project'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Project description',
                example='Complete redesign of company website with new features'
            ),
            'assigned_users': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(type=openapi.TYPE_INTEGER),
                description='List of user IDs assigned to project'
            ),
        }
    ),
    responses={
        200: openapi.Response(description="Projects retrieved successfully"),
        201: openapi.Response(description="Project created successfully"),
    },
    tags=['📊 Project Management']
)
