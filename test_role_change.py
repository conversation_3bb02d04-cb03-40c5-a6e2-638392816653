#!/usr/bin/env python
"""
Test script to debug role change functionality
Run this script to test the force logout mechanism
"""

import os
import sys
import django
from django.conf import settings

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ticketing_system.settings')
django.setup()

from apps.user_management.models import User, Role
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

def test_role_change():
    """Test the role change functionality"""
    
    print("🧪 Testing Role Change Functionality")
    print("=" * 50)
    
    # Get all users and roles
    users = User.objects.filter(is_deleted=False, is_active=True)[:5]  # Get first 5 active users
    roles = Role.objects.all()
    
    print(f"📊 Found {users.count()} active users")
    print(f"📊 Found {roles.count()} roles")
    
    if not users.exists():
        print("❌ No active users found!")
        return
    
    if roles.count() < 2:
        print("❌ Need at least 2 roles to test role change!")
        return
    
    # Display available users
    print("\n👥 Available Users:")
    for i, user in enumerate(users, 1):
        print(f"  {i}. {user.first_name} {user.last_name} (ID: {user.id}) - Current Role: {user.role.role_name}")
    
    # Display available roles
    print("\n🎭 Available Roles:")
    for i, role in enumerate(roles, 1):
        print(f"  {i}. {role.role_name} (ID: {role.role_id})")
    
    # Test WebSocket channel layer
    print("\n🔌 Testing WebSocket Channel Layer...")
    channel_layer = get_channel_layer()
    if channel_layer:
        print("✅ Channel layer is available")
    else:
        print("❌ Channel layer is not available!")
        return
    
    # Test sending a force logout message
    test_user = users.first()
    print(f"\n🧪 Testing force logout for user: {test_user.first_name} {test_user.last_name} (ID: {test_user.id})")
    
    group_name = f"user_{test_user.id}"
    print(f"📡 Sending to group: {group_name}")
    
    try:
        async_to_sync(channel_layer.group_send)(
            group_name,
            {"type": "force_logout", "message": "Test force logout"}
        )
        print("✅ Force logout message sent successfully!")
        print("💡 Note: The user needs to be connected to the WebSocket to receive this message.")
    except Exception as e:
        print(f"❌ Error sending force logout: {e}")
    
    print("\n📝 Instructions for testing:")
    print("1. Make sure your frontend is connected to the WebSocket:")
    print(f"   ws://localhost:8080/ws/force-logout/{test_user.id}/")
    print("2. Change the user's role through the API")
    print("3. Check if the frontend receives the logout message")
    
    print("\n🔧 Debugging Tips:")
    print("- Check the Django server logs for WebSocket connection messages")
    print("- Verify that the frontend is properly handling WebSocket messages")
    print("- Ensure the role change API is being called with the correct parameters")

def test_role_comparison():
    """Test role comparison logic"""
    print("\n🧪 Testing Role Comparison Logic")
    print("=" * 50)
    
    # Test different data types
    test_cases = [
        ("R001", "R001", False),  # Same string
        ("R001", "R002", True),   # Different string
        ("R001", 1, True),        # String vs int
        ("R001", None, True),     # String vs None
        (None, None, False),      # Both None
    ]
    
    for old_role, new_role, expected_change in test_cases:
        new_role_str = str(new_role) if new_role else None
        actual_change = old_role != new_role_str
        
        status = "✅" if actual_change == expected_change else "❌"
        print(f"{status} Old: {old_role} ({type(old_role).__name__}), New: {new_role_str} ({type(new_role_str).__name__}), Changed: {actual_change}")

if __name__ == "__main__":
    test_role_change()
    test_role_comparison()
