[{"model": "authentication.submodule", "pk": 1, "fields": {"module": 2, "sub_module_name": "All Ticket", "created_at": "2025-04-09T13:13:23.634Z", "updated_at": "2025-04-09T13:13:23.634Z"}}, {"model": "authentication.submodule", "pk": 2, "fields": {"module": 2, "sub_module_name": "Create Ticket", "created_at": "2025-04-09T13:13:23.636Z", "updated_at": "2025-04-09T13:13:23.636Z"}}, {"model": "authentication.submodule", "pk": 3, "fields": {"module": 2, "sub_module_name": "Pending Approval", "created_at": "2025-04-09T13:13:23.636Z", "updated_at": "2025-04-09T13:13:23.636Z"}}, {"model": "authentication.submodule", "pk": 4, "fields": {"module": 2, "sub_module_name": "Assign <PERSON>", "created_at": "2025-04-09T13:13:23.637Z", "updated_at": "2025-04-09T13:13:23.637Z"}}, {"model": "authentication.submodule", "pk": 5, "fields": {"module": 3, "sub_module_name": "Manage Users", "created_at": "2025-04-09T13:13:23.638Z", "updated_at": "2025-04-09T13:13:23.638Z"}}, {"model": "authentication.submodule", "pk": 6, "fields": {"module": 3, "sub_module_name": "Add New User", "created_at": "2025-04-09T13:13:23.638Z", "updated_at": "2025-04-09T13:13:23.638Z"}}, {"model": "authentication.submodule", "pk": 7, "fields": {"module": 3, "sub_module_name": "Role Assignment", "created_at": "2025-04-09T13:13:23.639Z", "updated_at": "2025-04-09T13:13:23.639Z"}}, {"model": "authentication.submodule", "pk": 8, "fields": {"module": 3, "sub_module_name": "Project Mapping", "created_at": "2025-04-09T13:13:23.640Z", "updated_at": "2025-04-09T13:13:23.640Z"}}, {"model": "authentication.submodule", "pk": 9, "fields": {"module": 4, "sub_module_name": "Tickets & Reports", "created_at": "2025-04-09T13:13:23.641Z", "updated_at": "2025-04-09T13:13:23.641Z"}}, {"model": "authentication.submodule", "pk": 10, "fields": {"module": 4, "sub_module_name": "Technician Performance", "created_at": "2025-04-09T13:13:23.641Z", "updated_at": "2025-04-09T13:13:23.641Z"}}, {"model": "authentication.submodule", "pk": 11, "fields": {"module": 4, "sub_module_name": "User <PERSON>", "created_at": "2025-04-09T13:13:23.642Z", "updated_at": "2025-04-09T13:13:23.642Z"}}, {"model": "authentication.submodule", "pk": 12, "fields": {"module": 5, "sub_module_name": "Ticket Categories", "created_at": "2025-04-09T13:13:23.643Z", "updated_at": "2025-04-09T13:13:23.643Z"}}]