#!/bin/bash

# Deployment Script (deploy.sh)
# Handles venv setup, migrations, server start, and Apache restart for staging and production

set -e  # Exit on any error

echo "=========================================="
echo "Deployment Script"
echo "=========================================="

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if service is running
check_service() {
    local port=$1
    if curl -f http://localhost:$port/ > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

log "Starting deployment process..."

# Step 1: Run environment setup first
log "Step 1: Setting up environment configuration..."
if [ -f "./setup-env.sh" ]; then
    ./setup-env.sh
    log "✅ Environment setup completed"
else
    log "❌ setup-env.sh not found! Please ensure the file exists."
    exit 1
fi

# Step 2: Read environment variables from .env file
log "Step 2: Reading environment configuration..."
if [ -f ".env" ]; then
    source .env
    log "✅ Environment variables loaded"
    log "Environment: $ENVIRONMENT"
    log "Port: $SERVER_PORT"
    log "Database: $DATABASE_NAME@$DATABASE_HOST"
else
    log "❌ .env file not found! Please run setup-env.sh first."
    exit 1
fi

# Step 3: Set deployment paths (SIMPLIFIED)
log "Step 3: Setting deployment paths..."
PROJECT_DIR="$(pwd)"
VENV_DIR="$PROJECT_DIR/venv"
LOG_FILE="/tmp/django_${ENVIRONMENT}.log"
log "✅ Using current directory: $(pwd)"
log "✅ Environment: $ENVIRONMENT"

# Step 4: Create required directories (SAFE - never deletes existing content)
log "Step 4: Creating required directories..."
mkdir -p media staticfiles logs

# CRITICAL: Create essential media subdirectories for application functionality
log "Creating essential media subdirectories..."
mkdir -p media/chat_uploads
mkdir -p media/files
mkdir -p media/uploads/profilepic

# SAFETY: Ensure media directory and subdirectories are never accidentally cleared
if [ -d "media" ]; then
    log "✅ Media directory exists and preserved (no files deleted)"

    # Verify essential subdirectories exist
    if [ -d "media/chat_uploads" ]; then
        log "✅ chat_uploads directory preserved"
    else
        mkdir -p media/chat_uploads
        log "✅ chat_uploads directory created"
    fi

    if [ -d "media/files" ]; then
        log "✅ files directory preserved"
    else
        mkdir -p media/files
        log "✅ files directory created"
    fi

    if [ -d "media/uploads/profilepic" ]; then
        log "✅ uploads/profilepic directory preserved"
    else
        mkdir -p media/uploads/profilepic
        log "✅ uploads/profilepic directory created"
    fi
else
    log "✅ Media directory and subdirectories created"
fi

log "✅ All directories created safely with essential media subdirectories"

# Step 5: Setup virtual environment (OPTIMIZED)
log "Step 5: Setting up virtual environment..."
if [[ "$ENVIRONMENT" == "local" ]]; then
    # For local development, skip venv setup and use system Python
    log "Using system Python for local development..."
    log "✅ System Python environment ready"
else
    # For staging/production, use existing venv if available
    if [ -d "$VENV_DIR" ] && [ -f "$VENV_DIR/bin/activate" ]; then
        log "⚡ Using existing virtual environment (performance optimization)"
        source "$VENV_DIR/bin/activate"

        # Quick health check of venv
        if python -c "import django" 2>/dev/null; then
            log "✅ Virtual environment is healthy and ready"
        else
            log "⚠️  Virtual environment needs refresh, recreating..."
            rm -rf "$VENV_DIR"
            python3 -m venv "$VENV_DIR"
            source "$VENV_DIR/bin/activate"
            log "✅ Fresh virtual environment created"
        fi
    else
        log "Creating new virtual environment for $ENVIRONMENT..."
        python3 -m venv "$VENV_DIR"
        source "$VENV_DIR/bin/activate"
        log "✅ New virtual environment created and activated"
    fi
fi

# Step 6: Install dependencies
log "Step 6: Installing dependencies..."

# Upgrade pip
if pip install --upgrade pip > /dev/null 2>&1; then
    log "✅ Pip upgraded"
else
    log "⚠️  Pip upgrade failed, continuing..."
fi

# Function to check if key packages are installed
check_key_packages() {
    local packages=("django" "djangorestframework" "drf_yasg" "PyMySQL" "python_dotenv")
    local missing=()

    for package in "${packages[@]}"; do
        if ! python -c "import ${package//-/_}" 2>/dev/null; then
            missing+=("$package")
        fi
    done

    # For local environment, skip mysqlclient check (use PyMySQL instead)
    if [[ "$ENVIRONMENT" == "local" ]]; then
        # Remove mysqlclient from requirements check for local
        if ! python -c "import MySQLdb" 2>/dev/null && ! python -c "import PyMySQL" 2>/dev/null; then
            missing+=("PyMySQL")
        fi
    fi

    if [ ${#missing[@]} -eq 0 ]; then
        return 0  # All packages installed
    else
        log "Missing packages: ${missing[*]}"
        return 1  # Some packages missing
    fi
}

# OPTIMIZED: Smart package installation
if check_key_packages; then
    log "⚡ All key packages already installed, skipping requirements installation (performance optimization)"

    # Only check for missing additional packages
    missing_additional=()
    for pkg in "python-decouple" "python-dotenv" "PyMySQL"; do
        if ! python -c "import ${pkg//-/_}" 2>/dev/null; then
            missing_additional+=("$pkg")
        fi
    done

    if [ ${#missing_additional[@]} -gt 0 ]; then
        log "Installing missing additional packages: ${missing_additional[*]}"
        pip install "${missing_additional[@]}" > /dev/null 2>&1 || true
    fi
    log "✅ Dependencies verified and optimized"
else
    # Install requirements only when packages are missing
    if [ -f "requirements.txt" ]; then
        log "Installing missing requirements..."

        if [[ "$ENVIRONMENT" == "local" ]]; then
            # For local development, install key packages individually to avoid mysqlclient issues
            log "Installing key packages individually for local development..."
            pip install django djangorestframework drf-yasg PyMySQL python-dotenv python-decouple --quiet
            log "✅ Key packages installed for local development"
        else
            # For staging/production, install all requirements efficiently
            log "Installing requirements with pip cache optimization..."
            if pip install -r requirements.txt --quiet; then
                log "✅ Requirements installed"
            else
                log "❌ Failed to install requirements"
                exit 1
            fi

            # Install additional packages only if missing
            missing_additional=()
            for pkg in "python-decouple" "python-dotenv" "PyMySQL"; do
                if ! python -c "import ${pkg//-/_}" 2>/dev/null; then
                    missing_additional+=("$pkg")
                fi
            done

            if [ ${#missing_additional[@]} -gt 0 ]; then
                log "Installing missing additional packages: ${missing_additional[*]}"
                if pip install "${missing_additional[@]}" --quiet; then
                    log "✅ Additional packages installed"
                else
                    log "❌ Failed to install additional packages"
                    exit 1
                fi
            else
                log "✅ All additional packages already installed"
            fi
        fi
    else
        log "❌ requirements.txt not found!"
        exit 1
    fi
fi

# Step 7: Database migrations
log "Step 7: Running database migrations..."
python manage.py migrate --noinput
log "✅ Database migrations completed"

# Step 8: OPTIMIZED Static files collection
log "Step 8: Collecting static files..."

# OPTIMIZED: Check if static files need updating
STATIC_FILES_HASH=""
if [ -f "staticfiles/.deployment_hash" ]; then
    STATIC_FILES_HASH=$(cat staticfiles/.deployment_hash 2>/dev/null || echo "")
fi

# Create hash of key files that affect static files
CURRENT_HASH=$(find . -name "*.py" -o -name "requirements.txt" -o -name "*.css" -o -name "*.js" | grep -E "(settings|static)" | sort | xargs md5sum 2>/dev/null | md5sum | cut -d' ' -f1 2>/dev/null || echo "new")

if [ "$STATIC_FILES_HASH" = "$CURRENT_HASH" ] && [ -d "staticfiles/drf-yasg" ]; then
    log "⚡ Static files are up-to-date, skipping collection (performance optimization)"
else
    log "Static files need updating, collecting..."

    # Handle permission issues gracefully
    if [ -d "staticfiles" ]; then
        chmod -R 777 staticfiles/ 2>/dev/null || true
        if ! rm -rf staticfiles/ 2>/dev/null; then
            log "⚠️  Permission issue, moving old staticfiles..."
            mv staticfiles "staticfiles.backup.$(date +%s)" 2>/dev/null || true
        fi
    fi

    # Ensure staticfiles directory exists
    mkdir -p staticfiles/

    # Collect static files efficiently
    python manage.py collectstatic --noinput --verbosity=1

    # Save hash for next deployment
    echo "$CURRENT_HASH" > staticfiles/.deployment_hash 2>/dev/null || true
    log "✅ Static files collected and cached"
fi

# Verify static files were collected
if [ -d "staticfiles/drf-yasg" ]; then
    log "✅ Static files collected successfully (including drf-yasg)"
else
    log "⚠️  drf-yasg static files not found, but continuing deployment"
fi

# For staging/production: Ensure static files are accessible via Apache
if [[ "$ENVIRONMENT" != "local" ]]; then
    log "Setting up static files for Apache serving..."

    # Set proper permissions for Apache
    chmod -R 777 staticfiles/ 2>/dev/null || true

    # Create symbolic link or copy to Apache document root if needed
    # This ensures Apache can serve the static files
    if [ -d "/var/www/html" ]; then
        log "Creating static files link for Apache..."
        ln -sf "$(pwd)/staticfiles" "/var/www/html/static" 2>/dev/null || true
    fi

    log "✅ Static files configured for Apache serving"
fi

# Step 9: Django service management (SIMPLIFIED)
log "Step 9: Managing Django service..."

# Stop any existing Django processes on this port
log "Stopping existing Django processes..."
pkill -f "manage.py runserver.*:$SERVER_PORT" || true
lsof -ti:$SERVER_PORT | xargs kill -9 2>/dev/null || true
sleep 2

# Step 10: Start Django application
log "Step 10: Starting Django application..."

# Prepare log file
touch "$LOG_FILE"

# Start the Django server
log "Starting $ENVIRONMENT server..."
nohup python manage.py runserver 0.0.0.0:$SERVER_PORT > "$LOG_FILE" 2>&1 &

SERVER_PID=$!
log "✅ Django server started with PID: $SERVER_PID"

# Step 11: Application startup verification
log "Step 11: Waiting for application to start..."

# Wait for application to start (simplified)
for i in {1..15}; do
    if check_service $SERVER_PORT; then
        log "✅ Application started successfully in ${i} seconds"
        break
    fi
    sleep 2
done

# Final check
if ! check_service $SERVER_PORT; then
    log "❌ Application failed to start, checking logs..."
    if [ -f "$LOG_FILE" ]; then
        log "Last 10 lines of application log:"
        tail -10 "$LOG_FILE"
    fi
    exit 1
fi

# Step 12: Apache conflict resolution
log "Step 12: Resolving Apache port conflicts..."

# Check if Apache is configured to use the same port as Django
if [[ "$ENVIRONMENT" != "local" ]]; then
    # Stop Apache if it's trying to use the same port as Django
    if systemctl is-active apache2 >/dev/null 2>&1; then
        log "⚠️  Apache is running, checking for port conflicts..."

        # Check if Apache is configured for our Django port
        if apache2ctl -S 2>/dev/null | grep -q ":$SERVER_PORT"; then
            log "⚠️  Apache is configured to use port $SERVER_PORT (same as Django)"
            log "🔧 Stopping Apache to prevent port conflict..."
            systemctl stop apache2 >/dev/null 2>&1 || true
            systemctl disable apache2 >/dev/null 2>&1 || true
            log "✅ Apache stopped to prevent port conflict with Django"
        else
            log "✅ Apache is not conflicting with Django port $SERVER_PORT"
        fi
    else
        log "✅ Apache is not running, no port conflicts"
    fi

    log "✅ Django application is running independently on port $SERVER_PORT"
else
    log "✅ Local environment, skipping Apache conflict check"
fi

# Step 13: Set file permissions
log "Step 13: Setting file permissions..."
chmod -R 777 media/ 2>/dev/null || true
chmod -R 777 staticfiles/ 2>/dev/null || true

# CRITICAL: Ensure essential media subdirectories have correct permissions
chmod -R 777 media/chat_uploads/ 2>/dev/null || true
chmod -R 777 media/files/ 2>/dev/null || true
chmod -R 777 media/uploads/ 2>/dev/null || true

chmod 644 "$LOG_FILE" 2>/dev/null || true
log "✅ File permissions set (media: 777, staticfiles: 777, subdirectories: 777)"

# Step 14: Final verification
log "Step 14: Final verification..."
if ps aux | grep -q "manage.py runserver.*:$SERVER_PORT"; then
    log "✅ Django process is running"
else
    log "❌ Django process not found"
    exit 1
fi

# Step 16: Display deployment summary
echo ""
echo "=========================================="
echo "🎉 DEPLOYMENT SUCCESSFUL!"
echo "=========================================="
echo "Environment: $ENVIRONMENT"
echo "Port: $SERVER_PORT"
echo "Database: $DATABASE_NAME@$DATABASE_HOST"
echo "Project Directory: $PROJECT_DIR"
echo "Log File: $LOG_FILE"
echo ""
echo "🌐 Application URLs:"
if [[ "$ENVIRONMENT" == "local" ]]; then
    echo "Main: http://localhost:$SERVER_PORT/"
    echo "Admin: http://localhost:$SERVER_PORT/admin/"
elif [[ "$ENVIRONMENT" == "staging" ]]; then
    echo "Main: http://internal-project.nexware-global.com:$SERVER_PORT/"
    echo "Admin: http://internal-project.nexware-global.com:$SERVER_PORT/admin/"
else # production
    echo "Main: https://ticket.nexware-global.com:$SERVER_PORT/"
    echo "Admin: https://ticket.nexware-global.com:$SERVER_PORT/admin/"
fi
echo ""
echo "📊 Useful Commands:"
echo "Check logs: tail -f $LOG_FILE"
echo "Check process: ps aux | grep 'manage.py runserver'"
echo "Stop service: pkill -f 'manage.py runserver.*:$SERVER_PORT'"
echo "=========================================="

log "Deployment completed successfully! 🚀"
