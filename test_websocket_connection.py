#!/usr/bin/env python
"""
Test script to verify WebSocket connection stability during role changes
"""

import asyncio
import websockets
import json
import threading
import time
import requests
from datetime import datetime

class WebSocketTester:
    def __init__(self, user_id, base_url="localhost:8080"):
        self.user_id = user_id
        self.base_url = base_url
        self.websocket = None
        self.connected = False
        self.messages_received = []
        
    async def connect(self):
        """Connect to the WebSocket"""
        uri = f"ws://{self.base_url}/ws/force-logout/{self.user_id}/"
        print(f"🔌 Connecting to: {uri}")
        
        try:
            self.websocket = await websockets.connect(uri)
            self.connected = True
            print(f"✅ Connected to WebSocket for user {self.user_id}")
            
            # Listen for messages
            await self.listen_for_messages()
            
        except Exception as e:
            print(f"❌ Failed to connect: {e}")
            self.connected = False
    
    async def listen_for_messages(self):
        """Listen for incoming messages"""
        try:
            async for message in self.websocket:
                timestamp = datetime.now().isoformat()
                print(f"📨 [{timestamp}] Received message: {message}")
                
                try:
                    data = json.loads(message)
                    self.messages_received.append({
                        'timestamp': timestamp,
                        'data': data
                    })
                    
                    if data.get('action') == 'logout':
                        print(f"🔴 LOGOUT MESSAGE RECEIVED!")
                        print(f"   Message: {data.get('message')}")
                        print(f"   Old Role: {data.get('old_role')}")
                        print(f"   New Role: {data.get('new_role')}")
                        
                except json.JSONDecodeError:
                    print(f"⚠️ Invalid JSON received: {message}")
                    
        except websockets.exceptions.ConnectionClosed as e:
            print(f"❌ WebSocket connection closed: {e}")
            self.connected = False
        except Exception as e:
            print(f"❌ Error listening for messages: {e}")
            self.connected = False
    
    async def disconnect(self):
        """Disconnect from WebSocket"""
        if self.websocket:
            await self.websocket.close()
            self.connected = False
            print(f"❌ Disconnected from WebSocket for user {self.user_id}")

def test_role_change_api(user_id, new_role, api_base_url="http://localhost:8080"):
    """Test the role change API"""
    url = f"{api_base_url}/api/users/update/{user_id}/"
    
    # You'll need to get a valid JWT token for this to work
    headers = {
        'Content-Type': 'application/json',
        # 'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE'  # Add your token
    }
    
    data = {
        'role': new_role,
        'updated_by': 1  # Replace with actual updater user ID
    }
    
    print(f"🔄 Changing user {user_id} role to {new_role}")
    
    try:
        response = requests.put(url, json=data, headers=headers)
        print(f"📡 API Response: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ Role change successful")
            return True
        else:
            print(f"❌ Role change failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error calling API: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 WebSocket Connection Stability Test")
    print("=" * 50)
    
    # Test configuration
    user_id = input("Enter user ID to test: ").strip()
    if not user_id:
        print("❌ User ID is required")
        return
    
    # Create WebSocket tester
    tester = WebSocketTester(user_id)
    
    # Connect to WebSocket
    print(f"\n1. Connecting to WebSocket for user {user_id}...")
    
    # Run WebSocket connection in background
    websocket_task = asyncio.create_task(tester.connect())
    
    # Wait a bit for connection to establish
    await asyncio.sleep(2)
    
    if not tester.connected:
        print("❌ Failed to establish WebSocket connection")
        return
    
    print("✅ WebSocket connected successfully")
    
    # Test sending a manual force logout message
    print(f"\n2. Testing manual force logout message...")
    
    # You can uncomment this to test manual message sending
    # if tester.websocket:
    #     test_message = {
    #         "action": "logout",
    #         "message": "Test logout message",
    #         "timestamp": datetime.now().isoformat()
    #     }
    #     await tester.websocket.send(json.dumps(test_message))
    
    # Keep connection alive for testing
    print(f"\n3. WebSocket is now listening for messages...")
    print(f"   You can now change the user's role through the admin interface or API")
    print(f"   The WebSocket will display any received messages")
    print(f"   Press Ctrl+C to stop the test")
    
    try:
        # Keep the connection alive
        while tester.connected:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print(f"\n🛑 Test interrupted by user")
    
    finally:
        print(f"\n📊 Test Results:")
        print(f"   Messages received: {len(tester.messages_received)}")
        for i, msg in enumerate(tester.messages_received, 1):
            print(f"   {i}. [{msg['timestamp']}] {msg['data']}")
        
        await tester.disconnect()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Test stopped")
    except Exception as e:
        print(f"❌ Test failed: {e}")
