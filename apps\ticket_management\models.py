from django.db import models
from django.conf import settings 
# from django.contrib.auth.models import User
from apps.user_management.models import Project, User, Location
from apps.it_support_config.models import Category, SubCategory
from apps.websocket.models import TicketChatMessage
from django.utils.timezone import now
# from .models import Status

def upload_to_ticket(instance, filename):
    return f'ticket_docs/{instance.id}/{filename}'

class Status(models.Model):
    id=models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=50, unique=True)  # e.g., 'Open', 'In Progress'
    description = models.TextField(null=True, blank=True)  # Optional field to describe the status
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)

    def __str__(self):
        return self.name

class ApprovalStatus(models.Model):
    id=models.BigAutoField(primary_key=True)
    name = models.CharField(max_length=50, unique=True) 
    description = models.TextField(null=True, blank=True)  # Optional field to describe the Approvel status
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

 

class PriorityLevel(models.Model):
    priority_name = models.CharField(max_length=50, unique=True)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)
    def __str__(self):
        return self.priority_name

class Ticket(models.Model):
   
    APPROVEL_STATUS=[
        ('pending','Pending'),
        ('accepted','Accepted'),
        ('rejected','Rejected')
    ]
    # ticket_id=models.CharField(max_length=4, unique=True,primary_key=True,blank=True)
    ticket_id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=100)
    project=models.ForeignKey(Project, on_delete=models.CASCADE, max_length=100, null=True)
    type=models.CharField(max_length=30,null=True)
    category=models.ForeignKey(Category, on_delete=models.CASCADE, max_length=30,null=True)
    subcategory=models.ForeignKey(SubCategory, on_delete=models.CASCADE ,max_length=30,null=True)
    description = models.TextField()
    priority=models.ForeignKey(PriorityLevel,null=True, on_delete=models.SET_NULL, blank=True, )
    watchers = models.JSONField(default=list)
    location=models.ForeignKey(Location,
        on_delete=models.CASCADE,null=True
    )
    created_by = models.ForeignKey(User,
        # settings.AUTH_USER_MODEL,  # Use AUTH_USER_MODEL
        on_delete=models.CASCADE,
        related_name='tickets_created',null=True
    )
    updated_by=models.ForeignKey(User,
        # settings.AUTH_USER_MODEL,  # Use AUTH_USER_MODEL
        on_delete=models.CASCADE,
        related_name='tickets_updated',null=True
    )
    assigned_to = models.ForeignKey(User,
        # settings.AUTH_USER_MODEL,  # Use AUTH_USER_MODEL
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='assigned_tickets'
    )
    due_date = models.DateTimeField(null=True, blank=True)
    due_expiry_reason=models.CharField(max_length=1000, null=True, blank=True)
    status = models.ForeignKey(Status, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    solved_at = models.DateTimeField(null=True,blank=True)
    closed_at = models.DateTimeField(null=True,blank=True)
    approvel_status = models.ForeignKey(
    ApprovalStatus, null=True, blank=True, on_delete=models.SET_NULL)
    approvel_message= models.TextField(max_length=1000,null=True)
    cancel_message= models.TextField(max_length=1000,null=True,blank=True)
    is_approved=models.BooleanField(default=False)
    approved_by=models.ForeignKey(User,
        # settings.AUTH_USER_MODEL,  # Use AUTH_USER_MODEL
        on_delete=models.CASCADE,
        related_name='tickets_approved',
        null=True, blank=True,
    )
    justification=models.TextField(null=True,blank=True)
    attachement=models.ForeignKey('ticket_management.TicketChatAttachenment',on_delete=models.SET_NULL, null=True, blank=True,related_name="attachements")
    auto_closed=models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='tickets_deleted_by', null=True, blank=True, to_field='id')

    def __str__(self):
        return f"{self.title} - {self.status}"
    
    def soft_delete(self, deleted_by=None):
        """Marks the ticket as deleted without actually removing it from the database."""
        self.is_deleted = True
        self.deleted_at = now()
        self.deleted_by = deleted_by
        self.save()
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._previous_status = self.status.name.lower() if self.status else ""

class StatusTracking(models.Model):
    ticket=models.ForeignKey(Ticket, related_name="ticket_status_tracking",on_delete=models.CASCADE)
    current_status=models.ForeignKey(Status,related_name="status_tracking",on_delete=models.SET_NULL, null=True)
    updated_by=models.ForeignKey(User,related_name="updated_user",on_delete=models.SET_NULL, null=True,)
    created_by=models.ForeignKey(User,related_name="created_user",on_delete=models.CASCADE, null=True,)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

class TicketChatAttachenment(models.Model):
    ticket = models.ForeignKey("ticket_management.Ticket", related_name="attachments", on_delete=models.CASCADE)
    attachement = models.FileField(upload_to="files/", max_length=300, null=True, blank=True)
    chat = models.ForeignKey("websocket.TicketChatMessage", on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Document for Ticket: {self.ticket.ticket_id}"
class Notification(models.Model):
    ticket = models.ForeignKey(Ticket, on_delete=models.CASCADE, related_name="notifications")
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="notifications") 
    updated_by=models.ForeignKey(User,related_name="Notification_update",on_delete=models.SET_NULL, null=True,)
    from_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="notifications_sent",null=True,)
    to_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="notifications_received",null=True,)
    message = models.TextField()
    is_read = models.BooleanField(default=False)  # To track if the notification is read
    is_clear = models.BooleanField(default=False)  # To track if the notification is cleared
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Notification for Ticket ID {self.ticket.id}"


 
class FeedbackValue(models.Model):
    value_id = models.AutoField(primary_key=True)
    emoji = models.CharField(max_length=10)  
    label = models.CharField(max_length=50)  

    def __str__(self):
        return f"{self.emoji} {self.label}"

class Feedback(models.Model):
    feedback_id = models.AutoField(primary_key=True)
    ticket = models.ForeignKey(
        Ticket,
        related_name='feedbacks',
        on_delete=models.CASCADE
    )
    user = models.ForeignKey(
        User,
        related_name='feedbacks',
        on_delete=models.CASCADE
    )
    feedback_value = models.ForeignKey(
        FeedbackValue,
        related_name='feedbacks',
        on_delete=models.CASCADE
    )
    reason = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Feedback for {self.ticket} by {self.employee} - {self.feedback_value}"

  # Assuming you use Django's User model
class ChatList(models.Model):
    ticket = models.ForeignKey(Ticket, on_delete=models.SET_NULL, related_name='chats', null=True)
    message = models.TextField(max_length=1000, null=True)
    title = models.CharField(max_length=100, null=True)
    sender = models.ForeignKey(User, related_name='sent_messages', on_delete=models.CASCADE)
    receiver = models.ForeignKey(User, related_name='received_messages', on_delete=models.CASCADE, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    chat = models.ForeignKey(TicketChatAttachenment, on_delete=models.CASCADE, null=True)
    deleted_at = models.BooleanField(default=False)

    def __str__(self):
        return f"Chat for Ticket {self.ticket.id} by {self.sender.username} to {self.receiver.username}"
