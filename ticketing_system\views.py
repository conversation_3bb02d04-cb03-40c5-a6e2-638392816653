from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
def home_view(request):
    """
    A simple view to handle the root URL.
    Returns a JSON response with basic information about the API.
    """
    return JsonResponse({
        "status": "success",
        "message": "Ticketing System API is running",
        "api_endpoints": {
            "api": "/api/",
            "admin": "/admin/",
            "tickets": "/api/tickets/",
            "users": "/api/users/",
            "categories": "/api/categories/"
        },
        "version": "1.0.0"
    })
