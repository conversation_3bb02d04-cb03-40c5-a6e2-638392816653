from django.http import JsonResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
def home_view(request):
    """
    Enhanced home view that serves both HTML and JSON responses.
    - HTML: Beautiful landing page for browser users
    - JSON: API information for programmatic access
    """
    base_url = request.build_absolute_uri('/')

    # Determine environment based on URL
    if ":9019" in base_url:
        environment = "staging"
    elif ":9049" in base_url:
        environment = "production"
    else:
        environment = "local"

    # Prepare context data
    context = {
        "status": "success",
        "message": "🎫 Nex Ticketing System API is running",
        "version": "1.0.0",
        "environment": environment,

        # 📖 API Documentation
        "documentation": {
            "swagger_ui": f"{base_url}api/docs/",
            "redoc": f"{base_url}api/redoc/",
            "schema_json": f"{base_url}api/swagger.json",
            "schema_yaml": f"{base_url}api/swagger.yaml"
        },

        # 🔐 Authentication
        "authentication": {
            "login": "/api/login",
            "token_refresh": "/api/token/refresh/",
            "logout": "/api/token/logout/",
            "password_reset": "/api/password-reset-request/"
        },

        # 📋 Main API Endpoints
        "api_endpoints": {
            "tickets": "/api/tickets/",
            "users": "/api/users/",
            "categories": "/api/categories/",
            "reports": "/api/report/",
            "chats": "/api/chats/",
            "notifications": "/api/notifications/"
        },

        # 🛠️ System Endpoints
        "system": {
            "admin": "/admin/",
            "api_root": "/api/",
            "health_check": "/api/",
            "media": "/media/",
            "static": "/static/"
        },

        # 📊 Features
        "features": [
            "🎫 Ticket Management",
            "👥 User Management",
            "📊 Analytics & Reports",
            "🔧 IT Support Configuration",
            "💬 Real-time Chat",
            "🔔 Push Notifications",
            "🔐 JWT Authentication",
            "📱 Mobile API Support"
        ],

        # 🌐 CORS Information
        "cors_enabled": True,
        "cors_origins": "Configured for frontend access",

        # 📞 Support
        "support": {
            "email": "<EMAIL>",
            "documentation": f"{base_url}api/docs/",
            "company": "Nexware Global"
        }
    }

    # Check if request wants JSON (API clients)
    if (request.headers.get('Accept', '').startswith('application/json') or
        request.GET.get('format') == 'json'):
        return JsonResponse(context)

    # Return HTML template for browser users
    return render(request, 'api_home.html', context)
