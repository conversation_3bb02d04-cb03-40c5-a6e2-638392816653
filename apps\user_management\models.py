from django.db import models
from django.forms import ValidationError
from django.utils.timezone import now
from rest_framework.authtoken.models import Token
from django.contrib.auth.models import AbstractUser
class Role(models.Model):
    role_id = models.CharField(max_length=10, unique=True, primary_key=True)
    role_name = models.CharField(max_length=50)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)
    created_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, related_name='roles_created_by', to_field='id')
    updated_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, related_name='roles_updated_by', to_field='id')

    def save(self, *args, **kwargs):
        if not self.role_id:  # Only generate ID if it doesn't already exist
            last_role = Role.objects.order_by('-created_at').first()
            last_id = int(last_role.role_id[1:]) if last_role else 0  # Extract or start from 0
            self.role_id = f"R{last_id + 1:03d}"  # Increment and format with leading zeros
        super(Role, self).save(*args, **kwargs)

    def __str__(self):
        return self.role_name



class Location(models.Model):
    location_id = models.CharField(max_length=10, unique=True, primary_key=True)
    location_name = models.CharField(max_length=50)
    created_at = models.DateTimeField(default=now)
    updated_at = models.DateTimeField(default=now)
    created_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, related_name='location_created_by', to_field='id')
    updated_by = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, related_name='location_updated_by', to_field='id')

    def save(self, *args, **kwargs):
        if not self.location_id:  # Only generate ID if it doesn't already exist
            last_location = Location.objects.order_by('-created_at').first()
            last_id = int(last_location.location_id[1:]) if last_location else 0  # Extract or start from 0
            self.location_id = f"L{last_id + 1:03d}"  # Increment and format with leading zeros
        super(Location, self).save(*args, **kwargs)

    def __str__(self):
        return self.location_name


class User(AbstractUser):
    id = models.BigAutoField(primary_key=True, unique=True)
    employee_id = models.CharField(max_length=20, unique=True)
    first_name = models.CharField(max_length=50)
    last_name = models.CharField(max_length=50)
    email = models.EmailField(unique=True)
    password = models.CharField(max_length=255)  # Ensure this is hashed before saving
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='users')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='users')
    is_active = models.BooleanField(default=True)
    is_deleted = models.BooleanField(default=False)
    profile_pic = models.ImageField(upload_to='uploads/profilepic/' , null=True, blank=True, default="")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
    'self',
    on_delete=models.SET_NULL,
    related_name='users_created_by',
    null=True,
    to_field='id'
)
    updated_by = models.ForeignKey('self', on_delete=models.SET_NULL, related_name='users_updated_by',null=True, to_field='id')
    username = None
    fcm_token = models.TextField(blank=True, null=True)
    new_user = models.BooleanField(default=True, blank=False)
    last_login = models.DateTimeField(blank=True, null=True)  # Add the last_login field
    date_joined = models.DateTimeField(auto_now_add=True)  # Add the date_joined field
    is_staff = models.BooleanField(default=False)  # Add the is_staff field
    is_superuser = models.BooleanField(default=False)  # Add the is_superuser field
    REQUIRED_FIELDS = ['first_name', 'last_name', 'employee_id']
    USERNAME_FIELD = 'email'
    last_used_reset_token = models.CharField(max_length=150, blank=True, null=True)

    def soft_delete(self):
        """Marks the user as deleted without actually removing them from the database."""
        self.is_deleted = True
        self.save()

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

class Project(models.Model):
    id = models.BigAutoField(primary_key=True, unique=True)
    project_name = models.CharField(max_length=100, unique=True)
    project_status = models.BooleanField(default=True)
    project_start_date = models.DateField()
    project_end_date = models.DateField(null=True, blank=True)
    project_manager = models.ForeignKey(User, on_delete=models.PROTECT, null=False, related_name='project_manager', to_field='id')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='projects_created_by', null=True, to_field='id')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='projects_updated_by', null=True, to_field='id')
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey(User, on_delete=models.SET_NULL, related_name='projects_deleted_by', null=True, blank=True, to_field='id')

    def save(self, *args, **kwargs):
        # If end date is set, mark project and related mappings as inactive
        if self.project_end_date:
            self.project_status = False  # Mark project as Inactive
            super().save(*args, **kwargs)

            # Set all related project mappings to inactive
            ProjectMapping.objects.filter(project=self).update(status=False, removed_at=now())
        else:
            super().save(*args, **kwargs)
    
    def soft_delete(self, deleted_by=None):
        """Marks the project as deleted without actually removing it from the database."""
        self.is_deleted = True
        self.deleted_at = now()
        if deleted_by:
            self.deleted_by = deleted_by
        self.save()

    def __str__(self):
        return self.project_name

class ProjectMapping(models.Model):
    id = models.BigAutoField(primary_key=True, unique=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='project_mapping', to_field='id')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='project_mapping', to_field='id')
    status = models.BooleanField(default=True)
    assigned_at = models.DateTimeField(auto_now_add=True)
    removed_at = models.DateTimeField(null=True, blank=True)
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assignments_created_by', to_field='id'
    )
    removed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assignments_removed_by', to_field='id'
    )

    def deactivate(self, removed_by=None):
        """Soft-deletes the mapping by setting status to inactive."""
        self.status = False  # Use False for BooleanField
        self.removed_at = now()
        if removed_by:
            self.removed_by = removed_by
        self.save()

    def activate(self, assigned_by=None):
        """Reactivates the mapping."""
        self.status = True  # Use True for BooleanField
        self.removed_at = None
        if assigned_by:
            self.assigned_by = assigned_by
        self.save()

    class Meta:
        unique_together = ('project', 'user')

    def __str__(self):
        return f"{self.project.project_name} - {self.user.first_name}"
