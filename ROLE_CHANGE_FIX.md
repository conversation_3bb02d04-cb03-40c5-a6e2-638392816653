# Role Change and Force Logout Fix

## Issues Identified

### 1. WebSocket Disconnection During Role Change

**Problem**: When changing a user's role, the WebSocket connection was getting disconnected before the force logout message could be received.

**Root Cause**: The `AuthMiddlewareStack` in the ASGI configuration was causing WebSocket disconnections when user authentication state changed during role updates.

**Fix**: Created a custom WebSocket middleware that bypasses authentication for force logout WebSocket connections.

### 2. WebSocket Message Type Mismatch

**Problem**: The backend was sending `{"type": "force.logout"}` but the WebSocket consumer was expecting `force_logout`.

**Root Cause**: Django Channels converts message types with dots to underscores, so `force.logout` becomes `force_logout` in the consumer method.

**Fix**: Changed the message type from `"force.logout"` to `"force_logout"` in the user update view.

### 2. Role Comparison Logic Issue

**Problem**: The role comparison was inconsistent because:

- `old_role` is a string (role_id from database)
- `new_role` could be various types (string, int, None) from the request data

**Fix**: Added proper type conversion and comparison logic:

```python
new_role_str = str(new_role) if new_role else None
if old_role != new_role_str:
    # Trigger force logout
```

### 3. Insufficient Error Handling

**Problem**: No error handling for WebSocket message sending, making debugging difficult.

**Fix**: Added comprehensive error handling and logging:

```python
try:
    async_to_sync(channel_layer.group_send)(
        group_name,
        {"type": "force_logout"}
    )
    print("✅ Force logout message sent successfully")
except Exception as e:
    print(f"❌ Error sending force logout: {e}")
```

### 4. Poor WebSocket Connection Management

**Problem**: No tracking of active WebSocket connections, making it hard to debug.

**Fix**: Added connection tracking and better logging in the WebSocket consumer.

## Files Modified

### 1. `apps/user_management/views.py`

- Fixed WebSocket message type from `"force.logout"` to `"force_logout"`
- Added proper role comparison logic
- Added comprehensive error handling and logging
- Added debug prints for troubleshooting

### 2. `apps/websocket/consumers.py`

- Improved connection tracking with `active_connections` dictionary
- Added better error handling in `force_logout` method
- Enhanced logging for debugging
- Fixed timestamp generation in logout message
- Added detailed disconnect reason logging
- Enhanced force logout message with role change details

### 3. `apps/websocket/middleware.py` (NEW)

- Created custom WebSocket middleware to handle authentication differently for force logout connections
- Bypasses authentication requirements for force logout WebSocket to prevent disconnections

### 4. `ticketing_system/asgi.py`

- Updated to use custom WebSocket middleware instead of standard AuthMiddlewareStack
- Prevents authentication-related disconnections during role changes

## How It Works Now

### 1. Role Change Detection

When a user's role is updated:

1. The system compares the old role ID with the new role ID
2. If they're different, it triggers a force logout
3. Detailed logging shows the comparison process

### 2. Force Logout Process

1. Backend sends a WebSocket message to the user's group: `user_{user_id}`
2. If the user is connected to the WebSocket, they receive the logout message
3. Frontend should handle the logout message and redirect to login

### 3. WebSocket Message Format

The frontend receives:

```json
{
  "action": "logout",
  "message": "Your role has been changed. Please log in again.",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Testing the Fix

### 1. Run the Test Script

```bash
python test_role_change.py
```

### 2. Manual Testing Steps

1. **Connect to WebSocket**: Ensure your frontend connects to:

   ```
   ws://localhost:8080/ws/force-logout/{user_id}/
   ```

2. **Change User Role**: Use the API to update a user's role:

   ```bash
   PUT /api/users/update/{user_id}
   {
       "role": "R002",
       "updated_by": 1
   }
   ```

3. **Check Logs**: Look for these messages in the Django console:

   ```
   🔄 Role changed from R001 to R002 for user 123
   📣 Sending force logout to: user_123
   ✅ Force logout message sent successfully
   ```

4. **Frontend Handling**: Ensure your frontend handles the WebSocket message:
   ```javascript
   websocket.onmessage = function (event) {
     const data = JSON.parse(event.data);
     if (data.action === "logout") {
       // Clear user session and redirect to login
       localStorage.removeItem("token");
       window.location.href = "/login";
     }
   };
   ```

## Common Issues and Solutions

### Issue 1: User Not Receiving Logout Message

**Possible Causes**:

- User not connected to WebSocket
- WebSocket connection dropped
- Frontend not handling the message

**Solutions**:

- Check WebSocket connection status
- Implement reconnection logic
- Add proper message handling

### Issue 2: Role Change Not Detected

**Possible Causes**:

- Role data type mismatch
- API not sending correct role format

**Solutions**:

- Check the role data format in the API request
- Ensure role is sent as the role_id (e.g., "R001")

### Issue 3: WebSocket Not Working

**Possible Causes**:

- Channel layer not configured
- Redis not running (if using Redis channel layer)

**Solutions**:

- Check Django Channels configuration
- Ensure Redis is running if using Redis channel layer
- Test with in-memory channel layer for development

## Frontend Implementation Example

```javascript
class ForceLogoutWebSocket {
  constructor(userId) {
    this.userId = userId;
    this.websocket = null;
    this.connect();
  }

  connect() {
    const wsUrl = `ws://localhost:8080/ws/force-logout/${this.userId}/`;
    this.websocket = new WebSocket(wsUrl);

    this.websocket.onopen = () => {
      console.log("Force logout WebSocket connected");
    };

    this.websocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.action === "logout") {
        this.handleForceLogout(data.message);
      }
    };

    this.websocket.onclose = () => {
      console.log("Force logout WebSocket disconnected");
      // Implement reconnection logic if needed
    };

    this.websocket.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
  }

  handleForceLogout(message) {
    alert(message || "Your session has been terminated. Please log in again.");

    // Clear user session
    localStorage.removeItem("token");
    localStorage.removeItem("user");

    // Redirect to login
    window.location.href = "/login";
  }

  disconnect() {
    if (this.websocket) {
      this.websocket.close();
    }
  }
}

// Usage
const userId = getCurrentUserId(); // Your function to get current user ID
const forceLogoutWS = new ForceLogoutWebSocket(userId);
```

## Summary

The force logout functionality should now work correctly when:

1. A user's role is changed
2. The user is connected to the WebSocket
3. The frontend properly handles the logout message

The fixes ensure proper role change detection, reliable WebSocket communication, and comprehensive error handling for easier debugging.
