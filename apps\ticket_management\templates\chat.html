<!-- <!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebSocket Chat</title>
  </head>
  <body>
    <h1>WebSocket Chat</h1> -->

<!-- Display messages -->
<!-- <ul id="messages"></ul> -->

<!-- Input field for sending messages -->
<!-- <input type="text" id="messageInput" placeholder="Type a message..." />
    <button id="sendMessageBtn">Send</button> -->

<!-- <script>
      const socket = new WebSocket(
        "ws://" + window.location.host + "/ws/chat/"
      ); -->

// Handle WebSocket connection open
<!-- socket.onopen = function (event) {
        console.log("WebSocket connection opened");
      }; -->

// Handle incoming messages
<!-- socket.onmessage = function (event) {
        const data = JSON.parse(event.data);
        const message = data.message;

        const messageList = document.getElementById("messages");
        const messageItem = document.createElement("li");
        messageItem.textContent = message;
        messageList.appendChild(messageItem);
      }; -->

// Handle WebSocket connection close
<!-- socket.onclose = function (event) {
        console.log("WebSocket connection closed");
      }; -->

// Send message when button is clicked
<!-- document.getElementById("sendMessageBtn").onclick = function () {
        const messageInput = document.getElementById("messageInput");
        const message = messageInput.value; -->

<!-- if (message) { -->
// Send message to the WebSocket
<!-- socket.send(JSON.stringify({ message: message }));
          messageInput.value = ""; // Clear input after sending
        }
      }; -->
<!-- </script>
  </body>
</html> -->
