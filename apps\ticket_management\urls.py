# from django_socketio import events 
from django.urls import path, include, re_path
from rest_framework.routers import DefaultRouter

from .views import  ChatListViewSet,NotificationView, TicketApprovalStatusListView,ClearUserNotificationsView, TicketPriorityListView, TicketStatusTrackingView, TicketUpdatePartialView, TicketView, TicketViewSet, UpdateNotificationsView, UserNotificationView, create_feedback_value, get_feedback_values, update_fcm_token
from django.conf.urls.static import static
from django.conf import settings
from .views import FeedbackListCreateView,TicketDetailView,TicketStatusListView
from .views import (
    # TicketChatViewSet,
    TicketViewSet,
    TicketUpdatePartialView,
    TicketDetailView,
    TicketAssigneeView,
    FeedbackListCreateView,
    
)
from django.conf.urls.static import static
from django.conf import settings
# from . import views
# from django.conf.urls import url

router = DefaultRouter()
router.register(r'tickets', TicketViewSet, basename='ticket')
# router.register(r'ticketchats', TicketChatViewSet, basename='ticketchat')
router.register(r'chats', ChatListViewSet)




urlpatterns = [
    path('api/', include(router.urls)),
    path('assignee-ticket/<int:pk>/update/', TicketUpdatePartialView.as_view(), name='ticket-update-partial'),
    path('tickets/<int:pk>/detail/', TicketDetailView.as_view(), name='ticket-detail'),
    path('assignee-update/',TicketAssigneeView.as_view(),name='ticket-assignees'),
    path('feedbacks/', FeedbackListCreateView.as_view(), name='feedback-list-create'),
    path('feedback-values/', get_feedback_values, name='feedback-values'),
    # path('feedbacks/<int:pk>/', FeedbackDetailView.as_view(), name='feedback-detail'),
    path('feedback-values/create', create_feedback_value, name='feedback-detail'),
    path('ticket/<int:ticket_id>/', TicketDetailView.as_view(), name='ticket-detail'),
    path('tickets-all/', TicketView.as_view(), name='ticket-filter-sort'),
    path('ticket-approvalstatus/',TicketApprovalStatusListView.as_view(), name='ticket approval-status'),
    path('ticket-approvalstatus/<int:ticket_id>/', TicketApprovalStatusListView.as_view(), name='ticket-approval-update'),
    path('ticket-status/',TicketStatusListView.as_view(), name='ticket status'),
    path("status-tracking/<int:ticket_id>/", TicketStatusTrackingView.as_view(), name="ticket_status_tracking"),
    path("update-fcm-token/", update_fcm_token, name="update_fcm_token"),
    # path("save-fcm-token/", SaveFCMTokenView.as_view(), name="save_fcm_token"),
    # path('api/ticketchat/<int:pk>/', TicketChatViewSet.as_view({'get': 'retrieve'}), name='ticket-chat'),
    path("tickets-priority/", TicketPriorityListView.as_view(), name="tickets-priority"),
    path('notifications/', NotificationView.as_view(), name='all-notifications'),
    path('notifications/<int:user_id>/', UserNotificationView.as_view(), name='user-notifications'),
    path('notifications/clear/<int:user_id>', ClearUserNotificationsView.as_view(), name='clear-notifications'),
    path('notifications/update/', UpdateNotificationsView.as_view(), name='update-notifications'),
    # path("comments/", get_comments, name="get_comments"),


]

# Add static files URL configuration if applicable
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

