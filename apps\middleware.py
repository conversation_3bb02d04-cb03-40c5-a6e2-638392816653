"""
Custom middleware to fix CORS headers issues.
"""
import logging
from django.http import HttpResponse
from django.conf import settings

logger = logging.getLogger(__name__)

class RemoveDuplicateCORSHeadersMiddleware:
    """
    Middleware to completely handle CORS and security headers.

    This middleware:
    1. Completely handles OPTIONS requests for CORS preflight
    2. Ensures only one set of CORS headers is sent in the response
    3. Adds security headers to all responses
    """

    def __init__(self, get_response):
        self.get_response = get_response

        # Get allowed origins from settings
        self.allowed_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', [
            "http://internal-project.nexware-global.com:9018",  # Frontend
            "http://internal-project.nexware-global.com:9019",  # Backend
            "https://ticket.nexware-global.com:9048",  # Prod Frontend
            "https://ticket.nexware-global.com:9049",  # Prod Backend
            "https://internal-project.nexware-global.com:8001",  # Prod websocket
            "http://localhost:3000",
            "http://localhost:4173",
            "http://localhost:5173",
            "http://localhost:8000",  # websocket
            "null",  # For file:// URLs
        ])

        # Check if we should allow all origins
        self.allow_all_origins = getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', False)

        # Check if we should allow null origin (for file:// URLs)
        self.allow_null_origin = getattr(settings, 'CORS_ALLOW_NULL_ORIGIN', False)

        logger.debug(f"CORS middleware initialized with allowed origins: {self.allowed_origins}")
        logger.debug(f"Allow all origins: {self.allow_all_origins}")
        logger.debug(f"Allow null origin: {self.allow_null_origin}")

    def __call__(self, request):
        # Get the origin from the request
        origin = request.headers.get('Origin')

        # Debug logging
        logger.debug(f"Request from origin: {origin}")
        logger.debug(f"Allowed origins: {self.allowed_origins}")
        logger.debug(f"Request method: {request.method}")
        logger.debug(f"Request path: {request.path}")
        logger.debug(f"Is secure: {request.is_secure()}")

        # For OPTIONS requests (preflight), handle directly and return
        if request.method == 'OPTIONS' and origin:
            # Check if origin is allowed
            is_allowed = self._is_origin_allowed(origin)

            if is_allowed:
                response = HttpResponse()

                # Add CORS headers
                self._add_cors_headers(response, origin)

                # Add security headers
                self._add_security_headers(response, request.is_secure())

                logger.debug(f"Returning OPTIONS response with CORS headers for origin: {origin}")
                return response
            else:
                logger.warning(f"Blocked OPTIONS request from non-allowed origin: {origin}")
                return HttpResponse(status=403)  # Forbidden if origin not allowed

        # For non-OPTIONS requests, process normally
        response = self.get_response(request)

        # Add CORS headers for all responses if origin is present
        if origin:
            # Check if origin is allowed
            is_allowed = self._is_origin_allowed(origin)

            if is_allowed:
                # First, remove any existing CORS headers to prevent duplicates
                self._remove_cors_headers(response)

                # Then add our own CORS headers
                self._add_cors_headers(response, origin)

                logger.debug(f"Added CORS headers to response for origin: {origin}")
            else:
                logger.warning(f"Not adding CORS headers for non-allowed origin: {origin}")

        # Add security headers to all responses
        self._add_security_headers(response, request.is_secure())

        return response

    def _is_origin_allowed(self, origin):
        """Check if the origin is allowed."""
        if self.allow_all_origins:
            return True

        if origin == "null" and self.allow_null_origin:
            return True

        return origin in self.allowed_origins

    def _add_cors_headers(self, response, origin):
        """Add CORS headers to the response."""
        response['Access-Control-Allow-Origin'] = origin
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS, PATCH'
        response['Access-Control-Allow-Headers'] = (
            'Authorization, Content-Type, X-Requested-With, '
            'Accept, Origin, X-CSRFToken, Bearer-Token, '
            'user-agent, cache-control, pragma, accept-encoding, dnt'
        )
        response['Access-Control-Allow-Credentials'] = 'true'
        response['Access-Control-Max-Age'] = '86400'  # 24 hours

    def _add_security_headers(self, response, is_secure):
        """Add security headers to the response."""
        # Set Cross-Origin-Opener-Policy
        # Note: This header is only respected in secure contexts (HTTPS)
        response['Cross-Origin-Opener-Policy'] = 'same-origin'

        # Set X-Content-Type-Options to prevent MIME type sniffing
        response['X-Content-Type-Options'] = 'nosniff'

        # Set Referrer-Policy
        response['Referrer-Policy'] = 'same-origin'

        # Set X-Frame-Options to prevent clickjacking
        response['X-Frame-Options'] = 'DENY'

        # Only set HSTS header in secure contexts
        if is_secure:
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

            # These headers are also only effective in secure contexts
            response['Cross-Origin-Embedder-Policy'] = 'require-corp'
            response['Cross-Origin-Resource-Policy'] = 'same-origin'

        # Log warning if trying to use security headers in non-secure context
        elif not is_secure:
            logger.warning("Security headers may not be effective in non-secure context (HTTP)")

    def _remove_cors_headers(self, response):
        """Remove all CORS-related headers from the response."""
        cors_headers = [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Credentials',
            'Access-Control-Allow-Methods',
            'Access-Control-Allow-Headers',
            'Access-Control-Max-Age',
            'Access-Control-Expose-Headers',
        ]

        for header in cors_headers:
            if header in response:
                del response[header]
