# Ticketing System API Documentation

## Overview

This document provides comprehensive documentation for the Ticketing System API. The API allows clients to interact with the ticketing system, manage users, handle authentication, and generate reports.

## Base URL

```
http://internal-project.nexware-global.com:9019/api/
```

## API Versioning

The API does not use explicit versioning in the URL. Any breaking changes will be communicated to API consumers.

## Response Format

All responses are returned in JSON format. Successful responses typically include the requested data, while error responses include an error message and sometimes additional details.

### Success Response Format

```json
{
  "status": "success",
  "data": { ... }
}
```

### Error Response Format

```json
{
  "status": "error",
  "message": "Error message",
  "details": { ... }
}
```

## Authentication Headers

For authenticated endpoints, include the JWT token in the Authorization header:

```
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

## Authentication

Most API endpoints require authentication. The API uses JWT (JSON Web Token) for authentication.

### Authentication Endpoints

#### Login

Authenticates a user and returns a JWT token.

- **URL**: `/login`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Success Response**:
  ```json
  {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "Admin"
    }
  }
  ```

#### Refresh Token

Refreshes an expired JWT token.

- **URL**: `/token/refresh/`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
  ```
- **Success Response**:
  ```json
  {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
  ```

#### Logout

Blacklists a refresh token, effectively logging out the user.

- **URL**: `/token/logout/`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
  ```
- **Success Response**:
  ```json
  {
    "detail": "Successfully logged out."
  }
  ```

### Password Management

#### Change Password

Changes the password for an authenticated user.

- **URL**: `/change-password/`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "old_password": "oldpassword123",
    "new_password": "newpassword123"
  }
  ```
- **Success Response**:
  ```json
  {
    "message": "Password changed successfully"
  }
  ```

#### Forgot Password Request

Initiates the password reset process.

- **URL**: `/forgot-password-request`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```
- **Success Response**:
  ```json
  {
    "message": "Password reset email sent"
  }
  ```

#### Reset Password

Resets the password using a token.

- **URL**: `/password-reset-confirm`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "token": "reset-token",
    "user_id": 1,
    "password": "newpassword123"
  }
  ```
- **Success Response**:
  ```json
  {
    "message": "Password reset successful"
  }
  ```

## Ticket Management

### Tickets

#### List All Tickets

Returns a list of all tickets.

- **URL**: `/api/tickets/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**:
  - `page`: Page number for pagination
  - `page_size`: Number of items per page
- **Success Response**:
  ```json
  {
    "count": 100,
    "next": "http://example.com/api/tickets/?page=2",
    "previous": null,
    "results": [
      {
        "id": 1,
        "title": "Issue with login",
        "description": "Unable to login to the system",
        "status": "Open",
        "priority": "High",
        "created_at": "2023-01-01T12:00:00Z",
        "updated_at": "2023-01-02T12:00:00Z",
        "assignee": {
          "id": 2,
          "name": "Technician Name"
        },
        "creator": {
          "id": 1,
          "name": "User Name"
        }
      }
    ]
  }
  ```

#### Create Ticket

Creates a new ticket.

- **URL**: `/api/tickets/`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "title": "Issue with login",
    "description": "Unable to login to the system",
    "priority": "High",
    "category": 1
  }
  ```
- **Success Response**:
  ```json
  {
    "id": 1,
    "title": "Issue with login",
    "description": "Unable to login to the system",
    "status": "Open",
    "priority": "High",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-01T12:00:00Z",
    "assignee": null,
    "creator": {
      "id": 1,
      "name": "User Name"
    }
  }
  ```

#### Get Ticket Details

Returns details for a specific ticket.

- **URL**: `/api/tickets/{id}/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  {
    "id": 1,
    "title": "Issue with login",
    "description": "Unable to login to the system",
    "status": "Open",
    "priority": "High",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-02T12:00:00Z",
    "assignee": {
      "id": 2,
      "name": "Technician Name"
    },
    "creator": {
      "id": 1,
      "name": "User Name"
    },
    "category": {
      "id": 1,
      "name": "Login Issues"
    }
  }
  ```

#### Update Ticket

Updates a specific ticket.

- **URL**: `/api/tickets/{id}/`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "title": "Updated issue with login",
    "description": "Still unable to login to the system",
    "status": "In Progress",
    "priority": "Critical"
  }
  ```
- **Success Response**:
  ```json
  {
    "id": 1,
    "title": "Updated issue with login",
    "description": "Still unable to login to the system",
    "status": "In Progress",
    "priority": "Critical",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-03T12:00:00Z",
    "assignee": {
      "id": 2,
      "name": "Technician Name"
    },
    "creator": {
      "id": 1,
      "name": "User Name"
    }
  }
  ```

#### Partial Update Ticket

Updates specific fields of a ticket.

- **URL**: `/assignee-ticket/{id}/update/`
- **Method**: `PATCH`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "status": "Resolved"
  }
  ```
- **Success Response**:
  ```json
  {
    "id": 1,
    "title": "Updated issue with login",
    "description": "Still unable to login to the system",
    "status": "Resolved",
    "priority": "Critical",
    "created_at": "2023-01-01T12:00:00Z",
    "updated_at": "2023-01-04T12:00:00Z",
    "assignee": {
      "id": 2,
      "name": "Technician Name"
    },
    "creator": {
      "id": 1,
      "name": "User Name"
    }
  }
  ```

### Ticket Status and Tracking

#### Get Ticket Status List

Returns a list of all possible ticket statuses.

- **URL**: `/ticket-status/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "id": 1,
      "name": "Open"
    },
    {
      "id": 2,
      "name": "In Progress"
    },
    {
      "id": 3,
      "name": "Resolved"
    },
    {
      "id": 4,
      "name": "Closed"
    }
  ]
  ```

#### Get Ticket Status Tracking

Returns the status history for a specific ticket.

- **URL**: `/status-tracking/{ticket_id}/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "id": 1,
      "ticket": 1,
      "status": "Open",
      "created_at": "2023-01-01T12:00:00Z",
      "updated_by": {
        "id": 1,
        "name": "User Name"
      }
    },
    {
      "id": 2,
      "ticket": 1,
      "status": "In Progress",
      "created_at": "2023-01-02T12:00:00Z",
      "updated_by": {
        "id": 2,
        "name": "Technician Name"
      }
    }
  ]
  ```

### Ticket Feedback

#### List/Create Feedback

Lists all feedback or creates new feedback for a ticket.

- **URL**: `/feedbacks/`
- **Method**: `GET` (list) or `POST` (create)
- **Auth Required**: Yes
- **Request Body (POST)**:
  ```json
  {
    "ticket": 1,
    "rating": 5,
    "comment": "Great service!",
    "feedback_values": [1, 3]
  }
  ```
- **Success Response (GET)**:
  ```json
  [
    {
      "id": 1,
      "ticket": 1,
      "rating": 5,
      "comment": "Great service!",
      "created_at": "2023-01-05T12:00:00Z",
      "feedback_values": [
        {
          "id": 1,
          "name": "Quick Response"
        },
        {
          "id": 3,
          "name": "Professional"
        }
      ]
    }
  ]
  ```

#### Get Feedback Values

Returns a list of all possible feedback values.

- **URL**: `/feedback-values/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "id": 1,
      "name": "Quick Response"
    },
    {
      "id": 2,
      "name": "Thorough Solution"
    },
    {
      "id": 3,
      "name": "Professional"
    }
  ]
  ```

### Ticket Chat

#### Get Ticket Messages

Returns all chat messages for a specific ticket.

- **URL**: `/ticket/{ticket_id}/messages/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "id": 1,
      "ticket": 1,
      "sender": {
        "id": 1,
        "name": "User Name"
      },
      "message": "Any updates on this issue?",
      "created_at": "2023-01-02T14:00:00Z",
      "attachments": []
    },
    {
      "id": 2,
      "ticket": 1,
      "sender": {
        "id": 2,
        "name": "Technician Name"
      },
      "message": "Working on it now, will update soon.",
      "created_at": "2023-01-02T14:05:00Z",
      "attachments": []
    }
  ]
  ```

#### Upload Chat Attachment

Uploads an attachment for a chat message.

- **URL**: `/upload_chat_attachment/`
- **Method**: `POST`
- **Auth Required**: Yes
- **Content Type**: `multipart/form-data`
- **Request Body**:
  ```
  ticket_id: 1
  file: [binary file data]
  ```
- **Success Response**:
  ```json
  {
    "id": 1,
    "file_url": "/media/chat_attachments/file.pdf",
    "file_name": "file.pdf",
    "uploaded_at": "2023-01-02T14:10:00Z"
  }
  ```

## User Management

### Users

#### List All Users

Returns a list of all users.

- **URL**: `/users/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "id": 1,
      "name": "User Name",
      "email": "<EMAIL>",
      "role": {
        "id": 1,
        "name": "Admin"
      },
      "location": {
        "id": 1,
        "name": "Headquarters"
      },
      "is_active": true,
      "created_at": "2023-01-01T00:00:00Z"
    }
  ]
  ```

#### Create User

Creates a new user.

- **URL**: `/users/create`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "name": "New User",
    "email": "<EMAIL>",
    "password": "password123",
    "role_id": 2,
    "location_id": 1
  }
  ```
- **Success Response**:
  ```json
  {
    "id": 2,
    "name": "New User",
    "email": "<EMAIL>",
    "role": {
      "id": 2,
      "name": "Technician"
    },
    "location": {
      "id": 1,
      "name": "Headquarters"
    },
    "is_active": true,
    "created_at": "2023-01-02T00:00:00Z"
  }
  ```

#### Get User by ID

Returns details for a specific user.

- **URL**: `/users/{id}`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>",
    "role": {
      "id": 1,
      "name": "Admin"
    },
    "location": {
      "id": 1,
      "name": "Headquarters"
    },
    "is_active": true,
    "created_at": "2023-01-01T00:00:00Z"
  }
  ```

#### Update User

Updates a specific user.

- **URL**: `/users/update/{id}`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "name": "Updated User Name",
    "email": "<EMAIL>",
    "role_id": 3,
    "location_id": 2
  }
  ```
- **Success Response**:
  ```json
  {
    "id": 1,
    "name": "Updated User Name",
    "email": "<EMAIL>",
    "role": {
      "id": 3,
      "name": "Manager"
    },
    "location": {
      "id": 2,
      "name": "Branch Office"
    },
    "is_active": true,
    "created_at": "2023-01-01T00:00:00Z"
  }
  ```

#### Block/Unblock User

Blocks or unblocks a user.

- **URL**: `/users/block_unblock/{id}`
- **Method**: `PUT`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>",
    "is_active": false,
    "message": "User blocked successfully"
  }
  ```

### Roles

#### List All Roles

Returns a list of all roles.

- **URL**: `/users/role/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "id": 1,
      "name": "Admin",
      "description": "Administrator with full access"
    },
    {
      "id": 2,
      "name": "Technician",
      "description": "IT support technician"
    },
    {
      "id": 3,
      "name": "User",
      "description": "Regular user"
    }
  ]
  ```

#### Create Role

Creates a new role.

- **URL**: `/users/role/create`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "name": "Manager",
    "description": "Department manager"
  }
  ```
- **Success Response**:
  ```json
  {
    "id": 4,
    "name": "Manager",
    "description": "Department manager"
  }
  ```

### Locations

#### List All Locations

Returns a list of all locations.

- **URL**: `/users/location/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "id": 1,
      "name": "Headquarters",
      "address": "123 Main St, City"
    },
    {
      "id": 2,
      "name": "Branch Office",
      "address": "456 Side St, Town"
    }
  ]
  ```

#### Create Location

Creates a new location.

- **URL**: `/users/location/create`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "name": "New Office",
    "address": "789 New St, Village"
  }
  ```
- **Success Response**:
  ```json
  {
    "id": 3,
    "name": "New Office",
    "address": "789 New St, Village"
  }
  ```

## Report Analytics

### Ticket Status Reports

#### Get Ticket Status Count

Returns the count of tickets by status.

- **URL**: `/report/tickets/status`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  {
    "open": 10,
    "in_progress": 5,
    "resolved": 15,
    "closed": 20
  }
  ```

#### Get Yearly Report

Returns ticket statistics by month for a specific year.

- **URL**: `/report/year_report`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**:
  - `year`: Year for the report (default: current year)
- **Success Response**:
  ```json
  {
    "year": 2023,
    "months": [
      {
        "month": "January",
        "open": 5,
        "in_progress": 2,
        "resolved": 8,
        "closed": 10
      },
      {
        "month": "February",
        "open": 7,
        "in_progress": 3,
        "resolved": 6,
        "closed": 9
      }
    ]
  }
  ```

#### Get Location Report

Returns ticket statistics by location.

- **URL**: `/report/location/`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "location": "Headquarters",
      "open": 5,
      "in_progress": 2,
      "resolved": 8,
      "closed": 10
    },
    {
      "location": "Branch Office",
      "open": 7,
      "in_progress": 3,
      "resolved": 6,
      "closed": 9
    }
  ]
  ```

#### Get Category Report

Returns ticket statistics by category.

- **URL**: `/report/category_report`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "category": "Hardware Issues",
      "count": 15
    },
    {
      "category": "Software Issues",
      "count": 25
    },
    {
      "category": "Network Issues",
      "count": 10
    }
  ]
  ```

### Technician Reports

#### Get Technician Status

Returns ticket statistics by technician.

- **URL**: `/report/technician`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "technician": "Technician Name",
      "open": 3,
      "in_progress": 2,
      "resolved": 10,
      "closed": 15
    }
  ]
  ```

#### Get Technician Feedback

Returns feedback statistics for technicians.

- **URL**: `/report/feedback/technician`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**:
  ```json
  [
    {
      "technician": "Technician Name",
      "average_rating": 4.5,
      "feedback_count": 20
    }
  ]
  ```

## Error Codes

The API uses standard HTTP status codes to indicate the success or failure of a request:

- `200 OK`: The request was successful
- `201 Created`: The resource was successfully created
- `400 Bad Request`: The request was invalid or cannot be served
- `401 Unauthorized`: Authentication is required and has failed or has not been provided
- `403 Forbidden`: The authenticated user does not have permission to access the requested resource
- `404 Not Found`: The requested resource does not exist
- `500 Internal Server Error`: An error occurred on the server

## Rate Limiting

The API implements rate limiting to prevent abuse. The current limits are:

- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated users

When a rate limit is exceeded, the API will return a `429 Too Many Requests` status code.