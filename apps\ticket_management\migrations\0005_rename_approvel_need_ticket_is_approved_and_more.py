# Generated by Django 5.1.5 on 2025-02-05 12:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ticket_management', '0004_ticket_approvel_need'),
        ('user_management', '0002_remove_user_reporting_to'),
    ]

    operations = [
        migrations.RenameField(
            model_name='ticket',
            old_name='approvel_need',
            new_name='is_approved',
        ),
        migrations.AlterField(
            model_name='ticket',
            name='approvel_status',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='ticket_management.approvalstatus'),
        ),
        migrations.CreateModel(
            name='StatusTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_user', to='user_management.user')),
                ('current_status', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='status_tracking', to='ticket_management.status')),
                ('ticket', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ticket_status_tracking', to='ticket_management.ticket')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_user', to='user_management.user')),
            ],
        ),
    ]
