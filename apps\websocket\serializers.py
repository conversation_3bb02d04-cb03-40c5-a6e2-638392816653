from rest_framework import serializers

from apps.ticket_management.serializers import TicketDocumentSerializer
from .models import TicketChatMessage

class TicketChatMessageSerializer(serializers.ModelSerializer):
    attachments = TicketDocumentSerializer(many=True, read_only=True)
    sender_name = serializers.SerializerMethodField()

    class Meta:
        model = TicketChatMessage
        fields = ["id", "ticket", "sender_name", "message", "timestamp","attachments"]

    def get_sender_name(self, obj):
        """Retrieve sender's full name safely."""
        if obj.sender:  # Ensure sender exists
            return f"{obj.sender.first_name} {obj.sender.last_name}".strip()
        return "Unknown"
